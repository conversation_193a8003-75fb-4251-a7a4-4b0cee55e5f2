{"version": 3, "file": "VRMLLoader.cjs", "sources": ["../../src/loaders/VRMLLoader.js"], "sourcesContent": ["import {\n  BackSide,\n  BoxGeometry,\n  BufferAttribute,\n  BufferGeometry,\n  ClampToEdgeWrapping,\n  Color,\n  ConeGeometry,\n  CylinderGeometry,\n  DataTexture,\n  DoubleSide,\n  FileLoader,\n  Float32BufferAttribute,\n  FrontSide,\n  Group,\n  LineBasicMaterial,\n  LineSegments,\n  Loader,\n  LoaderUtils,\n  Mesh,\n  MeshBasicMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Points,\n  PointsMaterial,\n  Quaternion,\n  RepeatWrapping,\n  Scene,\n  ShapeUtils,\n  SphereGeometry,\n  TextureLoader,\n  Vector2,\n  Vector3,\n} from 'three'\nimport { Lexer, CstParser, createToken } from '../libs/chevrotain'\n\nclass VRMLLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    const loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n    loader.load(\n      url,\n      function (text) {\n        try {\n          onLoad(scope.parse(text, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(data, path) {\n    const nodeMap = {}\n\n    function generateVRMLTree(data) {\n      // create lexer, parser and visitor\n\n      const tokenData = createTokens()\n\n      const lexer = new VRMLLexer(tokenData.tokens)\n      const parser = new VRMLParser(tokenData.tokenVocabulary)\n      const visitor = createVisitor(parser.getBaseCstVisitorConstructor())\n\n      // lexing\n\n      const lexingResult = lexer.lex(data)\n      parser.input = lexingResult.tokens\n\n      // parsing\n\n      const cstOutput = parser.vrml()\n\n      if (parser.errors.length > 0) {\n        console.error(parser.errors)\n\n        throw Error('THREE.VRMLLoader: Parsing errors detected.')\n      }\n\n      // actions\n\n      const ast = visitor.visit(cstOutput)\n\n      return ast\n    }\n\n    function createTokens() {\n      // from http://gun.teipir.gr/VRML-amgem/spec/part1/concepts.html#SyntaxBasics\n\n      const RouteIdentifier = createToken({\n        name: 'RouteIdentifier',\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*[\\.][^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/,\n      })\n      const Identifier = createToken({\n        name: 'Identifier',\n        pattern: /[^\\x30-\\x39\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d][^\\0-\\x20\\x22\\x27\\x23\\x2b\\x2c\\x2d\\x2e\\x5b\\x5d\\x5c\\x7b\\x7d]*/,\n        longer_alt: RouteIdentifier,\n      })\n\n      // from http://gun.teipir.gr/VRML-amgem/spec/part1/nodesRef.html\n\n      const nodeTypes = [\n        'Anchor',\n        'Billboard',\n        'Collision',\n        'Group',\n        'Transform', // grouping nodes\n        'Inline',\n        'LOD',\n        'Switch', // special groups\n        'AudioClip',\n        'DirectionalLight',\n        'PointLight',\n        'Script',\n        'Shape',\n        'Sound',\n        'SpotLight',\n        'WorldInfo', // common nodes\n        'CylinderSensor',\n        'PlaneSensor',\n        'ProximitySensor',\n        'SphereSensor',\n        'TimeSensor',\n        'TouchSensor',\n        'VisibilitySensor', // sensors\n        'Box',\n        'Cone',\n        'Cylinder',\n        'ElevationGrid',\n        'Extrusion',\n        'IndexedFaceSet',\n        'IndexedLineSet',\n        'PointSet',\n        'Sphere', // geometries\n        'Color',\n        'Coordinate',\n        'Normal',\n        'TextureCoordinate', // geometric properties\n        'Appearance',\n        'FontStyle',\n        'ImageTexture',\n        'Material',\n        'MovieTexture',\n        'PixelTexture',\n        'TextureTransform', // appearance\n        'ColorInterpolator',\n        'CoordinateInterpolator',\n        'NormalInterpolator',\n        'OrientationInterpolator',\n        'PositionInterpolator',\n        'ScalarInterpolator', // interpolators\n        'Background',\n        'Fog',\n        'NavigationInfo',\n        'Viewpoint', // bindable nodes\n        'Text', // Text must be placed at the end of the regex so there are no matches for TextureTransform and TextureCoordinate\n      ]\n\n      //\n\n      const Version = createToken({\n        name: 'Version',\n        pattern: /#VRML.*/,\n        longer_alt: Identifier,\n      })\n\n      const NodeName = createToken({\n        name: 'NodeName',\n        pattern: new RegExp(nodeTypes.join('|')),\n        longer_alt: Identifier,\n      })\n\n      const DEF = createToken({\n        name: 'DEF',\n        pattern: /DEF/,\n        longer_alt: Identifier,\n      })\n\n      const USE = createToken({\n        name: 'USE',\n        pattern: /USE/,\n        longer_alt: Identifier,\n      })\n\n      const ROUTE = createToken({\n        name: 'ROUTE',\n        pattern: /ROUTE/,\n        longer_alt: Identifier,\n      })\n\n      const TO = createToken({\n        name: 'TO',\n        pattern: /TO/,\n        longer_alt: Identifier,\n      })\n\n      //\n\n      const StringLiteral = createToken({\n        name: 'StringLiteral',\n        pattern: /\"(?:[^\\\\\"\\n\\r]|\\\\[bfnrtv\"\\\\/]|\\\\u[0-9a-fA-F][0-9a-fA-F][0-9a-fA-F][0-9a-fA-F])*\"/,\n      })\n      const HexLiteral = createToken({ name: 'HexLiteral', pattern: /0[xX][0-9a-fA-F]+/ })\n      const NumberLiteral = createToken({ name: 'NumberLiteral', pattern: /[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?/ })\n      const TrueLiteral = createToken({ name: 'TrueLiteral', pattern: /TRUE/ })\n      const FalseLiteral = createToken({ name: 'FalseLiteral', pattern: /FALSE/ })\n      const NullLiteral = createToken({ name: 'NullLiteral', pattern: /NULL/ })\n      const LSquare = createToken({ name: 'LSquare', pattern: /\\[/ })\n      const RSquare = createToken({ name: 'RSquare', pattern: /]/ })\n      const LCurly = createToken({ name: 'LCurly', pattern: /{/ })\n      const RCurly = createToken({ name: 'RCurly', pattern: /}/ })\n      const Comment = createToken({\n        name: 'Comment',\n        pattern: /#.*/,\n        group: Lexer.SKIPPED,\n      })\n\n      // commas, blanks, tabs, newlines and carriage returns are whitespace characters wherever they appear outside of string fields\n\n      const WhiteSpace = createToken({\n        name: 'WhiteSpace',\n        pattern: /[ ,\\s]/,\n        group: Lexer.SKIPPED,\n      })\n\n      const tokens = [\n        WhiteSpace,\n        // keywords appear before the Identifier\n        NodeName,\n        DEF,\n        USE,\n        ROUTE,\n        TO,\n        TrueLiteral,\n        FalseLiteral,\n        NullLiteral,\n        // the Identifier must appear after the keywords because all keywords are valid identifiers\n        Version,\n        Identifier,\n        RouteIdentifier,\n        StringLiteral,\n        HexLiteral,\n        NumberLiteral,\n        LSquare,\n        RSquare,\n        LCurly,\n        RCurly,\n        Comment,\n      ]\n\n      const tokenVocabulary = {}\n\n      for (let i = 0, l = tokens.length; i < l; i++) {\n        const token = tokens[i]\n\n        tokenVocabulary[token.name] = token\n      }\n\n      return { tokens: tokens, tokenVocabulary: tokenVocabulary }\n    }\n\n    function createVisitor(BaseVRMLVisitor) {\n      // the visitor is created dynmaically based on the given base class\n\n      function VRMLToASTVisitor() {\n        BaseVRMLVisitor.call(this)\n\n        this.validateVisitor()\n      }\n\n      VRMLToASTVisitor.prototype = Object.assign(Object.create(BaseVRMLVisitor.prototype), {\n        constructor: VRMLToASTVisitor,\n\n        vrml: function (ctx) {\n          const data = {\n            version: this.visit(ctx.version),\n            nodes: [],\n            routes: [],\n          }\n\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i]\n\n            data.nodes.push(this.visit(node))\n          }\n\n          if (ctx.route) {\n            for (let i = 0, l = ctx.route.length; i < l; i++) {\n              const route = ctx.route[i]\n\n              data.routes.push(this.visit(route))\n            }\n          }\n\n          return data\n        },\n\n        version: function (ctx) {\n          return ctx.Version[0].image\n        },\n\n        node: function (ctx) {\n          const data = {\n            name: ctx.NodeName[0].image,\n            fields: [],\n          }\n\n          if (ctx.field) {\n            for (let i = 0, l = ctx.field.length; i < l; i++) {\n              const field = ctx.field[i]\n\n              data.fields.push(this.visit(field))\n            }\n          }\n\n          // DEF\n\n          if (ctx.def) {\n            data.DEF = this.visit(ctx.def[0])\n          }\n\n          return data\n        },\n\n        field: function (ctx) {\n          const data = {\n            name: ctx.Identifier[0].image,\n            type: null,\n            values: null,\n          }\n\n          let result\n\n          // SFValue\n\n          if (ctx.singleFieldValue) {\n            result = this.visit(ctx.singleFieldValue[0])\n          }\n\n          // MFValue\n\n          if (ctx.multiFieldValue) {\n            result = this.visit(ctx.multiFieldValue[0])\n          }\n\n          data.type = result.type\n          data.values = result.values\n\n          return data\n        },\n\n        def: function (ctx) {\n          return (ctx.Identifier || ctx.NodeName)[0].image\n        },\n\n        use: function (ctx) {\n          return { USE: (ctx.Identifier || ctx.NodeName)[0].image }\n        },\n\n        singleFieldValue: function (ctx) {\n          return processField(this, ctx)\n        },\n\n        multiFieldValue: function (ctx) {\n          return processField(this, ctx)\n        },\n\n        route: function (ctx) {\n          const data = {\n            FROM: ctx.RouteIdentifier[0].image,\n            TO: ctx.RouteIdentifier[1].image,\n          }\n\n          return data\n        },\n      })\n\n      function processField(scope, ctx) {\n        const field = {\n          type: null,\n          values: [],\n        }\n\n        if (ctx.node) {\n          field.type = 'node'\n\n          for (let i = 0, l = ctx.node.length; i < l; i++) {\n            const node = ctx.node[i]\n\n            field.values.push(scope.visit(node))\n          }\n        }\n\n        if (ctx.use) {\n          field.type = 'use'\n\n          for (let i = 0, l = ctx.use.length; i < l; i++) {\n            const use = ctx.use[i]\n\n            field.values.push(scope.visit(use))\n          }\n        }\n\n        if (ctx.StringLiteral) {\n          field.type = 'string'\n\n          for (let i = 0, l = ctx.StringLiteral.length; i < l; i++) {\n            const stringLiteral = ctx.StringLiteral[i]\n\n            field.values.push(stringLiteral.image.replace(/'|\"/g, ''))\n          }\n        }\n\n        if (ctx.NumberLiteral) {\n          field.type = 'number'\n\n          for (let i = 0, l = ctx.NumberLiteral.length; i < l; i++) {\n            const numberLiteral = ctx.NumberLiteral[i]\n\n            field.values.push(parseFloat(numberLiteral.image))\n          }\n        }\n\n        if (ctx.HexLiteral) {\n          field.type = 'hex'\n\n          for (let i = 0, l = ctx.HexLiteral.length; i < l; i++) {\n            const hexLiteral = ctx.HexLiteral[i]\n\n            field.values.push(hexLiteral.image)\n          }\n        }\n\n        if (ctx.TrueLiteral) {\n          field.type = 'boolean'\n\n          for (let i = 0, l = ctx.TrueLiteral.length; i < l; i++) {\n            const trueLiteral = ctx.TrueLiteral[i]\n\n            if (trueLiteral.image === 'TRUE') field.values.push(true)\n          }\n        }\n\n        if (ctx.FalseLiteral) {\n          field.type = 'boolean'\n\n          for (let i = 0, l = ctx.FalseLiteral.length; i < l; i++) {\n            const falseLiteral = ctx.FalseLiteral[i]\n\n            if (falseLiteral.image === 'FALSE') field.values.push(false)\n          }\n        }\n\n        if (ctx.NullLiteral) {\n          field.type = 'null'\n\n          ctx.NullLiteral.forEach(function () {\n            field.values.push(null)\n          })\n        }\n\n        return field\n      }\n\n      return new VRMLToASTVisitor()\n    }\n\n    function parseTree(tree) {\n      // console.log( JSON.stringify( tree, null, 2 ) );\n\n      const nodes = tree.nodes\n      const scene = new Scene()\n\n      // first iteration: build nodemap based on DEF statements\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i]\n\n        buildNodeMap(node)\n      }\n\n      // second iteration: build nodes\n\n      for (let i = 0, l = nodes.length; i < l; i++) {\n        const node = nodes[i]\n        const object = getNode(node)\n\n        if (object instanceof Object3D) scene.add(object)\n\n        if (node.name === 'WorldInfo') scene.userData.worldInfo = object\n      }\n\n      return scene\n    }\n\n    function buildNodeMap(node) {\n      if (node.DEF) {\n        nodeMap[node.DEF] = node\n      }\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n\n        if (field.type === 'node') {\n          const fieldValues = field.values\n\n          for (let j = 0, jl = fieldValues.length; j < jl; j++) {\n            buildNodeMap(fieldValues[j])\n          }\n        }\n      }\n    }\n\n    function getNode(node) {\n      // handle case where a node refers to a different one\n\n      if (node.USE) {\n        return resolveUSE(node.USE)\n      }\n\n      if (node.build !== undefined) return node.build\n\n      node.build = buildNode(node)\n\n      return node.build\n    }\n\n    // node builder\n\n    function buildNode(node) {\n      const nodeName = node.name\n      let build\n\n      switch (nodeName) {\n        case 'Group':\n        case 'Transform':\n        case 'Collision':\n          build = buildGroupingNode(node)\n          break\n\n        case 'Background':\n          build = buildBackgroundNode(node)\n          break\n\n        case 'Shape':\n          build = buildShapeNode(node)\n          break\n\n        case 'Appearance':\n          build = buildAppearanceNode(node)\n          break\n\n        case 'Material':\n          build = buildMaterialNode(node)\n          break\n\n        case 'ImageTexture':\n          build = buildImageTextureNode(node)\n          break\n\n        case 'PixelTexture':\n          build = buildPixelTextureNode(node)\n          break\n\n        case 'TextureTransform':\n          build = buildTextureTransformNode(node)\n          break\n\n        case 'IndexedFaceSet':\n          build = buildIndexedFaceSetNode(node)\n          break\n\n        case 'IndexedLineSet':\n          build = buildIndexedLineSetNode(node)\n          break\n\n        case 'PointSet':\n          build = buildPointSetNode(node)\n          break\n\n        case 'Box':\n          build = buildBoxNode(node)\n          break\n\n        case 'Cone':\n          build = buildConeNode(node)\n          break\n\n        case 'Cylinder':\n          build = buildCylinderNode(node)\n          break\n\n        case 'Sphere':\n          build = buildSphereNode(node)\n          break\n\n        case 'ElevationGrid':\n          build = buildElevationGridNode(node)\n          break\n\n        case 'Extrusion':\n          build = buildExtrusionNode(node)\n          break\n\n        case 'Color':\n        case 'Coordinate':\n        case 'Normal':\n        case 'TextureCoordinate':\n          build = buildGeometricNode(node)\n          break\n\n        case 'WorldInfo':\n          build = buildWorldInfoNode(node)\n          break\n\n        case 'Anchor':\n        case 'Billboard':\n\n        case 'Inline':\n        case 'LOD':\n        case 'Switch':\n\n        case 'AudioClip':\n        case 'DirectionalLight':\n        case 'PointLight':\n        case 'Script':\n        case 'Sound':\n        case 'SpotLight':\n\n        case 'CylinderSensor':\n        case 'PlaneSensor':\n        case 'ProximitySensor':\n        case 'SphereSensor':\n        case 'TimeSensor':\n        case 'TouchSensor':\n        case 'VisibilitySensor':\n\n        case 'Text':\n\n        case 'FontStyle':\n        case 'MovieTexture':\n\n        case 'ColorInterpolator':\n        case 'CoordinateInterpolator':\n        case 'NormalInterpolator':\n        case 'OrientationInterpolator':\n        case 'PositionInterpolator':\n        case 'ScalarInterpolator':\n\n        case 'Fog':\n        case 'NavigationInfo':\n        case 'Viewpoint':\n          // node not supported yet\n          break\n\n        default:\n          console.warn('THREE.VRMLLoader: Unknown node:', nodeName)\n          break\n      }\n\n      if (build !== undefined && node.DEF !== undefined && build.hasOwnProperty('name') === true) {\n        build.name = node.DEF\n      }\n\n      return build\n    }\n\n    function buildGroupingNode(node) {\n      const object = new Group()\n\n      //\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bboxCenter':\n            // field not supported\n            break\n\n          case 'bboxSize':\n            // field not supported\n            break\n\n          case 'center':\n            // field not supported\n            break\n\n          case 'children':\n            parseFieldChildren(fieldValues, object)\n            break\n\n          case 'collide':\n            // field not supported\n            break\n\n          case 'rotation':\n            const axis = new Vector3(fieldValues[0], fieldValues[1], fieldValues[2]).normalize()\n            const angle = fieldValues[3]\n            object.quaternion.setFromAxisAngle(axis, angle)\n            break\n\n          case 'scale':\n            object.scale.set(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'scaleOrientation':\n            // field not supported\n            break\n\n          case 'translation':\n            object.position.set(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'proxy':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return object\n    }\n\n    function buildBackgroundNode(node) {\n      const group = new Group()\n\n      let groundAngle, groundColor\n      let skyAngle, skyColor\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'groundAngle':\n            groundAngle = fieldValues\n            break\n\n          case 'groundColor':\n            groundColor = fieldValues\n            break\n\n          case 'backUrl':\n            // field not supported\n            break\n\n          case 'bottomUrl':\n            // field not supported\n            break\n\n          case 'frontUrl':\n            // field not supported\n            break\n\n          case 'leftUrl':\n            // field not supported\n            break\n\n          case 'rightUrl':\n            // field not supported\n            break\n\n          case 'topUrl':\n            // field not supported\n            break\n\n          case 'skyAngle':\n            skyAngle = fieldValues\n            break\n\n          case 'skyColor':\n            skyColor = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const radius = 10000\n\n      // sky\n\n      if (skyColor) {\n        const skyGeometry = new SphereGeometry(radius, 32, 16)\n        const skyMaterial = new MeshBasicMaterial({ fog: false, side: BackSide, depthWrite: false, depthTest: false })\n\n        if (skyColor.length > 3) {\n          paintFaces(skyGeometry, radius, skyAngle, toColorArray(skyColor), true)\n          skyMaterial.vertexColors = true\n        } else {\n          skyMaterial.color.setRGB(skyColor[0], skyColor[1], skyColor[2])\n        }\n\n        const sky = new Mesh(skyGeometry, skyMaterial)\n        group.add(sky)\n      }\n\n      // ground\n\n      if (groundColor) {\n        if (groundColor.length > 0) {\n          const groundGeometry = new SphereGeometry(radius, 32, 16, 0, 2 * Math.PI, 0.5 * Math.PI, 1.5 * Math.PI)\n          const groundMaterial = new MeshBasicMaterial({\n            fog: false,\n            side: BackSide,\n            vertexColors: true,\n            depthWrite: false,\n            depthTest: false,\n          })\n\n          paintFaces(groundGeometry, radius, groundAngle, toColorArray(groundColor), false)\n\n          const ground = new Mesh(groundGeometry, groundMaterial)\n          group.add(ground)\n        }\n      }\n\n      // render background group first\n\n      group.renderOrder = -Infinity\n\n      return group\n    }\n\n    function buildShapeNode(node) {\n      const fields = node.fields\n\n      // if the appearance field is NULL or unspecified, lighting is off and the unlit object color is (0, 0, 0)\n\n      let material = new MeshBasicMaterial({ color: 0x000000 })\n      let geometry\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'appearance':\n            if (fieldValues[0] !== null) {\n              material = getNode(fieldValues[0])\n            }\n\n            break\n\n          case 'geometry':\n            if (fieldValues[0] !== null) {\n              geometry = getNode(fieldValues[0])\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // build 3D object\n\n      let object\n\n      if (geometry && geometry.attributes.position) {\n        const type = geometry._type\n\n        if (type === 'points') {\n          // points\n\n          const pointsMaterial = new PointsMaterial({ color: 0xffffff })\n\n          if (geometry.attributes.color !== undefined) {\n            pointsMaterial.vertexColors = true\n          } else {\n            // if the color field is NULL and there is a material defined for the appearance affecting this PointSet, then use the emissiveColor of the material to draw the points\n\n            if (material.isMeshPhongMaterial) {\n              pointsMaterial.color.copy(material.emissive)\n            }\n          }\n\n          object = new Points(geometry, pointsMaterial)\n        } else if (type === 'line') {\n          // lines\n\n          const lineMaterial = new LineBasicMaterial({ color: 0xffffff })\n\n          if (geometry.attributes.color !== undefined) {\n            lineMaterial.vertexColors = true\n          } else {\n            // if the color field is NULL and there is a material defined for the appearance affecting this IndexedLineSet, then use the emissiveColor of the material to draw the lines\n\n            if (material.isMeshPhongMaterial) {\n              lineMaterial.color.copy(material.emissive)\n            }\n          }\n\n          object = new LineSegments(geometry, lineMaterial)\n        } else {\n          // consider meshes\n\n          // check \"solid\" hint (it's placed in the geometry but affects the material)\n\n          if (geometry._solid !== undefined) {\n            material.side = geometry._solid ? FrontSide : DoubleSide\n          }\n\n          // check for vertex colors\n\n          if (geometry.attributes.color !== undefined) {\n            material.vertexColors = true\n          }\n\n          object = new Mesh(geometry, material)\n        }\n      } else {\n        object = new Object3D()\n\n        // if the geometry field is NULL or no vertices are defined the object is not drawn\n\n        object.visible = false\n      }\n\n      return object\n    }\n\n    function buildAppearanceNode(node) {\n      let material = new MeshPhongMaterial()\n      let transformData\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'material':\n            if (fieldValues[0] !== null) {\n              const materialData = getNode(fieldValues[0])\n\n              if (materialData.diffuseColor) material.color.copy(materialData.diffuseColor)\n              if (materialData.emissiveColor) material.emissive.copy(materialData.emissiveColor)\n              if (materialData.shininess) material.shininess = materialData.shininess\n              if (materialData.specularColor) material.specular.copy(materialData.specularColor)\n              if (materialData.transparency) material.opacity = 1 - materialData.transparency\n              if (materialData.transparency > 0) material.transparent = true\n            } else {\n              // if the material field is NULL or unspecified, lighting is off and the unlit object color is (0, 0, 0)\n\n              material = new MeshBasicMaterial({ color: 0x000000 })\n            }\n\n            break\n\n          case 'texture':\n            const textureNode = fieldValues[0]\n            if (textureNode !== null) {\n              if (textureNode.name === 'ImageTexture' || textureNode.name === 'PixelTexture') {\n                material.map = getNode(textureNode)\n              } else {\n                // MovieTexture not supported yet\n              }\n            }\n\n            break\n\n          case 'textureTransform':\n            if (fieldValues[0] !== null) {\n              transformData = getNode(fieldValues[0])\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // only apply texture transform data if a texture was defined\n\n      if (material.map) {\n        // respect VRML lighting model\n\n        if (material.map.__type) {\n          switch (material.map.__type) {\n            case TEXTURE_TYPE.INTENSITY_ALPHA:\n              material.opacity = 1 // ignore transparency\n              break\n\n            case TEXTURE_TYPE.RGB:\n              material.color.set(0xffffff) // ignore material color\n              break\n\n            case TEXTURE_TYPE.RGBA:\n              material.color.set(0xffffff) // ignore material color\n              material.opacity = 1 // ignore transparency\n              break\n\n            default:\n          }\n\n          delete material.map.__type\n        }\n\n        // apply texture transform\n\n        if (transformData) {\n          material.map.center.copy(transformData.center)\n          material.map.rotation = transformData.rotation\n          material.map.repeat.copy(transformData.scale)\n          material.map.offset.copy(transformData.translation)\n        }\n      }\n\n      return material\n    }\n\n    function buildMaterialNode(node) {\n      const materialData = {}\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'ambientIntensity':\n            // field not supported\n            break\n\n          case 'diffuseColor':\n            materialData.diffuseColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'emissiveColor':\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'shininess':\n            materialData.shininess = fieldValues[0]\n            break\n\n          case 'specularColor':\n            materialData.emissiveColor = new Color(fieldValues[0], fieldValues[1], fieldValues[2])\n            break\n\n          case 'transparency':\n            materialData.transparency = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return materialData\n    }\n\n    function parseHexColor(hex, textureType, color) {\n      let value\n\n      switch (textureType) {\n        case TEXTURE_TYPE.INTENSITY:\n          // Intensity texture: A one-component image specifies one-byte hexadecimal or integer values representing the intensity of the image\n          value = parseInt(hex)\n          color.r = value\n          color.g = value\n          color.b = value\n          color.a = 1\n          break\n\n        case TEXTURE_TYPE.INTENSITY_ALPHA:\n          // Intensity+Alpha texture: A two-component image specifies the intensity in the first (high) byte and the alpha opacity in the second (low) byte.\n          value = parseInt('0x' + hex.substring(2, 4))\n          color.r = value\n          color.g = value\n          color.b = value\n          color.a = parseInt('0x' + hex.substring(4, 6))\n          break\n\n        case TEXTURE_TYPE.RGB:\n          // RGB texture: Pixels in a three-component image specify the red component in the first (high) byte, followed by the green and blue components\n          color.r = parseInt('0x' + hex.substring(2, 4))\n          color.g = parseInt('0x' + hex.substring(4, 6))\n          color.b = parseInt('0x' + hex.substring(6, 8))\n          color.a = 1\n          break\n\n        case TEXTURE_TYPE.RGBA:\n          // RGBA texture: Four-component images specify the alpha opacity byte after red/green/blue\n          color.r = parseInt('0x' + hex.substring(2, 4))\n          color.g = parseInt('0x' + hex.substring(4, 6))\n          color.b = parseInt('0x' + hex.substring(6, 8))\n          color.a = parseInt('0x' + hex.substring(8, 10))\n          break\n\n        default:\n      }\n    }\n\n    function getTextureType(num_components) {\n      let type\n\n      switch (num_components) {\n        case 1:\n          type = TEXTURE_TYPE.INTENSITY\n          break\n\n        case 2:\n          type = TEXTURE_TYPE.INTENSITY_ALPHA\n          break\n\n        case 3:\n          type = TEXTURE_TYPE.RGB\n          break\n\n        case 4:\n          type = TEXTURE_TYPE.RGBA\n          break\n\n        default:\n      }\n\n      return type\n    }\n\n    function buildPixelTextureNode(node) {\n      let texture\n      let wrapS = RepeatWrapping\n      let wrapT = RepeatWrapping\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'image':\n            const width = fieldValues[0]\n            const height = fieldValues[1]\n            const num_components = fieldValues[2]\n\n            const textureType = getTextureType(num_components)\n\n            const data = new Uint8Array(4 * width * height)\n\n            const color = { r: 0, g: 0, b: 0, a: 0 }\n\n            for (let j = 3, k = 0, jl = fieldValues.length; j < jl; j++, k++) {\n              parseHexColor(fieldValues[j], textureType, color)\n\n              const stride = k * 4\n\n              data[stride + 0] = color.r\n              data[stride + 1] = color.g\n              data[stride + 2] = color.b\n              data[stride + 3] = color.a\n            }\n\n            texture = new DataTexture(data, width, height)\n            texture.needsUpdate = true\n            texture.__type = textureType // needed for material modifications\n            break\n\n          case 'repeatS':\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping\n            break\n\n          case 'repeatT':\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (texture) {\n        texture.wrapS = wrapS\n        texture.wrapT = wrapT\n      }\n\n      return texture\n    }\n\n    function buildImageTextureNode(node) {\n      let texture\n      let wrapS = RepeatWrapping\n      let wrapT = RepeatWrapping\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'url':\n            const url = fieldValues[0]\n            if (url) texture = textureLoader.load(url)\n            break\n\n          case 'repeatS':\n            if (fieldValues[0] === false) wrapS = ClampToEdgeWrapping\n            break\n\n          case 'repeatT':\n            if (fieldValues[0] === false) wrapT = ClampToEdgeWrapping\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (texture) {\n        texture.wrapS = wrapS\n        texture.wrapT = wrapT\n      }\n\n      return texture\n    }\n\n    function buildTextureTransformNode(node) {\n      const transformData = {\n        center: new Vector2(),\n        rotation: new Vector2(),\n        scale: new Vector2(),\n        translation: new Vector2(),\n      }\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'center':\n            transformData.center.set(fieldValues[0], fieldValues[1])\n            break\n\n          case 'rotation':\n            transformData.rotation = fieldValues[0]\n            break\n\n          case 'scale':\n            transformData.scale.set(fieldValues[0], fieldValues[1])\n            break\n\n          case 'translation':\n            transformData.translation.set(fieldValues[0], fieldValues[1])\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return transformData\n    }\n\n    function buildGeometricNode(node) {\n      return node.fields[0].values\n    }\n\n    function buildWorldInfoNode(node) {\n      const worldInfo = {}\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'title':\n            worldInfo.title = fieldValues[0]\n            break\n\n          case 'info':\n            worldInfo.info = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      return worldInfo\n    }\n\n    function buildIndexedFaceSetNode(node) {\n      let color, coord, normal, texCoord\n      let ccw = true,\n        solid = true,\n        creaseAngle = 0\n      let colorIndex, coordIndex, normalIndex, texCoordIndex\n      let colorPerVertex = true,\n        normalPerVertex = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          case 'normal':\n            const normalNode = fieldValues[0]\n\n            if (normalNode !== null) {\n              normal = getNode(normalNode)\n            }\n\n            break\n\n          case 'texCoord':\n            const texCoordNode = fieldValues[0]\n\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode)\n            }\n\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'colorIndex':\n            colorIndex = fieldValues\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'convex':\n            // field not supported\n            break\n\n          case 'coordIndex':\n            coordIndex = fieldValues\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'normalIndex':\n            normalIndex = fieldValues\n            break\n\n          case 'normalPerVertex':\n            normalPerVertex = fieldValues[0]\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'texCoordIndex':\n            texCoordIndex = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      if (coordIndex === undefined) {\n        console.warn('THREE.VRMLLoader: Missing coordIndex.')\n\n        return new BufferGeometry() // handle VRML files with incomplete geometry definition\n      }\n\n      const triangulatedCoordIndex = triangulateFaceIndex(coordIndex, ccw)\n\n      let colorAttribute\n      let normalAttribute\n      let uvAttribute\n\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex && colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then it is used to choose colors for each vertex of the IndexedFaceSet.\n\n            const triangulatedColorIndex = triangulateFaceIndex(colorIndex, ccw)\n            colorAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedColorIndex, color, 3)\n          } else {\n            // if the colorIndex field is empty, then the coordIndex field is used to choose colors from the Color node\n\n            colorAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(color, 3))\n          }\n        } else {\n          if (colorIndex && colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then they are used to choose one color for each face of the IndexedFaceSet\n\n            const flattenFaceColors = flattenData(color, colorIndex)\n            const triangulatedFaceColors = triangulateFaceData(flattenFaceColors, coordIndex)\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors)\n          } else {\n            // if the colorIndex field is empty, then the color are applied to each face of the IndexedFaceSet in order\n\n            const triangulatedFaceColors = triangulateFaceData(color, coordIndex)\n            colorAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceColors)\n          }\n        }\n      }\n\n      if (normal) {\n        if (normalPerVertex === true) {\n          // consider vertex normals\n\n          if (normalIndex && normalIndex.length > 0) {\n            // if the normalIndex field is not empty, then it is used to choose normals for each vertex of the IndexedFaceSet.\n\n            const triangulatedNormalIndex = triangulateFaceIndex(normalIndex, ccw)\n            normalAttribute = computeAttributeFromIndexedData(\n              triangulatedCoordIndex,\n              triangulatedNormalIndex,\n              normal,\n              3,\n            )\n          } else {\n            // if the normalIndex field is empty, then the coordIndex field is used to choose normals from the Normal node\n\n            normalAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(normal, 3))\n          }\n        } else {\n          // consider face normals\n\n          if (normalIndex && normalIndex.length > 0) {\n            // if the normalIndex field is not empty, then they are used to choose one normal for each face of the IndexedFaceSet\n\n            const flattenFaceNormals = flattenData(normal, normalIndex)\n            const triangulatedFaceNormals = triangulateFaceData(flattenFaceNormals, coordIndex)\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals)\n          } else {\n            // if the normalIndex field is empty, then the normals are applied to each face of the IndexedFaceSet in order\n\n            const triangulatedFaceNormals = triangulateFaceData(normal, coordIndex)\n            normalAttribute = computeAttributeFromFaceData(triangulatedCoordIndex, triangulatedFaceNormals)\n          }\n        }\n      } else {\n        // if the normal field is NULL, then the loader should automatically generate normals, using creaseAngle to determine if and how normals are smoothed across shared vertices\n\n        normalAttribute = computeNormalAttribute(triangulatedCoordIndex, coord, creaseAngle)\n      }\n\n      if (texCoord) {\n        // texture coordinates are always defined on vertex level\n\n        if (texCoordIndex && texCoordIndex.length > 0) {\n          // if the texCoordIndex field is not empty, then it is used to choose texture coordinates for each vertex of the IndexedFaceSet.\n\n          const triangulatedTexCoordIndex = triangulateFaceIndex(texCoordIndex, ccw)\n          uvAttribute = computeAttributeFromIndexedData(triangulatedCoordIndex, triangulatedTexCoordIndex, texCoord, 2)\n        } else {\n          // if the texCoordIndex field is empty, then the coordIndex array is used to choose texture coordinates from the TextureCoordinate node\n\n          uvAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(texCoord, 2))\n        }\n      }\n\n      const geometry = new BufferGeometry()\n      const positionAttribute = toNonIndexedAttribute(triangulatedCoordIndex, new Float32BufferAttribute(coord, 3))\n\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n\n      // optional attributes\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n      if (uvAttribute) geometry.setAttribute('uv', uvAttribute)\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    function buildIndexedLineSetNode(node) {\n      let color, coord\n      let colorIndex, coordIndex\n      let colorPerVertex = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          case 'colorIndex':\n            colorIndex = fieldValues\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'coordIndex':\n            coordIndex = fieldValues\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // build lines\n\n      let colorAttribute\n\n      const expandedLineIndex = expandLineIndex(coordIndex) // create an index for three.js's linesegment primitive\n\n      if (color) {\n        if (colorPerVertex === true) {\n          if (colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then one color is used for each polyline of the IndexedLineSet.\n\n            const expandedColorIndex = expandLineIndex(colorIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromIndexedData(expandedLineIndex, expandedColorIndex, color, 3) // compute data on vertex level\n          } else {\n            // if the colorIndex field is empty, then the colors are applied to each polyline of the IndexedLineSet in order.\n\n            colorAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(color, 3))\n          }\n        } else {\n          if (colorIndex.length > 0) {\n            // if the colorIndex field is not empty, then colors are applied to each vertex of the IndexedLineSet\n\n            const flattenLineColors = flattenData(color, colorIndex) // compute colors for each VRML primitve\n            const expandedLineColors = expandLineData(flattenLineColors, coordIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors) // compute data on vertex level\n          } else {\n            // if the colorIndex field is empty, then the coordIndex field is used to choose colors from the Color node\n\n            const expandedLineColors = expandLineData(color, coordIndex) // compute colors for each line segment (rendering primitve)\n            colorAttribute = computeAttributeFromLineData(expandedLineIndex, expandedLineColors) // compute data on vertex level\n          }\n        }\n      }\n\n      //\n\n      const geometry = new BufferGeometry()\n\n      const positionAttribute = toNonIndexedAttribute(expandedLineIndex, new Float32BufferAttribute(coord, 3))\n      geometry.setAttribute('position', positionAttribute)\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n\n      geometry._type = 'line'\n\n      return geometry\n    }\n\n    function buildPointSetNode(node) {\n      let color, coord\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'coord':\n            const coordNode = fieldValues[0]\n\n            if (coordNode !== null) {\n              coord = getNode(coordNode)\n            }\n\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new BufferGeometry()\n\n      geometry.setAttribute('position', new Float32BufferAttribute(coord, 3))\n      if (color) geometry.setAttribute('color', new Float32BufferAttribute(color, 3))\n\n      geometry._type = 'points'\n\n      return geometry\n    }\n\n    function buildBoxNode(node) {\n      const size = new Vector3(2, 2, 2)\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'size':\n            size.x = fieldValues[0]\n            size.y = fieldValues[1]\n            size.z = fieldValues[2]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new BoxGeometry(size.x, size.y, size.z)\n\n      return geometry\n    }\n\n    function buildConeNode(node) {\n      let radius = 1,\n        height = 2,\n        openEnded = false\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bottom':\n            openEnded = !fieldValues[0]\n            break\n\n          case 'bottomRadius':\n            radius = fieldValues[0]\n            break\n\n          case 'height':\n            height = fieldValues[0]\n            break\n\n          case 'side':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new ConeGeometry(radius, height, 16, 1, openEnded)\n\n      return geometry\n    }\n\n    function buildCylinderNode(node) {\n      let radius = 1,\n        height = 2\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'bottom':\n            // field not supported\n            break\n\n          case 'radius':\n            radius = fieldValues[0]\n            break\n\n          case 'height':\n            height = fieldValues[0]\n            break\n\n          case 'side':\n            // field not supported\n            break\n\n          case 'top':\n            // field not supported\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new CylinderGeometry(radius, radius, height, 16, 1)\n\n      return geometry\n    }\n\n    function buildSphereNode(node) {\n      let radius = 1\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'radius':\n            radius = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const geometry = new SphereGeometry(radius, 16, 16)\n\n      return geometry\n    }\n\n    function buildElevationGridNode(node) {\n      let color\n      let normal\n      let texCoord\n      let height\n\n      let colorPerVertex = true\n      let normalPerVertex = true\n      let solid = true\n      let ccw = true\n      let creaseAngle = 0\n      let xDimension = 2\n      let zDimension = 2\n      let xSpacing = 1\n      let zSpacing = 1\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'color':\n            const colorNode = fieldValues[0]\n\n            if (colorNode !== null) {\n              color = getNode(colorNode)\n            }\n\n            break\n\n          case 'normal':\n            const normalNode = fieldValues[0]\n\n            if (normalNode !== null) {\n              normal = getNode(normalNode)\n            }\n\n            break\n\n          case 'texCoord':\n            const texCoordNode = fieldValues[0]\n\n            if (texCoordNode !== null) {\n              texCoord = getNode(texCoordNode)\n            }\n\n            break\n\n          case 'height':\n            height = fieldValues\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'colorPerVertex':\n            colorPerVertex = fieldValues[0]\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'normalPerVertex':\n            normalPerVertex = fieldValues[0]\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'xDimension':\n            xDimension = fieldValues[0]\n            break\n\n          case 'xSpacing':\n            xSpacing = fieldValues[0]\n            break\n\n          case 'zDimension':\n            zDimension = fieldValues[0]\n            break\n\n          case 'zSpacing':\n            zSpacing = fieldValues[0]\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      // vertex data\n\n      const vertices = []\n      const normals = []\n      const colors = []\n      const uvs = []\n\n      for (let i = 0; i < zDimension; i++) {\n        for (let j = 0; j < xDimension; j++) {\n          // compute a row major index\n\n          const index = i * xDimension + j\n\n          // vertices\n\n          const x = xSpacing * i\n          const y = height[index]\n          const z = zSpacing * j\n\n          vertices.push(x, y, z)\n\n          // colors\n\n          if (color && colorPerVertex === true) {\n            const r = color[index * 3 + 0]\n            const g = color[index * 3 + 1]\n            const b = color[index * 3 + 2]\n\n            colors.push(r, g, b)\n          }\n\n          // normals\n\n          if (normal && normalPerVertex === true) {\n            const xn = normal[index * 3 + 0]\n            const yn = normal[index * 3 + 1]\n            const zn = normal[index * 3 + 2]\n\n            normals.push(xn, yn, zn)\n          }\n\n          // uvs\n\n          if (texCoord) {\n            const s = texCoord[index * 2 + 0]\n            const t = texCoord[index * 2 + 1]\n\n            uvs.push(s, t)\n          } else {\n            uvs.push(i / (xDimension - 1), j / (zDimension - 1))\n          }\n        }\n      }\n\n      // indices\n\n      const indices = []\n\n      for (let i = 0; i < xDimension - 1; i++) {\n        for (let j = 0; j < zDimension - 1; j++) {\n          // from https://tecfa.unige.ch/guides/vrml/vrml97/spec/part1/nodesRef.html#ElevationGrid\n\n          const a = i + j * xDimension\n          const b = i + (j + 1) * xDimension\n          const c = i + 1 + (j + 1) * xDimension\n          const d = i + 1 + j * xDimension\n\n          // faces\n\n          if (ccw === true) {\n            indices.push(a, c, b)\n            indices.push(c, a, d)\n          } else {\n            indices.push(a, b, c)\n            indices.push(c, d, a)\n          }\n        }\n      }\n\n      //\n\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3))\n      const uvAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(uvs, 2))\n      let colorAttribute\n      let normalAttribute\n\n      // color attribute\n\n      if (color) {\n        if (colorPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1)\n\n              const r = color[index * 3 + 0]\n              const g = color[index * 3 + 1]\n              const b = color[index * 3 + 2]\n\n              // one color per quad\n\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n              colors.push(r, g, b)\n            }\n          }\n\n          colorAttribute = new Float32BufferAttribute(colors, 3)\n        } else {\n          colorAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(colors, 3))\n        }\n      }\n\n      // normal attribute\n\n      if (normal) {\n        if (normalPerVertex === false) {\n          for (let i = 0; i < xDimension - 1; i++) {\n            for (let j = 0; j < zDimension - 1; j++) {\n              const index = i + j * (xDimension - 1)\n\n              const xn = normal[index * 3 + 0]\n              const yn = normal[index * 3 + 1]\n              const zn = normal[index * 3 + 2]\n\n              // one normal per quad\n\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n              normals.push(xn, yn, zn)\n            }\n          }\n\n          normalAttribute = new Float32BufferAttribute(normals, 3)\n        } else {\n          normalAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(normals, 3))\n        }\n      } else {\n        normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle)\n      }\n\n      // build geometry\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n      geometry.setAttribute('uv', uvAttribute)\n\n      if (colorAttribute) geometry.setAttribute('color', colorAttribute)\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    function buildExtrusionNode(node) {\n      let crossSection = [1, 1, 1, -1, -1, -1, -1, 1, 1, 1]\n      let spine = [0, 0, 0, 0, 1, 0]\n      let scale\n      let orientation\n\n      let beginCap = true\n      let ccw = true\n      let creaseAngle = 0\n      let endCap = true\n      let solid = true\n\n      const fields = node.fields\n\n      for (let i = 0, l = fields.length; i < l; i++) {\n        const field = fields[i]\n        const fieldName = field.name\n        const fieldValues = field.values\n\n        switch (fieldName) {\n          case 'beginCap':\n            beginCap = fieldValues[0]\n            break\n\n          case 'ccw':\n            ccw = fieldValues[0]\n            break\n\n          case 'convex':\n            // field not supported\n            break\n\n          case 'creaseAngle':\n            creaseAngle = fieldValues[0]\n            break\n\n          case 'crossSection':\n            crossSection = fieldValues\n            break\n\n          case 'endCap':\n            endCap = fieldValues[0]\n            break\n\n          case 'orientation':\n            orientation = fieldValues\n            break\n\n          case 'scale':\n            scale = fieldValues\n            break\n\n          case 'solid':\n            solid = fieldValues[0]\n            break\n\n          case 'spine':\n            spine = fieldValues // only extrusion along the Y-axis are supported so far\n            break\n\n          default:\n            console.warn('THREE.VRMLLoader: Unknown field:', fieldName)\n            break\n        }\n      }\n\n      const crossSectionClosed =\n        crossSection[0] === crossSection[crossSection.length - 2] &&\n        crossSection[1] === crossSection[crossSection.length - 1]\n\n      // vertices\n\n      const vertices = []\n      const spineVector = new Vector3()\n      const scaling = new Vector3()\n\n      const axis = new Vector3()\n      const vertex = new Vector3()\n      const quaternion = new Quaternion()\n\n      for (let i = 0, j = 0, o = 0, il = spine.length; i < il; i += 3, j += 2, o += 4) {\n        spineVector.fromArray(spine, i)\n\n        scaling.x = scale ? scale[j + 0] : 1\n        scaling.y = 1\n        scaling.z = scale ? scale[j + 1] : 1\n\n        axis.x = orientation ? orientation[o + 0] : 0\n        axis.y = orientation ? orientation[o + 1] : 0\n        axis.z = orientation ? orientation[o + 2] : 1\n        const angle = orientation ? orientation[o + 3] : 0\n\n        for (let k = 0, kl = crossSection.length; k < kl; k += 2) {\n          vertex.x = crossSection[k + 0]\n          vertex.y = 0\n          vertex.z = crossSection[k + 1]\n\n          // scale\n\n          vertex.multiply(scaling)\n\n          // rotate\n\n          quaternion.setFromAxisAngle(axis, angle)\n          vertex.applyQuaternion(quaternion)\n\n          // translate\n\n          vertex.add(spineVector)\n\n          vertices.push(vertex.x, vertex.y, vertex.z)\n        }\n      }\n\n      // indices\n\n      const indices = []\n\n      const spineCount = spine.length / 3\n      const crossSectionCount = crossSection.length / 2\n\n      for (let i = 0; i < spineCount - 1; i++) {\n        for (let j = 0; j < crossSectionCount - 1; j++) {\n          const a = j + i * crossSectionCount\n          let b = j + 1 + i * crossSectionCount\n          const c = j + (i + 1) * crossSectionCount\n          let d = j + 1 + (i + 1) * crossSectionCount\n\n          if (j === crossSectionCount - 2 && crossSectionClosed === true) {\n            b = i * crossSectionCount\n            d = (i + 1) * crossSectionCount\n          }\n\n          if (ccw === true) {\n            indices.push(a, b, c)\n            indices.push(c, b, d)\n          } else {\n            indices.push(a, c, b)\n            indices.push(c, d, b)\n          }\n        }\n      }\n\n      // triangulate cap\n\n      if (beginCap === true || endCap === true) {\n        const contour = []\n\n        for (let i = 0, l = crossSection.length; i < l; i += 2) {\n          contour.push(new Vector2(crossSection[i], crossSection[i + 1]))\n        }\n\n        const faces = ShapeUtils.triangulateShape(contour, [])\n        const capIndices = []\n\n        for (let i = 0, l = faces.length; i < l; i++) {\n          const face = faces[i]\n\n          capIndices.push(face[0], face[1], face[2])\n        }\n\n        // begin cap\n\n        if (beginCap === true) {\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(capIndices[i + 0], capIndices[i + 1], capIndices[i + 2])\n            } else {\n              indices.push(capIndices[i + 0], capIndices[i + 2], capIndices[i + 1])\n            }\n          }\n        }\n\n        // end cap\n\n        if (endCap === true) {\n          const indexOffset = crossSectionCount * (spineCount - 1) // references to the first vertex of the last cross section\n\n          for (let i = 0, l = capIndices.length; i < l; i += 3) {\n            if (ccw === true) {\n              indices.push(\n                indexOffset + capIndices[i + 0],\n                indexOffset + capIndices[i + 2],\n                indexOffset + capIndices[i + 1],\n              )\n            } else {\n              indices.push(\n                indexOffset + capIndices[i + 0],\n                indexOffset + capIndices[i + 1],\n                indexOffset + capIndices[i + 2],\n              )\n            }\n          }\n        }\n      }\n\n      const positionAttribute = toNonIndexedAttribute(indices, new Float32BufferAttribute(vertices, 3))\n      const normalAttribute = computeNormalAttribute(indices, vertices, creaseAngle)\n\n      const geometry = new BufferGeometry()\n      geometry.setAttribute('position', positionAttribute)\n      geometry.setAttribute('normal', normalAttribute)\n      // no uvs yet\n\n      // \"solid\" influences the material so let's store it for later use\n\n      geometry._solid = solid\n      geometry._type = 'mesh'\n\n      return geometry\n    }\n\n    // helper functions\n\n    function resolveUSE(identifier) {\n      const node = nodeMap[identifier]\n      const build = getNode(node)\n\n      // because the same 3D objects can have different transformations, it's necessary to clone them.\n      // materials can be influenced by the geometry (e.g. vertex normals). cloning is necessary to avoid\n      // any side effects\n\n      return build.isObject3D || build.isMaterial ? build.clone() : build\n    }\n\n    function parseFieldChildren(children, owner) {\n      for (let i = 0, l = children.length; i < l; i++) {\n        const object = getNode(children[i])\n\n        if (object instanceof Object3D) owner.add(object)\n      }\n    }\n\n    function triangulateFaceIndex(index, ccw) {\n      const indices = []\n\n      // since face defintions can have more than three vertices, it's necessary to\n      // perform a simple triangulation\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[start]\n        const i2 = index[i + (ccw ? 1 : 2)]\n        const i3 = index[i + (ccw ? 2 : 1)]\n\n        indices.push(i1, i2, i3)\n\n        // an index of -1 indicates that the current face has ended and the next one begins\n\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3\n          start = i + 1\n        }\n      }\n\n      return indices\n    }\n\n    function triangulateFaceData(data, index) {\n      const triangulatedData = []\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        triangulatedData.push(x, y, z)\n\n        // an index of -1 indicates that the current face has ended and the next one begins\n\n        if (index[i + 3] === -1 || i + 3 >= l) {\n          i += 3\n          start++\n        }\n      }\n\n      return triangulatedData\n    }\n\n    function flattenData(data, index) {\n      const flattenData = []\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i]\n\n        const stride = i1 * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        flattenData.push(x, y, z)\n      }\n\n      return flattenData\n    }\n\n    function expandLineIndex(index) {\n      const indices = []\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const i1 = index[i]\n        const i2 = index[i + 1]\n\n        indices.push(i1, i2)\n\n        // an index of -1 indicates that the current line has ended and the next one begins\n\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2\n        }\n      }\n\n      return indices\n    }\n\n    function expandLineData(data, index) {\n      const triangulatedData = []\n\n      let start = 0\n\n      for (let i = 0, l = index.length; i < l; i++) {\n        const stride = start * 3\n\n        const x = data[stride]\n        const y = data[stride + 1]\n        const z = data[stride + 2]\n\n        triangulatedData.push(x, y, z)\n\n        // an index of -1 indicates that the current line has ended and the next one begins\n\n        if (index[i + 2] === -1 || i + 2 >= l) {\n          i += 2\n          start++\n        }\n      }\n\n      return triangulatedData\n    }\n\n    const vA = new Vector3()\n    const vB = new Vector3()\n    const vC = new Vector3()\n\n    const uvA = new Vector2()\n    const uvB = new Vector2()\n    const uvC = new Vector2()\n\n    function computeAttributeFromIndexedData(coordIndex, index, data, itemSize) {\n      const array = []\n\n      // we use the coordIndex.length as delimiter since normalIndex must contain at least as many indices\n\n      for (let i = 0, l = coordIndex.length; i < l; i += 3) {\n        const a = index[i]\n        const b = index[i + 1]\n        const c = index[i + 2]\n\n        if (itemSize === 2) {\n          uvA.fromArray(data, a * itemSize)\n          uvB.fromArray(data, b * itemSize)\n          uvC.fromArray(data, c * itemSize)\n\n          array.push(uvA.x, uvA.y)\n          array.push(uvB.x, uvB.y)\n          array.push(uvC.x, uvC.y)\n        } else {\n          vA.fromArray(data, a * itemSize)\n          vB.fromArray(data, b * itemSize)\n          vC.fromArray(data, c * itemSize)\n\n          array.push(vA.x, vA.y, vA.z)\n          array.push(vB.x, vB.y, vB.z)\n          array.push(vC.x, vC.y, vC.z)\n        }\n      }\n\n      return new Float32BufferAttribute(array, itemSize)\n    }\n\n    function computeAttributeFromFaceData(index, faceData) {\n      const array = []\n\n      for (let i = 0, j = 0, l = index.length; i < l; i += 3, j++) {\n        vA.fromArray(faceData, j * 3)\n\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n      }\n\n      return new Float32BufferAttribute(array, 3)\n    }\n\n    function computeAttributeFromLineData(index, lineData) {\n      const array = []\n\n      for (let i = 0, j = 0, l = index.length; i < l; i += 2, j++) {\n        vA.fromArray(lineData, j * 3)\n\n        array.push(vA.x, vA.y, vA.z)\n        array.push(vA.x, vA.y, vA.z)\n      }\n\n      return new Float32BufferAttribute(array, 3)\n    }\n\n    function toNonIndexedAttribute(indices, attribute) {\n      const array = attribute.array\n      const itemSize = attribute.itemSize\n\n      const array2 = new array.constructor(indices.length * itemSize)\n\n      let index = 0,\n        index2 = 0\n\n      for (let i = 0, l = indices.length; i < l; i++) {\n        index = indices[i] * itemSize\n\n        for (let j = 0; j < itemSize; j++) {\n          array2[index2++] = array[index++]\n        }\n      }\n\n      return new Float32BufferAttribute(array2, itemSize)\n    }\n\n    const ab = new Vector3()\n    const cb = new Vector3()\n\n    function computeNormalAttribute(index, coord, creaseAngle) {\n      const faces = []\n      const vertexNormals = {}\n\n      // prepare face and raw vertex normals\n\n      for (let i = 0, l = index.length; i < l; i += 3) {\n        const a = index[i]\n        const b = index[i + 1]\n        const c = index[i + 2]\n\n        const face = new Face(a, b, c)\n\n        vA.fromArray(coord, a * 3)\n        vB.fromArray(coord, b * 3)\n        vC.fromArray(coord, c * 3)\n\n        cb.subVectors(vC, vB)\n        ab.subVectors(vA, vB)\n        cb.cross(ab)\n\n        cb.normalize()\n\n        face.normal.copy(cb)\n\n        if (vertexNormals[a] === undefined) vertexNormals[a] = []\n        if (vertexNormals[b] === undefined) vertexNormals[b] = []\n        if (vertexNormals[c] === undefined) vertexNormals[c] = []\n\n        vertexNormals[a].push(face.normal)\n        vertexNormals[b].push(face.normal)\n        vertexNormals[c].push(face.normal)\n\n        faces.push(face)\n      }\n\n      // compute vertex normals and build final geometry\n\n      const normals = []\n\n      for (let i = 0, l = faces.length; i < l; i++) {\n        const face = faces[i]\n\n        const nA = weightedNormal(vertexNormals[face.a], face.normal, creaseAngle)\n        const nB = weightedNormal(vertexNormals[face.b], face.normal, creaseAngle)\n        const nC = weightedNormal(vertexNormals[face.c], face.normal, creaseAngle)\n\n        vA.fromArray(coord, face.a * 3)\n        vB.fromArray(coord, face.b * 3)\n        vC.fromArray(coord, face.c * 3)\n\n        normals.push(nA.x, nA.y, nA.z)\n        normals.push(nB.x, nB.y, nB.z)\n        normals.push(nC.x, nC.y, nC.z)\n      }\n\n      return new Float32BufferAttribute(normals, 3)\n    }\n\n    function weightedNormal(normals, vector, creaseAngle) {\n      const normal = new Vector3()\n\n      if (creaseAngle === 0) {\n        normal.copy(vector)\n      } else {\n        for (let i = 0, l = normals.length; i < l; i++) {\n          if (normals[i].angleTo(vector) < creaseAngle) {\n            normal.add(normals[i])\n          }\n        }\n      }\n\n      return normal.normalize()\n    }\n\n    function toColorArray(colors) {\n      const array = []\n\n      for (let i = 0, l = colors.length; i < l; i += 3) {\n        array.push(new Color(colors[i], colors[i + 1], colors[i + 2]))\n      }\n\n      return array\n    }\n\n    /**\n     * Vertically paints the faces interpolating between the\n     * specified colors at the specified angels. This is used for the Background\n     * node, but could be applied to other nodes with multiple faces as well.\n     *\n     * When used with the Background node, default is directionIsDown is true if\n     * interpolating the skyColor down from the Zenith. When interpolationg up from\n     * the Nadir i.e. interpolating the groundColor, the directionIsDown is false.\n     *\n     * The first angle is never specified, it is the Zenith (0 rad). Angles are specified\n     * in radians. The geometry is thought a sphere, but could be anything. The color interpolation\n     * is linear along the Y axis in any case.\n     *\n     * You must specify one more color than you have angles at the beginning of the colors array.\n     * This is the color of the Zenith (the top of the shape).\n     *\n     * @param {BufferGeometry} geometry\n     * @param {number} radius\n     * @param {array} angles\n     * @param {array} colors\n     * @param {boolean} topDown - Whether to work top down or bottom up.\n     */\n    function paintFaces(geometry, radius, angles, colors, topDown) {\n      // compute threshold values\n\n      const thresholds = []\n      const startAngle = topDown === true ? 0 : Math.PI\n\n      for (let i = 0, l = colors.length; i < l; i++) {\n        let angle = i === 0 ? 0 : angles[i - 1]\n        angle = topDown === true ? angle : startAngle - angle\n\n        const point = new Vector3()\n        point.setFromSphericalCoords(radius, angle, 0)\n\n        thresholds.push(point)\n      }\n\n      // generate vertex colors\n\n      const indices = geometry.index\n      const positionAttribute = geometry.attributes.position\n      const colorAttribute = new BufferAttribute(new Float32Array(geometry.attributes.position.count * 3), 3)\n\n      const position = new Vector3()\n      const color = new Color()\n\n      for (let i = 0; i < indices.count; i++) {\n        const index = indices.getX(i)\n        position.fromBufferAttribute(positionAttribute, index)\n\n        let thresholdIndexA, thresholdIndexB\n        let t = 1\n\n        for (let j = 1; j < thresholds.length; j++) {\n          thresholdIndexA = j - 1\n          thresholdIndexB = j\n\n          const thresholdA = thresholds[thresholdIndexA]\n          const thresholdB = thresholds[thresholdIndexB]\n\n          if (topDown === true) {\n            // interpolation for sky color\n\n            if (position.y <= thresholdA.y && position.y > thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y)\n\n              break\n            }\n          } else {\n            // interpolation for ground color\n\n            if (position.y >= thresholdA.y && position.y < thresholdB.y) {\n              t = Math.abs(thresholdA.y - position.y) / Math.abs(thresholdA.y - thresholdB.y)\n\n              break\n            }\n          }\n        }\n\n        const colorA = colors[thresholdIndexA]\n        const colorB = colors[thresholdIndexB]\n\n        color.copy(colorA).lerp(colorB, t)\n\n        colorAttribute.setXYZ(index, color.r, color.g, color.b)\n      }\n\n      geometry.setAttribute('color', colorAttribute)\n    }\n\n    //\n\n    const textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    // check version (only 2.0 is supported)\n\n    if (data.indexOf('#VRML V2.0') === -1) {\n      throw Error('THREE.VRMLLexer: Version of VRML asset not supported.')\n    }\n\n    // create JSON representing the tree structure of the VRML asset\n\n    const tree = generateVRMLTree(data)\n\n    // parse the tree structure to a three.js scene\n\n    const scene = parseTree(tree)\n\n    return scene\n  }\n}\n\nclass VRMLLexer {\n  constructor(tokens) {\n    this.lexer = new Lexer(tokens)\n  }\n\n  lex(inputText) {\n    const lexingResult = this.lexer.tokenize(inputText)\n\n    if (lexingResult.errors.length > 0) {\n      console.error(lexingResult.errors)\n\n      throw Error('THREE.VRMLLexer: Lexing errors detected.')\n    }\n\n    return lexingResult\n  }\n}\n\nclass VRMLParser extends CstParser {\n  constructor(tokenVocabulary) {\n    super(tokenVocabulary)\n\n    const $ = this\n\n    const Version = tokenVocabulary['Version']\n    const LCurly = tokenVocabulary['LCurly']\n    const RCurly = tokenVocabulary['RCurly']\n    const LSquare = tokenVocabulary['LSquare']\n    const RSquare = tokenVocabulary['RSquare']\n    const Identifier = tokenVocabulary['Identifier']\n    const RouteIdentifier = tokenVocabulary['RouteIdentifier']\n    const StringLiteral = tokenVocabulary['StringLiteral']\n    const HexLiteral = tokenVocabulary['HexLiteral']\n    const NumberLiteral = tokenVocabulary['NumberLiteral']\n    const TrueLiteral = tokenVocabulary['TrueLiteral']\n    const FalseLiteral = tokenVocabulary['FalseLiteral']\n    const NullLiteral = tokenVocabulary['NullLiteral']\n    const DEF = tokenVocabulary['DEF']\n    const USE = tokenVocabulary['USE']\n    const ROUTE = tokenVocabulary['ROUTE']\n    const TO = tokenVocabulary['TO']\n    const NodeName = tokenVocabulary['NodeName']\n\n    $.RULE('vrml', function () {\n      $.SUBRULE($.version)\n      $.AT_LEAST_ONE(function () {\n        $.SUBRULE($.node)\n      })\n      $.MANY(function () {\n        $.SUBRULE($.route)\n      })\n    })\n\n    $.RULE('version', function () {\n      $.CONSUME(Version)\n    })\n\n    $.RULE('node', function () {\n      $.OPTION(function () {\n        $.SUBRULE($.def)\n      })\n\n      $.CONSUME(NodeName)\n      $.CONSUME(LCurly)\n      $.MANY(function () {\n        $.SUBRULE($.field)\n      })\n      $.CONSUME(RCurly)\n    })\n\n    $.RULE('field', function () {\n      $.CONSUME(Identifier)\n\n      $.OR2([\n        {\n          ALT: function () {\n            $.SUBRULE($.singleFieldValue)\n          },\n        },\n        {\n          ALT: function () {\n            $.SUBRULE($.multiFieldValue)\n          },\n        },\n      ])\n    })\n\n    $.RULE('def', function () {\n      $.CONSUME(DEF)\n      $.OR([\n        {\n          ALT: function () {\n            $.CONSUME(Identifier)\n          },\n        },\n        {\n          ALT: function () {\n            $.CONSUME(NodeName)\n          },\n        },\n      ])\n    })\n\n    $.RULE('use', function () {\n      $.CONSUME(USE)\n      $.OR([\n        {\n          ALT: function () {\n            $.CONSUME(Identifier)\n          },\n        },\n        {\n          ALT: function () {\n            $.CONSUME(NodeName)\n          },\n        },\n      ])\n    })\n\n    $.RULE('singleFieldValue', function () {\n      $.AT_LEAST_ONE(function () {\n        $.OR([\n          {\n            ALT: function () {\n              $.SUBRULE($.node)\n            },\n          },\n          {\n            ALT: function () {\n              $.SUBRULE($.use)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(StringLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(HexLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NumberLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(TrueLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(FalseLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NullLiteral)\n            },\n          },\n        ])\n      })\n    })\n\n    $.RULE('multiFieldValue', function () {\n      $.CONSUME(LSquare)\n      $.MANY(function () {\n        $.OR([\n          {\n            ALT: function () {\n              $.SUBRULE($.node)\n            },\n          },\n          {\n            ALT: function () {\n              $.SUBRULE($.use)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(StringLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(HexLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NumberLiteral)\n            },\n          },\n          {\n            ALT: function () {\n              $.CONSUME(NullLiteral)\n            },\n          },\n        ])\n      })\n      $.CONSUME(RSquare)\n    })\n\n    $.RULE('route', function () {\n      $.CONSUME(ROUTE)\n      $.CONSUME(RouteIdentifier)\n      $.CONSUME(TO)\n      $.CONSUME2(RouteIdentifier)\n    })\n\n    this.performSelfAnalysis()\n  }\n}\n\nclass Face {\n  constructor(a, b, c) {\n    this.a = a\n    this.b = b\n    this.c = c\n    this.normal = new Vector3()\n  }\n}\n\nconst TEXTURE_TYPE = {\n  INTENSITY: 1,\n  INTENSITY_ALPHA: 2,\n  RGB: 3,\n  RGBA: 4,\n}\n\nexport { VRMLLoader }\n"], "names": ["Loader", "LoaderUtils", "<PERSON><PERSON><PERSON><PERSON>", "data", "createToken", "<PERSON><PERSON>", "tree", "scene", "Scene", "Object3D", "Group", "Vector3", "SphereGeometry", "MeshBasicMaterial", "BackSide", "<PERSON><PERSON>", "PointsMaterial", "Points", "LineBasicMaterial", "LineSegments", "FrontSide", "DoubleSide", "MeshPhongMaterial", "Color", "RepeatWrapping", "DataTexture", "ClampToEdgeWrapping", "Vector2", "BufferGeometry", "Float32BufferAttribute", "BoxGeometry", "ConeGeometry", "CylinderGeometry", "Quaternion", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flattenData", "BufferAttribute", "TextureLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;AAoCA,MAAM,mBAAmBA,MAAAA,OAAO;AAAA,EAC9B,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,OAAO,MAAM,SAAS,KAAKC,MAAW,YAAC,eAAe,GAAG,IAAI,MAAM;AAEzE,UAAM,SAAS,IAAIC,iBAAW,MAAM,OAAO;AAC3C,WAAO,QAAQ,MAAM,IAAI;AACzB,WAAO,iBAAiB,MAAM,aAAa;AAC3C,WAAO,mBAAmB,MAAM,eAAe;AAC/C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,QAC/B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA,EAED,MAAM,MAAM,MAAM;AAChB,UAAM,UAAU,CAAE;AAElB,aAAS,iBAAiBC,OAAM;AAG9B,YAAM,YAAY,aAAc;AAEhC,YAAM,QAAQ,IAAI,UAAU,UAAU,MAAM;AAC5C,YAAM,SAAS,IAAI,WAAW,UAAU,eAAe;AACvD,YAAM,UAAU,cAAc,OAAO,6BAA4B,CAAE;AAInE,YAAM,eAAe,MAAM,IAAIA,KAAI;AACnC,aAAO,QAAQ,aAAa;AAI5B,YAAM,YAAY,OAAO,KAAM;AAE/B,UAAI,OAAO,OAAO,SAAS,GAAG;AAC5B,gBAAQ,MAAM,OAAO,MAAM;AAE3B,cAAM,MAAM,4CAA4C;AAAA,MACzD;AAID,YAAM,MAAM,QAAQ,MAAM,SAAS;AAEnC,aAAO;AAAA,IACR;AAED,aAAS,eAAe;AAGtB,YAAM,kBAAkBC,WAAAA,YAAY;AAAA,QAClC,MAAM;AAAA,QACN,SAAS;AAAA,MACjB,CAAO;AACD,YAAM,aAAaA,WAAAA,YAAY;AAAA,QAC7B,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAID,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACD;AAID,YAAM,UAAUA,WAAAA,YAAY;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,WAAWA,WAAAA,YAAY;AAAA,QAC3B,MAAM;AAAA,QACN,SAAS,IAAI,OAAO,UAAU,KAAK,GAAG,CAAC;AAAA,QACvC,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,MAAMA,WAAAA,YAAY;AAAA,QACtB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,MAAMA,WAAAA,YAAY;AAAA,QACtB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,QAAQA,WAAAA,YAAY;AAAA,QACxB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAED,YAAM,KAAKA,WAAAA,YAAY;AAAA,QACrB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,MACpB,CAAO;AAID,YAAM,gBAAgBA,WAAAA,YAAY;AAAA,QAChC,MAAM;AAAA,QACN,SAAS;AAAA,MACjB,CAAO;AACD,YAAM,aAAaA,WAAAA,YAAY,EAAE,MAAM,cAAc,SAAS,qBAAqB;AACnF,YAAM,gBAAgBA,WAAAA,YAAY,EAAE,MAAM,iBAAiB,SAAS,0CAA0C;AAC9G,YAAM,cAAcA,WAAAA,YAAY,EAAE,MAAM,eAAe,SAAS,QAAQ;AACxE,YAAM,eAAeA,WAAAA,YAAY,EAAE,MAAM,gBAAgB,SAAS,SAAS;AAC3E,YAAM,cAAcA,WAAAA,YAAY,EAAE,MAAM,eAAe,SAAS,QAAQ;AACxE,YAAM,UAAUA,WAAAA,YAAY,EAAE,MAAM,WAAW,SAAS,MAAM;AAC9D,YAAM,UAAUA,WAAAA,YAAY,EAAE,MAAM,WAAW,SAAS,KAAK;AAC7D,YAAM,SAASA,WAAAA,YAAY,EAAE,MAAM,UAAU,SAAS,KAAK;AAC3D,YAAM,SAASA,WAAAA,YAAY,EAAE,MAAM,UAAU,SAAS,KAAK;AAC3D,YAAM,UAAUA,WAAAA,YAAY;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAOC,WAAK,MAAC;AAAA,MACrB,CAAO;AAID,YAAM,aAAaD,WAAAA,YAAY;AAAA,QAC7B,MAAM;AAAA,QACN,SAAS;AAAA,QACT,OAAOC,WAAK,MAAC;AAAA,MACrB,CAAO;AAED,YAAM,SAAS;AAAA,QACb;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAED,YAAM,kBAAkB,CAAE;AAE1B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AAEtB,wBAAgB,MAAM,IAAI,IAAI;AAAA,MAC/B;AAED,aAAO,EAAE,QAAgB,gBAAkC;AAAA,IAC5D;AAED,aAAS,cAAc,iBAAiB;AAGtC,eAAS,mBAAmB;AAC1B,wBAAgB,KAAK,IAAI;AAEzB,aAAK,gBAAiB;AAAA,MACvB;AAED,uBAAiB,YAAY,OAAO,OAAO,OAAO,OAAO,gBAAgB,SAAS,GAAG;AAAA,QACnF,aAAa;AAAA,QAEb,MAAM,SAAU,KAAK;AACnB,gBAAMF,QAAO;AAAA,YACX,SAAS,KAAK,MAAM,IAAI,OAAO;AAAA,YAC/B,OAAO,CAAE;AAAA,YACT,QAAQ,CAAE;AAAA,UACX;AAED,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC/C,kBAAM,OAAO,IAAI,KAAK,CAAC;AAEvB,YAAAA,MAAK,MAAM,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,UACjC;AAED,cAAI,IAAI,OAAO;AACb,qBAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAChD,oBAAM,QAAQ,IAAI,MAAM,CAAC;AAEzB,cAAAA,MAAK,OAAO,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,YACnC;AAAA,UACF;AAED,iBAAOA;AAAA,QACR;AAAA,QAED,SAAS,SAAU,KAAK;AACtB,iBAAO,IAAI,QAAQ,CAAC,EAAE;AAAA,QACvB;AAAA,QAED,MAAM,SAAU,KAAK;AACnB,gBAAMA,QAAO;AAAA,YACX,MAAM,IAAI,SAAS,CAAC,EAAE;AAAA,YACtB,QAAQ,CAAE;AAAA,UACX;AAED,cAAI,IAAI,OAAO;AACb,qBAAS,IAAI,GAAG,IAAI,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAChD,oBAAM,QAAQ,IAAI,MAAM,CAAC;AAEzB,cAAAA,MAAK,OAAO,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,YACnC;AAAA,UACF;AAID,cAAI,IAAI,KAAK;AACX,YAAAA,MAAK,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,CAAC;AAAA,UACjC;AAED,iBAAOA;AAAA,QACR;AAAA,QAED,OAAO,SAAU,KAAK;AACpB,gBAAMA,QAAO;AAAA,YACX,MAAM,IAAI,WAAW,CAAC,EAAE;AAAA,YACxB,MAAM;AAAA,YACN,QAAQ;AAAA,UACT;AAED,cAAI;AAIJ,cAAI,IAAI,kBAAkB;AACxB,qBAAS,KAAK,MAAM,IAAI,iBAAiB,CAAC,CAAC;AAAA,UAC5C;AAID,cAAI,IAAI,iBAAiB;AACvB,qBAAS,KAAK,MAAM,IAAI,gBAAgB,CAAC,CAAC;AAAA,UAC3C;AAED,UAAAA,MAAK,OAAO,OAAO;AACnB,UAAAA,MAAK,SAAS,OAAO;AAErB,iBAAOA;AAAA,QACR;AAAA,QAED,KAAK,SAAU,KAAK;AAClB,kBAAQ,IAAI,cAAc,IAAI,UAAU,CAAC,EAAE;AAAA,QAC5C;AAAA,QAED,KAAK,SAAU,KAAK;AAClB,iBAAO,EAAE,MAAM,IAAI,cAAc,IAAI,UAAU,CAAC,EAAE,MAAO;AAAA,QAC1D;AAAA,QAED,kBAAkB,SAAU,KAAK;AAC/B,iBAAO,aAAa,MAAM,GAAG;AAAA,QAC9B;AAAA,QAED,iBAAiB,SAAU,KAAK;AAC9B,iBAAO,aAAa,MAAM,GAAG;AAAA,QAC9B;AAAA,QAED,OAAO,SAAU,KAAK;AACpB,gBAAMA,QAAO;AAAA,YACX,MAAM,IAAI,gBAAgB,CAAC,EAAE;AAAA,YAC7B,IAAI,IAAI,gBAAgB,CAAC,EAAE;AAAA,UAC5B;AAED,iBAAOA;AAAA,QACR;AAAA,MACT,CAAO;AAED,eAAS,aAAa,OAAO,KAAK;AAChC,cAAM,QAAQ;AAAA,UACZ,MAAM;AAAA,UACN,QAAQ,CAAE;AAAA,QACX;AAED,YAAI,IAAI,MAAM;AACZ,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC/C,kBAAM,OAAO,IAAI,KAAK,CAAC;AAEvB,kBAAM,OAAO,KAAK,MAAM,MAAM,IAAI,CAAC;AAAA,UACpC;AAAA,QACF;AAED,YAAI,IAAI,KAAK;AACX,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC9C,kBAAM,MAAM,IAAI,IAAI,CAAC;AAErB,kBAAM,OAAO,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA,UACnC;AAAA,QACF;AAED,YAAI,IAAI,eAAe;AACrB,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACxD,kBAAM,gBAAgB,IAAI,cAAc,CAAC;AAEzC,kBAAM,OAAO,KAAK,cAAc,MAAM,QAAQ,QAAQ,EAAE,CAAC;AAAA,UAC1D;AAAA,QACF;AAED,YAAI,IAAI,eAAe;AACrB,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,cAAc,QAAQ,IAAI,GAAG,KAAK;AACxD,kBAAM,gBAAgB,IAAI,cAAc,CAAC;AAEzC,kBAAM,OAAO,KAAK,WAAW,cAAc,KAAK,CAAC;AAAA,UAClD;AAAA,QACF;AAED,YAAI,IAAI,YAAY;AAClB,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK;AACrD,kBAAM,aAAa,IAAI,WAAW,CAAC;AAEnC,kBAAM,OAAO,KAAK,WAAW,KAAK;AAAA,UACnC;AAAA,QACF;AAED,YAAI,IAAI,aAAa;AACnB,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,YAAY,QAAQ,IAAI,GAAG,KAAK;AACtD,kBAAM,cAAc,IAAI,YAAY,CAAC;AAErC,gBAAI,YAAY,UAAU;AAAQ,oBAAM,OAAO,KAAK,IAAI;AAAA,UACzD;AAAA,QACF;AAED,YAAI,IAAI,cAAc;AACpB,gBAAM,OAAO;AAEb,mBAAS,IAAI,GAAG,IAAI,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK;AACvD,kBAAM,eAAe,IAAI,aAAa,CAAC;AAEvC,gBAAI,aAAa,UAAU;AAAS,oBAAM,OAAO,KAAK,KAAK;AAAA,UAC5D;AAAA,QACF;AAED,YAAI,IAAI,aAAa;AACnB,gBAAM,OAAO;AAEb,cAAI,YAAY,QAAQ,WAAY;AAClC,kBAAM,OAAO,KAAK,IAAI;AAAA,UAClC,CAAW;AAAA,QACF;AAED,eAAO;AAAA,MACR;AAED,aAAO,IAAI,iBAAkB;AAAA,IAC9B;AAED,aAAS,UAAUG,OAAM;AAGvB,YAAM,QAAQA,MAAK;AACnB,YAAMC,SAAQ,IAAIC,YAAO;AAIzB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,OAAO,MAAM,CAAC;AAEpB,qBAAa,IAAI;AAAA,MAClB;AAID,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,OAAO,MAAM,CAAC;AACpB,cAAM,SAAS,QAAQ,IAAI;AAE3B,YAAI,kBAAkBC,MAAQ;AAAE,UAAAF,OAAM,IAAI,MAAM;AAEhD,YAAI,KAAK,SAAS;AAAa,UAAAA,OAAM,SAAS,YAAY;AAAA,MAC3D;AAED,aAAOA;AAAA,IACR;AAED,aAAS,aAAa,MAAM;AAC1B,UAAI,KAAK,KAAK;AACZ,gBAAQ,KAAK,GAAG,IAAI;AAAA,MACrB;AAED,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AAEtB,YAAI,MAAM,SAAS,QAAQ;AACzB,gBAAM,cAAc,MAAM;AAE1B,mBAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,yBAAa,YAAY,CAAC,CAAC;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAED,aAAS,QAAQ,MAAM;AAGrB,UAAI,KAAK,KAAK;AACZ,eAAO,WAAW,KAAK,GAAG;AAAA,MAC3B;AAED,UAAI,KAAK,UAAU;AAAW,eAAO,KAAK;AAE1C,WAAK,QAAQ,UAAU,IAAI;AAE3B,aAAO,KAAK;AAAA,IACb;AAID,aAAS,UAAU,MAAM;AACvB,YAAM,WAAW,KAAK;AACtB,UAAI;AAEJ,cAAQ,UAAQ;AAAA,QACd,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,kBAAQ,kBAAkB,IAAI;AAC9B;AAAA,QAEF,KAAK;AACH,kBAAQ,oBAAoB,IAAI;AAChC;AAAA,QAEF,KAAK;AACH,kBAAQ,eAAe,IAAI;AAC3B;AAAA,QAEF,KAAK;AACH,kBAAQ,oBAAoB,IAAI;AAChC;AAAA,QAEF,KAAK;AACH,kBAAQ,kBAAkB,IAAI;AAC9B;AAAA,QAEF,KAAK;AACH,kBAAQ,sBAAsB,IAAI;AAClC;AAAA,QAEF,KAAK;AACH,kBAAQ,sBAAsB,IAAI;AAClC;AAAA,QAEF,KAAK;AACH,kBAAQ,0BAA0B,IAAI;AACtC;AAAA,QAEF,KAAK;AACH,kBAAQ,wBAAwB,IAAI;AACpC;AAAA,QAEF,KAAK;AACH,kBAAQ,wBAAwB,IAAI;AACpC;AAAA,QAEF,KAAK;AACH,kBAAQ,kBAAkB,IAAI;AAC9B;AAAA,QAEF,KAAK;AACH,kBAAQ,aAAa,IAAI;AACzB;AAAA,QAEF,KAAK;AACH,kBAAQ,cAAc,IAAI;AAC1B;AAAA,QAEF,KAAK;AACH,kBAAQ,kBAAkB,IAAI;AAC9B;AAAA,QAEF,KAAK;AACH,kBAAQ,gBAAgB,IAAI;AAC5B;AAAA,QAEF,KAAK;AACH,kBAAQ,uBAAuB,IAAI;AACnC;AAAA,QAEF,KAAK;AACH,kBAAQ,mBAAmB,IAAI;AAC/B;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,kBAAQ,mBAAmB,IAAI;AAC/B;AAAA,QAEF,KAAK;AACH,kBAAQ,mBAAmB,IAAI;AAC/B;AAAA,QAEF,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAEH;AAAA,QAEF;AACE,kBAAQ,KAAK,mCAAmC,QAAQ;AACxD;AAAA,MACH;AAED,UAAI,UAAU,UAAa,KAAK,QAAQ,UAAa,MAAM,eAAe,MAAM,MAAM,MAAM;AAC1F,cAAM,OAAO,KAAK;AAAA,MACnB;AAED,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,MAAM;AAC/B,YAAM,SAAS,IAAIG,YAAO;AAI1B,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,+BAAmB,aAAa,MAAM;AACtC;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,kBAAM,OAAO,IAAIC,cAAQ,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC,EAAE,UAAW;AACpF,kBAAM,QAAQ,YAAY,CAAC;AAC3B,mBAAO,WAAW,iBAAiB,MAAM,KAAK;AAC9C;AAAA,UAEF,KAAK;AACH,mBAAO,MAAM,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC/D;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,mBAAO,SAAS,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAClE;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,oBAAoB,MAAM;AACjC,YAAM,QAAQ,IAAID,YAAO;AAEzB,UAAI,aAAa;AACjB,UAAI,UAAU;AAEd,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,0BAAc;AACd;AAAA,UAEF,KAAK;AACH,0BAAc;AACd;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,uBAAW;AACX;AAAA,UAEF,KAAK;AACH,uBAAW;AACX;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,SAAS;AAIf,UAAI,UAAU;AACZ,cAAM,cAAc,IAAIE,MAAAA,eAAe,QAAQ,IAAI,EAAE;AACrD,cAAM,cAAc,IAAIC,wBAAkB,EAAE,KAAK,OAAO,MAAMC,MAAAA,UAAU,YAAY,OAAO,WAAW,MAAK,CAAE;AAE7G,YAAI,SAAS,SAAS,GAAG;AACvB,qBAAW,aAAa,QAAQ,UAAU,aAAa,QAAQ,GAAG,IAAI;AACtE,sBAAY,eAAe;AAAA,QACrC,OAAe;AACL,sBAAY,MAAM,OAAO,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,QAC/D;AAED,cAAM,MAAM,IAAIC,WAAK,aAAa,WAAW;AAC7C,cAAM,IAAI,GAAG;AAAA,MACd;AAID,UAAI,aAAa;AACf,YAAI,YAAY,SAAS,GAAG;AAC1B,gBAAM,iBAAiB,IAAIH,MAAc,eAAC,QAAQ,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,EAAE;AACtG,gBAAM,iBAAiB,IAAIC,wBAAkB;AAAA,YAC3C,KAAK;AAAA,YACL,MAAMC,MAAQ;AAAA,YACd,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,WAAW;AAAA,UACvB,CAAW;AAED,qBAAW,gBAAgB,QAAQ,aAAa,aAAa,WAAW,GAAG,KAAK;AAEhF,gBAAM,SAAS,IAAIC,WAAK,gBAAgB,cAAc;AACtD,gBAAM,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AAID,YAAM,cAAc;AAEpB,aAAO;AAAA,IACR;AAED,aAAS,eAAe,MAAM;AAC5B,YAAM,SAAS,KAAK;AAIpB,UAAI,WAAW,IAAIF,MAAAA,kBAAkB,EAAE,OAAO,EAAQ,CAAE;AACxD,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM,MAAM;AAC3B,yBAAW,QAAQ,YAAY,CAAC,CAAC;AAAA,YAClC;AAED;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM,MAAM;AAC3B,yBAAW,QAAQ,YAAY,CAAC,CAAC;AAAA,YAClC;AAED;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAID,UAAI;AAEJ,UAAI,YAAY,SAAS,WAAW,UAAU;AAC5C,cAAM,OAAO,SAAS;AAEtB,YAAI,SAAS,UAAU;AAGrB,gBAAM,iBAAiB,IAAIG,MAAAA,eAAe,EAAE,OAAO,SAAQ,CAAE;AAE7D,cAAI,SAAS,WAAW,UAAU,QAAW;AAC3C,2BAAe,eAAe;AAAA,UAC1C,OAAiB;AAGL,gBAAI,SAAS,qBAAqB;AAChC,6BAAe,MAAM,KAAK,SAAS,QAAQ;AAAA,YAC5C;AAAA,UACF;AAED,mBAAS,IAAIC,MAAAA,OAAO,UAAU,cAAc;AAAA,QACtD,WAAmB,SAAS,QAAQ;AAG1B,gBAAM,eAAe,IAAIC,MAAAA,kBAAkB,EAAE,OAAO,SAAQ,CAAE;AAE9D,cAAI,SAAS,WAAW,UAAU,QAAW;AAC3C,yBAAa,eAAe;AAAA,UACxC,OAAiB;AAGL,gBAAI,SAAS,qBAAqB;AAChC,2BAAa,MAAM,KAAK,SAAS,QAAQ;AAAA,YAC1C;AAAA,UACF;AAED,mBAAS,IAAIC,MAAAA,aAAa,UAAU,YAAY;AAAA,QAC1D,OAAe;AAKL,cAAI,SAAS,WAAW,QAAW;AACjC,qBAAS,OAAO,SAAS,SAASC,MAAS,YAAGC,MAAU;AAAA,UACzD;AAID,cAAI,SAAS,WAAW,UAAU,QAAW;AAC3C,qBAAS,eAAe;AAAA,UACzB;AAED,mBAAS,IAAIN,MAAAA,KAAK,UAAU,QAAQ;AAAA,QACrC;AAAA,MACT,OAAa;AACL,iBAAS,IAAIN,MAAAA,SAAU;AAIvB,eAAO,UAAU;AAAA,MAClB;AAED,aAAO;AAAA,IACR;AAED,aAAS,oBAAoB,MAAM;AACjC,UAAI,WAAW,IAAIa,wBAAmB;AACtC,UAAI;AAEJ,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM,MAAM;AAC3B,oBAAM,eAAe,QAAQ,YAAY,CAAC,CAAC;AAE3C,kBAAI,aAAa;AAAc,yBAAS,MAAM,KAAK,aAAa,YAAY;AAC5E,kBAAI,aAAa;AAAe,yBAAS,SAAS,KAAK,aAAa,aAAa;AACjF,kBAAI,aAAa;AAAW,yBAAS,YAAY,aAAa;AAC9D,kBAAI,aAAa;AAAe,yBAAS,SAAS,KAAK,aAAa,aAAa;AACjF,kBAAI,aAAa;AAAc,yBAAS,UAAU,IAAI,aAAa;AACnE,kBAAI,aAAa,eAAe;AAAG,yBAAS,cAAc;AAAA,YACxE,OAAmB;AAGL,yBAAW,IAAIT,MAAiB,kBAAC,EAAE,OAAO,EAAQ,CAAE;AAAA,YACrD;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,cAAc,YAAY,CAAC;AACjC,gBAAI,gBAAgB,MAAM;AACxB,kBAAI,YAAY,SAAS,kBAAkB,YAAY,SAAS,gBAAgB;AAC9E,yBAAS,MAAM,QAAQ,WAAW;AAAA,cAGnC;AAAA,YACF;AAED;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM,MAAM;AAC3B,8BAAgB,QAAQ,YAAY,CAAC,CAAC;AAAA,YACvC;AAED;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAID,UAAI,SAAS,KAAK;AAGhB,YAAI,SAAS,IAAI,QAAQ;AACvB,kBAAQ,SAAS,IAAI,QAAM;AAAA,YACzB,KAAK,aAAa;AAChB,uBAAS,UAAU;AACnB;AAAA,YAEF,KAAK,aAAa;AAChB,uBAAS,MAAM,IAAI,QAAQ;AAC3B;AAAA,YAEF,KAAK,aAAa;AAChB,uBAAS,MAAM,IAAI,QAAQ;AAC3B,uBAAS,UAAU;AACnB;AAAA,UAGH;AAED,iBAAO,SAAS,IAAI;AAAA,QACrB;AAID,YAAI,eAAe;AACjB,mBAAS,IAAI,OAAO,KAAK,cAAc,MAAM;AAC7C,mBAAS,IAAI,WAAW,cAAc;AACtC,mBAAS,IAAI,OAAO,KAAK,cAAc,KAAK;AAC5C,mBAAS,IAAI,OAAO,KAAK,cAAc,WAAW;AAAA,QACnD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,MAAM;AAC/B,YAAM,eAAe,CAAE;AAEvB,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,yBAAa,eAAe,IAAIU,MAAK,MAAC,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACpF;AAAA,UAEF,KAAK;AACH,yBAAa,gBAAgB,IAAIA,MAAK,MAAC,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACrF;AAAA,UAEF,KAAK;AACH,yBAAa,YAAY,YAAY,CAAC;AACtC;AAAA,UAEF,KAAK;AACH,yBAAa,gBAAgB,IAAIA,MAAK,MAAC,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACrF;AAAA,UAEF,KAAK;AACH,yBAAa,eAAe,YAAY,CAAC;AACzC;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,cAAc,KAAK,aAAa,OAAO;AAC9C,UAAI;AAEJ,cAAQ,aAAW;AAAA,QACjB,KAAK,aAAa;AAEhB,kBAAQ,SAAS,GAAG;AACpB,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV;AAAA,QAEF,KAAK,aAAa;AAEhB,kBAAQ,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC3C,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV,gBAAM,IAAI;AACV,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C;AAAA,QAEF,KAAK,aAAa;AAEhB,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI;AACV;AAAA,QAEF,KAAK,aAAa;AAEhB,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,CAAC,CAAC;AAC7C,gBAAM,IAAI,SAAS,OAAO,IAAI,UAAU,GAAG,EAAE,CAAC;AAC9C;AAAA,MAGH;AAAA,IACF;AAED,aAAS,eAAe,gBAAgB;AACtC,UAAI;AAEJ,cAAQ,gBAAc;AAAA,QACpB,KAAK;AACH,iBAAO,aAAa;AACpB;AAAA,QAEF,KAAK;AACH,iBAAO,aAAa;AACpB;AAAA,QAEF,KAAK;AACH,iBAAO,aAAa;AACpB;AAAA,QAEF,KAAK;AACH,iBAAO,aAAa;AACpB;AAAA,MAGH;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsB,MAAM;AACnC,UAAI;AACJ,UAAI,QAAQC,MAAc;AAC1B,UAAI,QAAQA,MAAc;AAE1B,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,QAAQ,YAAY,CAAC;AAC3B,kBAAM,SAAS,YAAY,CAAC;AAC5B,kBAAM,iBAAiB,YAAY,CAAC;AAEpC,kBAAM,cAAc,eAAe,cAAc;AAEjD,kBAAMrB,QAAO,IAAI,WAAW,IAAI,QAAQ,MAAM;AAE9C,kBAAM,QAAQ,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAG;AAExC,qBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK,KAAK;AAChE,4BAAc,YAAY,CAAC,GAAG,aAAa,KAAK;AAEhD,oBAAM,SAAS,IAAI;AAEnB,cAAAA,MAAK,SAAS,CAAC,IAAI,MAAM;AACzB,cAAAA,MAAK,SAAS,CAAC,IAAI,MAAM;AACzB,cAAAA,MAAK,SAAS,CAAC,IAAI,MAAM;AACzB,cAAAA,MAAK,SAAS,CAAC,IAAI,MAAM;AAAA,YAC1B;AAED,sBAAU,IAAIsB,MAAW,YAACtB,OAAM,OAAO,MAAM;AAC7C,oBAAQ,cAAc;AACtB,oBAAQ,SAAS;AACjB;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM;AAAO,sBAAQuB,MAAmB;AACzD;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM;AAAO,sBAAQA,MAAmB;AACzD;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,UAAI,SAAS;AACX,gBAAQ,QAAQ;AAChB,gBAAQ,QAAQ;AAAA,MACjB;AAED,aAAO;AAAA,IACR;AAED,aAAS,sBAAsB,MAAM;AACnC,UAAI;AACJ,UAAI,QAAQF,MAAc;AAC1B,UAAI,QAAQA,MAAc;AAE1B,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,MAAM,YAAY,CAAC;AACzB,gBAAI;AAAK,wBAAU,cAAc,KAAK,GAAG;AACzC;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM;AAAO,sBAAQE,MAAmB;AACzD;AAAA,UAEF,KAAK;AACH,gBAAI,YAAY,CAAC,MAAM;AAAO,sBAAQA,MAAmB;AACzD;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,UAAI,SAAS;AACX,gBAAQ,QAAQ;AAChB,gBAAQ,QAAQ;AAAA,MACjB;AAED,aAAO;AAAA,IACR;AAED,aAAS,0BAA0B,MAAM;AACvC,YAAM,gBAAgB;AAAA,QACpB,QAAQ,IAAIC,MAAAA,QAAS;AAAA,QACrB,UAAU,IAAIA,MAAAA,QAAS;AAAA,QACvB,OAAO,IAAIA,MAAAA,QAAS;AAAA,QACpB,aAAa,IAAIA,MAAAA,QAAS;AAAA,MAC3B;AAED,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,0BAAc,OAAO,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACvD;AAAA,UAEF,KAAK;AACH,0BAAc,WAAW,YAAY,CAAC;AACtC;AAAA,UAEF,KAAK;AACH,0BAAc,MAAM,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACtD;AAAA,UAEF,KAAK;AACH,0BAAc,YAAY,IAAI,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AAC5D;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,MAAM;AAChC,aAAO,KAAK,OAAO,CAAC,EAAE;AAAA,IACvB;AAED,aAAS,mBAAmB,MAAM;AAChC,YAAM,YAAY,CAAE;AAEpB,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,sBAAU,QAAQ,YAAY,CAAC;AAC/B;AAAA,UAEF,KAAK;AACH,sBAAU,OAAO;AACjB;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,wBAAwB,MAAM;AACrC,UAAI,OAAO,OAAO,QAAQ;AAC1B,UAAI,MAAM,MACR,QAAQ,MACR,cAAc;AAChB,UAAI,YAAY,YAAY,aAAa;AACzC,UAAI,iBAAiB,MACnB,kBAAkB;AAEpB,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,aAAa,YAAY,CAAC;AAEhC,gBAAI,eAAe,MAAM;AACvB,uBAAS,QAAQ,UAAU;AAAA,YAC5B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,eAAe,YAAY,CAAC;AAElC,gBAAI,iBAAiB,MAAM;AACzB,yBAAW,QAAQ,YAAY;AAAA,YAChC;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,CAAC;AACnB;AAAA,UAEF,KAAK;AACH,yBAAa;AACb;AAAA,UAEF,KAAK;AACH,6BAAiB,YAAY,CAAC;AAC9B;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,yBAAa;AACb;AAAA,UAEF,KAAK;AACH,0BAAc,YAAY,CAAC;AAC3B;AAAA,UAEF,KAAK;AACH,0BAAc;AACd;AAAA,UAEF,KAAK;AACH,8BAAkB,YAAY,CAAC;AAC/B;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,CAAC;AACrB;AAAA,UAEF,KAAK;AACH,4BAAgB;AAChB;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,UAAI,eAAe,QAAW;AAC5B,gBAAQ,KAAK,uCAAuC;AAEpD,eAAO,IAAIC,MAAAA,eAAgB;AAAA,MAC5B;AAED,YAAM,yBAAyB,qBAAqB,YAAY,GAAG;AAEnE,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO;AACT,YAAI,mBAAmB,MAAM;AAC3B,cAAI,cAAc,WAAW,SAAS,GAAG;AAGvC,kBAAM,yBAAyB,qBAAqB,YAAY,GAAG;AACnE,6BAAiB,gCAAgC,wBAAwB,wBAAwB,OAAO,CAAC;AAAA,UACrH,OAAiB;AAGL,6BAAiB,sBAAsB,wBAAwB,IAAIC,MAAAA,uBAAuB,OAAO,CAAC,CAAC;AAAA,UACpG;AAAA,QACX,OAAe;AACL,cAAI,cAAc,WAAW,SAAS,GAAG;AAGvC,kBAAM,oBAAoB,YAAY,OAAO,UAAU;AACvD,kBAAM,yBAAyB,oBAAoB,mBAAmB,UAAU;AAChF,6BAAiB,6BAA6B,wBAAwB,sBAAsB;AAAA,UACxG,OAAiB;AAGL,kBAAM,yBAAyB,oBAAoB,OAAO,UAAU;AACpE,6BAAiB,6BAA6B,wBAAwB,sBAAsB;AAAA,UAC7F;AAAA,QACF;AAAA,MACF;AAED,UAAI,QAAQ;AACV,YAAI,oBAAoB,MAAM;AAG5B,cAAI,eAAe,YAAY,SAAS,GAAG;AAGzC,kBAAM,0BAA0B,qBAAqB,aAAa,GAAG;AACrE,8BAAkB;AAAA,cAChB;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAAA,UACb,OAAiB;AAGL,8BAAkB,sBAAsB,wBAAwB,IAAIA,MAAAA,uBAAuB,QAAQ,CAAC,CAAC;AAAA,UACtG;AAAA,QACX,OAAe;AAGL,cAAI,eAAe,YAAY,SAAS,GAAG;AAGzC,kBAAM,qBAAqB,YAAY,QAAQ,WAAW;AAC1D,kBAAM,0BAA0B,oBAAoB,oBAAoB,UAAU;AAClF,8BAAkB,6BAA6B,wBAAwB,uBAAuB;AAAA,UAC1G,OAAiB;AAGL,kBAAM,0BAA0B,oBAAoB,QAAQ,UAAU;AACtE,8BAAkB,6BAA6B,wBAAwB,uBAAuB;AAAA,UAC/F;AAAA,QACF;AAAA,MACT,OAAa;AAGL,0BAAkB,uBAAuB,wBAAwB,OAAO,WAAW;AAAA,MACpF;AAED,UAAI,UAAU;AAGZ,YAAI,iBAAiB,cAAc,SAAS,GAAG;AAG7C,gBAAM,4BAA4B,qBAAqB,eAAe,GAAG;AACzE,wBAAc,gCAAgC,wBAAwB,2BAA2B,UAAU,CAAC;AAAA,QACtH,OAAe;AAGL,wBAAc,sBAAsB,wBAAwB,IAAIA,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AAAA,QACpG;AAAA,MACF;AAED,YAAM,WAAW,IAAID,qBAAgB;AACrC,YAAM,oBAAoB,sBAAsB,wBAAwB,IAAIC,MAAAA,uBAAuB,OAAO,CAAC,CAAC;AAE5G,eAAS,aAAa,YAAY,iBAAiB;AACnD,eAAS,aAAa,UAAU,eAAe;AAI/C,UAAI;AAAgB,iBAAS,aAAa,SAAS,cAAc;AACjE,UAAI;AAAa,iBAAS,aAAa,MAAM,WAAW;AAIxD,eAAS,SAAS;AAClB,eAAS,QAAQ;AAEjB,aAAO;AAAA,IACR;AAED,aAAS,wBAAwB,MAAM;AACrC,UAAI,OAAO;AACX,UAAI,YAAY;AAChB,UAAI,iBAAiB;AAErB,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,yBAAa;AACb;AAAA,UAEF,KAAK;AACH,6BAAiB,YAAY,CAAC;AAC9B;AAAA,UAEF,KAAK;AACH,yBAAa;AACb;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAID,UAAI;AAEJ,YAAM,oBAAoB,gBAAgB,UAAU;AAEpD,UAAI,OAAO;AACT,YAAI,mBAAmB,MAAM;AAC3B,cAAI,WAAW,SAAS,GAAG;AAGzB,kBAAM,qBAAqB,gBAAgB,UAAU;AACrD,6BAAiB,gCAAgC,mBAAmB,oBAAoB,OAAO,CAAC;AAAA,UAC5G,OAAiB;AAGL,6BAAiB,sBAAsB,mBAAmB,IAAIA,MAAAA,uBAAuB,OAAO,CAAC,CAAC;AAAA,UAC/F;AAAA,QACX,OAAe;AACL,cAAI,WAAW,SAAS,GAAG;AAGzB,kBAAM,oBAAoB,YAAY,OAAO,UAAU;AACvD,kBAAM,qBAAqB,eAAe,mBAAmB,UAAU;AACvE,6BAAiB,6BAA6B,mBAAmB,kBAAkB;AAAA,UAC/F,OAAiB;AAGL,kBAAM,qBAAqB,eAAe,OAAO,UAAU;AAC3D,6BAAiB,6BAA6B,mBAAmB,kBAAkB;AAAA,UACpF;AAAA,QACF;AAAA,MACF;AAID,YAAM,WAAW,IAAID,qBAAgB;AAErC,YAAM,oBAAoB,sBAAsB,mBAAmB,IAAIC,MAAAA,uBAAuB,OAAO,CAAC,CAAC;AACvG,eAAS,aAAa,YAAY,iBAAiB;AAEnD,UAAI;AAAgB,iBAAS,aAAa,SAAS,cAAc;AAEjE,eAAS,QAAQ;AAEjB,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,MAAM;AAC/B,UAAI,OAAO;AAEX,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,WAAW,IAAID,qBAAgB;AAErC,eAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,OAAO,CAAC,CAAC;AACtE,UAAI;AAAO,iBAAS,aAAa,SAAS,IAAIA,6BAAuB,OAAO,CAAC,CAAC;AAE9E,eAAS,QAAQ;AAEjB,aAAO;AAAA,IACR;AAED,aAAS,aAAa,MAAM;AAC1B,YAAM,OAAO,IAAIlB,MAAAA,QAAQ,GAAG,GAAG,CAAC;AAEhC,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,iBAAK,IAAI,YAAY,CAAC;AACtB,iBAAK,IAAI,YAAY,CAAC;AACtB,iBAAK,IAAI,YAAY,CAAC;AACtB;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,WAAW,IAAImB,MAAAA,YAAY,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAEvD,aAAO;AAAA,IACR;AAED,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,GACX,SAAS,GACT,YAAY;AAEd,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,wBAAY,CAAC,YAAY,CAAC;AAC1B;AAAA,UAEF,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,WAAW,IAAIC,mBAAa,QAAQ,QAAQ,IAAI,GAAG,SAAS;AAElE,aAAO;AAAA,IACR;AAED,aAAS,kBAAkB,MAAM;AAC/B,UAAI,SAAS,GACX,SAAS;AAEX,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,WAAW,IAAIC,uBAAiB,QAAQ,QAAQ,QAAQ,IAAI,CAAC;AAEnE,aAAO;AAAA,IACR;AAED,aAAS,gBAAgB,MAAM;AAC7B,UAAI,SAAS;AAEb,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,WAAW,IAAIpB,MAAAA,eAAe,QAAQ,IAAI,EAAE;AAElD,aAAO;AAAA,IACR;AAED,aAAS,uBAAuB,MAAM;AACpC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,iBAAiB;AACrB,UAAI,kBAAkB;AACtB,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,UAAI,cAAc;AAClB,UAAI,aAAa;AACjB,UAAI,aAAa;AACjB,UAAI,WAAW;AACf,UAAI,WAAW;AAEf,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,kBAAM,YAAY,YAAY,CAAC;AAE/B,gBAAI,cAAc,MAAM;AACtB,sBAAQ,QAAQ,SAAS;AAAA,YAC1B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,aAAa,YAAY,CAAC;AAEhC,gBAAI,eAAe,MAAM;AACvB,uBAAS,QAAQ,UAAU;AAAA,YAC5B;AAED;AAAA,UAEF,KAAK;AACH,kBAAM,eAAe,YAAY,CAAC;AAElC,gBAAI,iBAAiB,MAAM;AACzB,yBAAW,QAAQ,YAAY;AAAA,YAChC;AAED;AAAA,UAEF,KAAK;AACH,qBAAS;AACT;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,CAAC;AACnB;AAAA,UAEF,KAAK;AACH,6BAAiB,YAAY,CAAC;AAC9B;AAAA,UAEF,KAAK;AACH,0BAAc,YAAY,CAAC;AAC3B;AAAA,UAEF,KAAK;AACH,8BAAkB,YAAY,CAAC;AAC/B;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,CAAC;AACrB;AAAA,UAEF,KAAK;AACH,yBAAa,YAAY,CAAC;AAC1B;AAAA,UAEF,KAAK;AACH,uBAAW,YAAY,CAAC;AACxB;AAAA,UAEF,KAAK;AACH,yBAAa,YAAY,CAAC;AAC1B;AAAA,UAEF,KAAK;AACH,uBAAW,YAAY,CAAC;AACxB;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAID,YAAM,WAAW,CAAE;AACnB,YAAM,UAAU,CAAE;AAClB,YAAM,SAAS,CAAE;AACjB,YAAM,MAAM,CAAE;AAEd,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AAGnC,gBAAM,QAAQ,IAAI,aAAa;AAI/B,gBAAM,IAAI,WAAW;AACrB,gBAAM,IAAI,OAAO,KAAK;AACtB,gBAAM,IAAI,WAAW;AAErB,mBAAS,KAAK,GAAG,GAAG,CAAC;AAIrB,cAAI,SAAS,mBAAmB,MAAM;AACpC,kBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAC7B,kBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAC7B,kBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAE7B,mBAAO,KAAK,GAAG,GAAG,CAAC;AAAA,UACpB;AAID,cAAI,UAAU,oBAAoB,MAAM;AACtC,kBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAC/B,kBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAC/B,kBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAE/B,oBAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,UACxB;AAID,cAAI,UAAU;AACZ,kBAAM,IAAI,SAAS,QAAQ,IAAI,CAAC;AAChC,kBAAM,IAAI,SAAS,QAAQ,IAAI,CAAC;AAEhC,gBAAI,KAAK,GAAG,CAAC;AAAA,UACzB,OAAiB;AACL,gBAAI,KAAK,KAAK,aAAa,IAAI,KAAK,aAAa,EAAE;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAID,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,iBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AAGvC,gBAAM,IAAI,IAAI,IAAI;AAClB,gBAAM,IAAI,KAAK,IAAI,KAAK;AACxB,gBAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,gBAAM,IAAI,IAAI,IAAI,IAAI;AAItB,cAAI,QAAQ,MAAM;AAChB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,UAChC,OAAiB;AACL,oBAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAID,YAAM,oBAAoB,sBAAsB,SAAS,IAAIiB,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AAChG,YAAM,cAAc,sBAAsB,SAAS,IAAIA,MAAAA,uBAAuB,KAAK,CAAC,CAAC;AACrF,UAAI;AACJ,UAAI;AAIJ,UAAI,OAAO;AACT,YAAI,mBAAmB,OAAO;AAC5B,mBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,qBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,oBAAM,QAAQ,IAAI,KAAK,aAAa;AAEpC,oBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAC7B,oBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAC7B,oBAAM,IAAI,MAAM,QAAQ,IAAI,CAAC;AAI7B,qBAAO,KAAK,GAAG,GAAG,CAAC;AACnB,qBAAO,KAAK,GAAG,GAAG,CAAC;AACnB,qBAAO,KAAK,GAAG,GAAG,CAAC;AACnB,qBAAO,KAAK,GAAG,GAAG,CAAC;AACnB,qBAAO,KAAK,GAAG,GAAG,CAAC;AACnB,qBAAO,KAAK,GAAG,GAAG,CAAC;AAAA,YACpB;AAAA,UACF;AAED,2BAAiB,IAAIA,MAAAA,uBAAuB,QAAQ,CAAC;AAAA,QAC/D,OAAe;AACL,2BAAiB,sBAAsB,SAAS,IAAIA,MAAAA,uBAAuB,QAAQ,CAAC,CAAC;AAAA,QACtF;AAAA,MACF;AAID,UAAI,QAAQ;AACV,YAAI,oBAAoB,OAAO;AAC7B,mBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,qBAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,oBAAM,QAAQ,IAAI,KAAK,aAAa;AAEpC,oBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAC/B,oBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAC/B,oBAAM,KAAK,OAAO,QAAQ,IAAI,CAAC;AAI/B,sBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB,sBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB,sBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB,sBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB,sBAAQ,KAAK,IAAI,IAAI,EAAE;AACvB,sBAAQ,KAAK,IAAI,IAAI,EAAE;AAAA,YACxB;AAAA,UACF;AAED,4BAAkB,IAAIA,MAAAA,uBAAuB,SAAS,CAAC;AAAA,QACjE,OAAe;AACL,4BAAkB,sBAAsB,SAAS,IAAIA,MAAAA,uBAAuB,SAAS,CAAC,CAAC;AAAA,QACxF;AAAA,MACT,OAAa;AACL,0BAAkB,uBAAuB,SAAS,UAAU,WAAW;AAAA,MACxE;AAID,YAAM,WAAW,IAAID,qBAAgB;AACrC,eAAS,aAAa,YAAY,iBAAiB;AACnD,eAAS,aAAa,UAAU,eAAe;AAC/C,eAAS,aAAa,MAAM,WAAW;AAEvC,UAAI;AAAgB,iBAAS,aAAa,SAAS,cAAc;AAIjE,eAAS,SAAS;AAClB,eAAS,QAAQ;AAEjB,aAAO;AAAA,IACR;AAED,aAAS,mBAAmB,MAAM;AAChC,UAAI,eAAe,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC;AACpD,UAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,UAAI;AACJ,UAAI;AAEJ,UAAI,WAAW;AACf,UAAI,MAAM;AACV,UAAI,cAAc;AAClB,UAAI,SAAS;AACb,UAAI,QAAQ;AAEZ,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,cAAM,QAAQ,OAAO,CAAC;AACtB,cAAM,YAAY,MAAM;AACxB,cAAM,cAAc,MAAM;AAE1B,gBAAQ,WAAS;AAAA,UACf,KAAK;AACH,uBAAW,YAAY,CAAC;AACxB;AAAA,UAEF,KAAK;AACH,kBAAM,YAAY,CAAC;AACnB;AAAA,UAEF,KAAK;AAEH;AAAA,UAEF,KAAK;AACH,0BAAc,YAAY,CAAC;AAC3B;AAAA,UAEF,KAAK;AACH,2BAAe;AACf;AAAA,UAEF,KAAK;AACH,qBAAS,YAAY,CAAC;AACtB;AAAA,UAEF,KAAK;AACH,0BAAc;AACd;AAAA,UAEF,KAAK;AACH,oBAAQ;AACR;AAAA,UAEF,KAAK;AACH,oBAAQ,YAAY,CAAC;AACrB;AAAA,UAEF,KAAK;AACH,oBAAQ;AACR;AAAA,UAEF;AACE,oBAAQ,KAAK,oCAAoC,SAAS;AAC1D;AAAA,QACH;AAAA,MACF;AAED,YAAM,qBACJ,aAAa,CAAC,MAAM,aAAa,aAAa,SAAS,CAAC,KACxD,aAAa,CAAC,MAAM,aAAa,aAAa,SAAS,CAAC;AAI1D,YAAM,WAAW,CAAE;AACnB,YAAM,cAAc,IAAIjB,cAAS;AACjC,YAAM,UAAU,IAAIA,cAAS;AAE7B,YAAM,OAAO,IAAIA,cAAS;AAC1B,YAAM,SAAS,IAAIA,cAAS;AAC5B,YAAM,aAAa,IAAIsB,iBAAY;AAEnC,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG;AAC/E,oBAAY,UAAU,OAAO,CAAC;AAE9B,gBAAQ,IAAI,QAAQ,MAAM,IAAI,CAAC,IAAI;AACnC,gBAAQ,IAAI;AACZ,gBAAQ,IAAI,QAAQ,MAAM,IAAI,CAAC,IAAI;AAEnC,aAAK,IAAI,cAAc,YAAY,IAAI,CAAC,IAAI;AAC5C,aAAK,IAAI,cAAc,YAAY,IAAI,CAAC,IAAI;AAC5C,aAAK,IAAI,cAAc,YAAY,IAAI,CAAC,IAAI;AAC5C,cAAM,QAAQ,cAAc,YAAY,IAAI,CAAC,IAAI;AAEjD,iBAAS,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAK,GAAG;AACxD,iBAAO,IAAI,aAAa,IAAI,CAAC;AAC7B,iBAAO,IAAI;AACX,iBAAO,IAAI,aAAa,IAAI,CAAC;AAI7B,iBAAO,SAAS,OAAO;AAIvB,qBAAW,iBAAiB,MAAM,KAAK;AACvC,iBAAO,gBAAgB,UAAU;AAIjC,iBAAO,IAAI,WAAW;AAEtB,mBAAS,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,QAC3C;AAAA,MACF;AAID,YAAM,UAAU,CAAE;AAElB,YAAM,aAAa,MAAM,SAAS;AAClC,YAAM,oBAAoB,aAAa,SAAS;AAEhD,eAAS,IAAI,GAAG,IAAI,aAAa,GAAG,KAAK;AACvC,iBAAS,IAAI,GAAG,IAAI,oBAAoB,GAAG,KAAK;AAC9C,gBAAM,IAAI,IAAI,IAAI;AAClB,cAAI,IAAI,IAAI,IAAI,IAAI;AACpB,gBAAM,IAAI,KAAK,IAAI,KAAK;AACxB,cAAI,IAAI,IAAI,KAAK,IAAI,KAAK;AAE1B,cAAI,MAAM,oBAAoB,KAAK,uBAAuB,MAAM;AAC9D,gBAAI,IAAI;AACR,iBAAK,IAAI,KAAK;AAAA,UACf;AAED,cAAI,QAAQ,MAAM;AAChB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,UAChC,OAAiB;AACL,oBAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,oBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAID,UAAI,aAAa,QAAQ,WAAW,MAAM;AACxC,cAAM,UAAU,CAAE;AAElB,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK,GAAG;AACtD,kBAAQ,KAAK,IAAIN,MAAAA,QAAQ,aAAa,CAAC,GAAG,aAAa,IAAI,CAAC,CAAC,CAAC;AAAA,QAC/D;AAED,cAAM,QAAQO,MAAU,WAAC,iBAAiB,SAAS,CAAA,CAAE;AACrD,cAAM,aAAa,CAAE;AAErB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,gBAAM,OAAO,MAAM,CAAC;AAEpB,qBAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,QAC1C;AAID,YAAI,aAAa,MAAM;AACrB,mBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK,GAAG;AACpD,gBAAI,QAAQ,MAAM;AAChB,sBAAQ,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAAA,YAClF,OAAmB;AACL,sBAAQ,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAID,YAAI,WAAW,MAAM;AACnB,gBAAM,cAAc,qBAAqB,aAAa;AAEtD,mBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK,GAAG;AACpD,gBAAI,QAAQ,MAAM;AAChB,sBAAQ;AAAA,gBACN,cAAc,WAAW,IAAI,CAAC;AAAA,gBAC9B,cAAc,WAAW,IAAI,CAAC;AAAA,gBAC9B,cAAc,WAAW,IAAI,CAAC;AAAA,cAC/B;AAAA,YACf,OAAmB;AACL,sBAAQ;AAAA,gBACN,cAAc,WAAW,IAAI,CAAC;AAAA,gBAC9B,cAAc,WAAW,IAAI,CAAC;AAAA,gBAC9B,cAAc,WAAW,IAAI,CAAC;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAED,YAAM,oBAAoB,sBAAsB,SAAS,IAAIL,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AAChG,YAAM,kBAAkB,uBAAuB,SAAS,UAAU,WAAW;AAE7E,YAAM,WAAW,IAAID,qBAAgB;AACrC,eAAS,aAAa,YAAY,iBAAiB;AACnD,eAAS,aAAa,UAAU,eAAe;AAK/C,eAAS,SAAS;AAClB,eAAS,QAAQ;AAEjB,aAAO;AAAA,IACR;AAID,aAAS,WAAW,YAAY;AAC9B,YAAM,OAAO,QAAQ,UAAU;AAC/B,YAAM,QAAQ,QAAQ,IAAI;AAM1B,aAAO,MAAM,cAAc,MAAM,aAAa,MAAM,MAAK,IAAK;AAAA,IAC/D;AAED,aAAS,mBAAmB,UAAU,OAAO;AAC3C,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,cAAM,SAAS,QAAQ,SAAS,CAAC,CAAC;AAElC,YAAI,kBAAkBnB,MAAQ;AAAE,gBAAM,IAAI,MAAM;AAAA,MACjD;AAAA,IACF;AAED,aAAS,qBAAqB,OAAO,KAAK;AACxC,YAAM,UAAU,CAAE;AAKlB,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,KAAK,MAAM,KAAK;AACtB,cAAM,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE;AAClC,cAAM,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE;AAElC,gBAAQ,KAAK,IAAI,IAAI,EAAE;AAIvB,YAAI,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,KAAK,GAAG;AACrC,eAAK;AACL,kBAAQ,IAAI;AAAA,QACb;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,oBAAoBN,OAAM,OAAO;AACxC,YAAM,mBAAmB,CAAE;AAE3B,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,SAAS,QAAQ;AAEvB,cAAM,IAAIA,MAAK,MAAM;AACrB,cAAM,IAAIA,MAAK,SAAS,CAAC;AACzB,cAAM,IAAIA,MAAK,SAAS,CAAC;AAEzB,yBAAiB,KAAK,GAAG,GAAG,CAAC;AAI7B,YAAI,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,KAAK,GAAG;AACrC,eAAK;AACL;AAAA,QACD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,YAAYA,OAAM,OAAO;AAChC,YAAMgC,eAAc,CAAE;AAEtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,KAAK,MAAM,CAAC;AAElB,cAAM,SAAS,KAAK;AAEpB,cAAM,IAAIhC,MAAK,MAAM;AACrB,cAAM,IAAIA,MAAK,SAAS,CAAC;AACzB,cAAM,IAAIA,MAAK,SAAS,CAAC;AAEzB,QAAAgC,aAAY,KAAK,GAAG,GAAG,CAAC;AAAA,MACzB;AAED,aAAOA;AAAA,IACR;AAED,aAAS,gBAAgB,OAAO;AAC9B,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,KAAK,MAAM,CAAC;AAClB,cAAM,KAAK,MAAM,IAAI,CAAC;AAEtB,gBAAQ,KAAK,IAAI,EAAE;AAInB,YAAI,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,KAAK,GAAG;AACrC,eAAK;AAAA,QACN;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,aAAS,eAAehC,OAAM,OAAO;AACnC,YAAM,mBAAmB,CAAE;AAE3B,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,SAAS,QAAQ;AAEvB,cAAM,IAAIA,MAAK,MAAM;AACrB,cAAM,IAAIA,MAAK,SAAS,CAAC;AACzB,cAAM,IAAIA,MAAK,SAAS,CAAC;AAEzB,yBAAiB,KAAK,GAAG,GAAG,CAAC;AAI7B,YAAI,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,KAAK,GAAG;AACrC,eAAK;AACL;AAAA,QACD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,UAAM,KAAK,IAAIQ,cAAS;AACxB,UAAM,KAAK,IAAIA,cAAS;AACxB,UAAM,KAAK,IAAIA,cAAS;AAExB,UAAM,MAAM,IAAIgB,cAAS;AACzB,UAAM,MAAM,IAAIA,cAAS;AACzB,UAAM,MAAM,IAAIA,cAAS;AAEzB,aAAS,gCAAgC,YAAY,OAAOxB,OAAM,UAAU;AAC1E,YAAM,QAAQ,CAAE;AAIhB,eAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAK,GAAG;AACpD,cAAM,IAAI,MAAM,CAAC;AACjB,cAAM,IAAI,MAAM,IAAI,CAAC;AACrB,cAAM,IAAI,MAAM,IAAI,CAAC;AAErB,YAAI,aAAa,GAAG;AAClB,cAAI,UAAUA,OAAM,IAAI,QAAQ;AAChC,cAAI,UAAUA,OAAM,IAAI,QAAQ;AAChC,cAAI,UAAUA,OAAM,IAAI,QAAQ;AAEhC,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACvB,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACvB,gBAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,QACjC,OAAe;AACL,aAAG,UAAUA,OAAM,IAAI,QAAQ;AAC/B,aAAG,UAAUA,OAAM,IAAI,QAAQ;AAC/B,aAAG,UAAUA,OAAM,IAAI,QAAQ;AAE/B,gBAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,gBAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,gBAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,QAC5B;AAAA,MACF;AAED,aAAO,IAAI0B,MAAAA,uBAAuB,OAAO,QAAQ;AAAA,IAClD;AAED,aAAS,6BAA6B,OAAO,UAAU;AACrD,YAAM,QAAQ,CAAE;AAEhB,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3D,WAAG,UAAU,UAAU,IAAI,CAAC;AAE5B,cAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,cAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,cAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC5B;AAED,aAAO,IAAIA,MAAAA,uBAAuB,OAAO,CAAC;AAAA,IAC3C;AAED,aAAS,6BAA6B,OAAO,UAAU;AACrD,YAAM,QAAQ,CAAE;AAEhB,eAAS,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3D,WAAG,UAAU,UAAU,IAAI,CAAC;AAE5B,cAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,cAAM,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC5B;AAED,aAAO,IAAIA,MAAAA,uBAAuB,OAAO,CAAC;AAAA,IAC3C;AAED,aAAS,sBAAsB,SAAS,WAAW;AACjD,YAAM,QAAQ,UAAU;AACxB,YAAM,WAAW,UAAU;AAE3B,YAAM,SAAS,IAAI,MAAM,YAAY,QAAQ,SAAS,QAAQ;AAE9D,UAAI,QAAQ,GACV,SAAS;AAEX,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,gBAAQ,QAAQ,CAAC,IAAI;AAErB,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,iBAAO,QAAQ,IAAI,MAAM,OAAO;AAAA,QACjC;AAAA,MACF;AAED,aAAO,IAAIA,MAAAA,uBAAuB,QAAQ,QAAQ;AAAA,IACnD;AAED,UAAM,KAAK,IAAIlB,cAAS;AACxB,UAAM,KAAK,IAAIA,cAAS;AAExB,aAAS,uBAAuB,OAAO,OAAO,aAAa;AACzD,YAAM,QAAQ,CAAE;AAChB,YAAM,gBAAgB,CAAE;AAIxB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK,GAAG;AAC/C,cAAM,IAAI,MAAM,CAAC;AACjB,cAAM,IAAI,MAAM,IAAI,CAAC;AACrB,cAAM,IAAI,MAAM,IAAI,CAAC;AAErB,cAAM,OAAO,IAAI,KAAK,GAAG,GAAG,CAAC;AAE7B,WAAG,UAAU,OAAO,IAAI,CAAC;AACzB,WAAG,UAAU,OAAO,IAAI,CAAC;AACzB,WAAG,UAAU,OAAO,IAAI,CAAC;AAEzB,WAAG,WAAW,IAAI,EAAE;AACpB,WAAG,WAAW,IAAI,EAAE;AACpB,WAAG,MAAM,EAAE;AAEX,WAAG,UAAW;AAEd,aAAK,OAAO,KAAK,EAAE;AAEnB,YAAI,cAAc,CAAC,MAAM;AAAW,wBAAc,CAAC,IAAI,CAAE;AACzD,YAAI,cAAc,CAAC,MAAM;AAAW,wBAAc,CAAC,IAAI,CAAE;AACzD,YAAI,cAAc,CAAC,MAAM;AAAW,wBAAc,CAAC,IAAI,CAAE;AAEzD,sBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AACjC,sBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AACjC,sBAAc,CAAC,EAAE,KAAK,KAAK,MAAM;AAEjC,cAAM,KAAK,IAAI;AAAA,MAChB;AAID,YAAM,UAAU,CAAE;AAElB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,cAAM,OAAO,MAAM,CAAC;AAEpB,cAAM,KAAK,eAAe,cAAc,KAAK,CAAC,GAAG,KAAK,QAAQ,WAAW;AACzE,cAAM,KAAK,eAAe,cAAc,KAAK,CAAC,GAAG,KAAK,QAAQ,WAAW;AACzE,cAAM,KAAK,eAAe,cAAc,KAAK,CAAC,GAAG,KAAK,QAAQ,WAAW;AAEzE,WAAG,UAAU,OAAO,KAAK,IAAI,CAAC;AAC9B,WAAG,UAAU,OAAO,KAAK,IAAI,CAAC;AAC9B,WAAG,UAAU,OAAO,KAAK,IAAI,CAAC;AAE9B,gBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,gBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7B,gBAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,MAC9B;AAED,aAAO,IAAIkB,MAAAA,uBAAuB,SAAS,CAAC;AAAA,IAC7C;AAED,aAAS,eAAe,SAAS,QAAQ,aAAa;AACpD,YAAM,SAAS,IAAIlB,cAAS;AAE5B,UAAI,gBAAgB,GAAG;AACrB,eAAO,KAAK,MAAM;AAAA,MAC1B,OAAa;AACL,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAI,QAAQ,CAAC,EAAE,QAAQ,MAAM,IAAI,aAAa;AAC5C,mBAAO,IAAI,QAAQ,CAAC,CAAC;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAED,aAAO,OAAO,UAAW;AAAA,IAC1B;AAED,aAAS,aAAa,QAAQ;AAC5B,YAAM,QAAQ,CAAE;AAEhB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,GAAG;AAChD,cAAM,KAAK,IAAIY,MAAK,MAAC,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC;AAAA,MAC9D;AAED,aAAO;AAAA,IACR;AAwBD,aAAS,WAAW,UAAU,QAAQ,QAAQ,QAAQ,SAAS;AAG7D,YAAM,aAAa,CAAE;AACrB,YAAM,aAAa,YAAY,OAAO,IAAI,KAAK;AAE/C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,YAAI,QAAQ,MAAM,IAAI,IAAI,OAAO,IAAI,CAAC;AACtC,gBAAQ,YAAY,OAAO,QAAQ,aAAa;AAEhD,cAAM,QAAQ,IAAIZ,cAAS;AAC3B,cAAM,uBAAuB,QAAQ,OAAO,CAAC;AAE7C,mBAAW,KAAK,KAAK;AAAA,MACtB;AAID,YAAM,UAAU,SAAS;AACzB,YAAM,oBAAoB,SAAS,WAAW;AAC9C,YAAM,iBAAiB,IAAIyB,sBAAgB,IAAI,aAAa,SAAS,WAAW,SAAS,QAAQ,CAAC,GAAG,CAAC;AAEtG,YAAM,WAAW,IAAIzB,cAAS;AAC9B,YAAM,QAAQ,IAAIY,YAAO;AAEzB,eAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,KAAK;AACtC,cAAM,QAAQ,QAAQ,KAAK,CAAC;AAC5B,iBAAS,oBAAoB,mBAAmB,KAAK;AAErD,YAAI,iBAAiB;AACrB,YAAI,IAAI;AAER,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,4BAAkB,IAAI;AACtB,4BAAkB;AAElB,gBAAM,aAAa,WAAW,eAAe;AAC7C,gBAAM,aAAa,WAAW,eAAe;AAE7C,cAAI,YAAY,MAAM;AAGpB,gBAAI,SAAS,KAAK,WAAW,KAAK,SAAS,IAAI,WAAW,GAAG;AAC3D,kBAAI,KAAK,IAAI,WAAW,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,WAAW,IAAI,WAAW,CAAC;AAE9E;AAAA,YACD;AAAA,UACb,OAAiB;AAGL,gBAAI,SAAS,KAAK,WAAW,KAAK,SAAS,IAAI,WAAW,GAAG;AAC3D,kBAAI,KAAK,IAAI,WAAW,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,WAAW,IAAI,WAAW,CAAC;AAE9E;AAAA,YACD;AAAA,UACF;AAAA,QACF;AAED,cAAM,SAAS,OAAO,eAAe;AACrC,cAAM,SAAS,OAAO,eAAe;AAErC,cAAM,KAAK,MAAM,EAAE,KAAK,QAAQ,CAAC;AAEjC,uBAAe,OAAO,OAAO,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAAA,MACvD;AAED,eAAS,aAAa,SAAS,cAAc;AAAA,IAC9C;AAID,UAAM,gBAAgB,IAAIc,oBAAc,KAAK,OAAO;AACpD,kBAAc,QAAQ,KAAK,gBAAgB,IAAI,EAAE,eAAe,KAAK,WAAW;AAIhF,QAAI,KAAK,QAAQ,YAAY,MAAM,IAAI;AACrC,YAAM,MAAM,uDAAuD;AAAA,IACpE;AAID,UAAM,OAAO,iBAAiB,IAAI;AAIlC,UAAM,QAAQ,UAAU,IAAI;AAE5B,WAAO;AAAA,EACR;AACH;AAEA,MAAM,UAAU;AAAA,EACd,YAAY,QAAQ;AAClB,SAAK,QAAQ,IAAIhC,WAAK,MAAC,MAAM;AAAA,EAC9B;AAAA,EAED,IAAI,WAAW;AACb,UAAM,eAAe,KAAK,MAAM,SAAS,SAAS;AAElD,QAAI,aAAa,OAAO,SAAS,GAAG;AAClC,cAAQ,MAAM,aAAa,MAAM;AAEjC,YAAM,MAAM,0CAA0C;AAAA,IACvD;AAED,WAAO;AAAA,EACR;AACH;AAEA,MAAM,mBAAmBiC,WAAAA,UAAU;AAAA,EACjC,YAAY,iBAAiB;AAC3B,UAAM,eAAe;AAErB,UAAM,IAAI;AAEV,UAAM,UAAU,gBAAgB,SAAS;AACzC,UAAM,SAAS,gBAAgB,QAAQ;AACvC,UAAM,SAAS,gBAAgB,QAAQ;AACvC,UAAM,UAAU,gBAAgB,SAAS;AACzC,UAAM,UAAU,gBAAgB,SAAS;AACzC,UAAM,aAAa,gBAAgB,YAAY;AAC/C,UAAM,kBAAkB,gBAAgB,iBAAiB;AACzD,UAAM,gBAAgB,gBAAgB,eAAe;AACrD,UAAM,aAAa,gBAAgB,YAAY;AAC/C,UAAM,gBAAgB,gBAAgB,eAAe;AACrD,UAAM,cAAc,gBAAgB,aAAa;AACjD,UAAM,eAAe,gBAAgB,cAAc;AACnD,UAAM,cAAc,gBAAgB,aAAa;AACjD,UAAM,MAAM,gBAAgB,KAAK;AACjC,UAAM,MAAM,gBAAgB,KAAK;AACjC,UAAM,QAAQ,gBAAgB,OAAO;AACrC,UAAM,KAAK,gBAAgB,IAAI;AAC/B,UAAM,WAAW,gBAAgB,UAAU;AAE3C,MAAE,KAAK,QAAQ,WAAY;AACzB,QAAE,QAAQ,EAAE,OAAO;AACnB,QAAE,aAAa,WAAY;AACzB,UAAE,QAAQ,EAAE,IAAI;AAAA,MACxB,CAAO;AACD,QAAE,KAAK,WAAY;AACjB,UAAE,QAAQ,EAAE,KAAK;AAAA,MACzB,CAAO;AAAA,IACP,CAAK;AAED,MAAE,KAAK,WAAW,WAAY;AAC5B,QAAE,QAAQ,OAAO;AAAA,IACvB,CAAK;AAED,MAAE,KAAK,QAAQ,WAAY;AACzB,QAAE,OAAO,WAAY;AACnB,UAAE,QAAQ,EAAE,GAAG;AAAA,MACvB,CAAO;AAED,QAAE,QAAQ,QAAQ;AAClB,QAAE,QAAQ,MAAM;AAChB,QAAE,KAAK,WAAY;AACjB,UAAE,QAAQ,EAAE,KAAK;AAAA,MACzB,CAAO;AACD,QAAE,QAAQ,MAAM;AAAA,IACtB,CAAK;AAED,MAAE,KAAK,SAAS,WAAY;AAC1B,QAAE,QAAQ,UAAU;AAEpB,QAAE,IAAI;AAAA,QACJ;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,EAAE,gBAAgB;AAAA,UAC7B;AAAA,QACF;AAAA,QACD;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,EAAE,eAAe;AAAA,UAC5B;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAED,MAAE,KAAK,OAAO,WAAY;AACxB,QAAE,QAAQ,GAAG;AACb,QAAE,GAAG;AAAA,QACH;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,UAAU;AAAA,UACrB;AAAA,QACF;AAAA,QACD;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,QAAQ;AAAA,UACnB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAED,MAAE,KAAK,OAAO,WAAY;AACxB,QAAE,QAAQ,GAAG;AACb,QAAE,GAAG;AAAA,QACH;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,UAAU;AAAA,UACrB;AAAA,QACF;AAAA,QACD;AAAA,UACE,KAAK,WAAY;AACf,cAAE,QAAQ,QAAQ;AAAA,UACnB;AAAA,QACF;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAED,MAAE,KAAK,oBAAoB,WAAY;AACrC,QAAE,aAAa,WAAY;AACzB,UAAE,GAAG;AAAA,UACH;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,EAAE,IAAI;AAAA,YACjB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,EAAE,GAAG;AAAA,YAChB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,aAAa;AAAA,YACxB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,UAAU;AAAA,YACrB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,aAAa;AAAA,YACxB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,WAAW;AAAA,YACtB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,YAAY;AAAA,YACvB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,WAAW;AAAA,YACtB;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAED,MAAE,KAAK,mBAAmB,WAAY;AACpC,QAAE,QAAQ,OAAO;AACjB,QAAE,KAAK,WAAY;AACjB,UAAE,GAAG;AAAA,UACH;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,EAAE,IAAI;AAAA,YACjB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,EAAE,GAAG;AAAA,YAChB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,aAAa;AAAA,YACxB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,UAAU;AAAA,YACrB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,aAAa;AAAA,YACxB;AAAA,UACF;AAAA,UACD;AAAA,YACE,KAAK,WAAY;AACf,gBAAE,QAAQ,WAAW;AAAA,YACtB;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AACD,QAAE,QAAQ,OAAO;AAAA,IACvB,CAAK;AAED,MAAE,KAAK,SAAS,WAAY;AAC1B,QAAE,QAAQ,KAAK;AACf,QAAE,QAAQ,eAAe;AACzB,QAAE,QAAQ,EAAE;AACZ,QAAE,SAAS,eAAe;AAAA,IAChC,CAAK;AAED,SAAK,oBAAqB;AAAA,EAC3B;AACH;AAEA,MAAM,KAAK;AAAA,EACT,YAAY,GAAG,GAAG,GAAG;AACnB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,SAAS,IAAI3B,cAAS;AAAA,EAC5B;AACH;AAEA,MAAM,eAAe;AAAA,EACnB,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,KAAK;AAAA,EACL,MAAM;AACR;;"}