import React from 'react';
import type { NumberProps } from './number-types';
export declare function Number({ label, id, displayValue, onUpdate, onChange, settings, innerLabelTrim, }: Omit<NumberProps, 'setSettings' | 'emitOnEditStart' | 'emitOnEditEnd'> & {
    id?: string;
    label: string;
    innerLabelTrim?: number;
}): React.JSX.Element;
export declare function NumberComponent(): React.JSX.Element;
