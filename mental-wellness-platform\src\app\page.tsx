import Link from 'next/link'
import { HeartIcon, ChatBubbleLeftRightIcon, PuzzlePieceIcon, BookOpenIcon, PhoneIcon } from '@heroicons/react/24/outline'
import { APP_CONFIG } from '@/lib/constants'

const features = [
  {
    name: '3D Avatar Counselor',
    description: 'Interact with a realistic 3D AI counselor with facial expressions, voice, and human-like responses.',
    icon: ChatBubbleLeftRightIcon,
    href: '/avatar-chat',
    color: 'bg-purple-500'
  },
  {
    name: 'AI-Powered Chat Support',
    description: 'Get instant emotional support and guidance from our AI counselor, available 24/7.',
    icon: ChatBubbleLeftRightIcon,
    href: '/chat',
    color: 'bg-blue-500'
  },
  {
    name: 'Therapeutic Games',
    description: 'Fun, interactive games designed to help manage stress and build coping skills.',
    icon: PuzzlePieceIcon,
    href: '/games',
    color: 'bg-green-500'
  },
  {
    name: 'Educational Content',
    description: 'Videos, stories, and resources to learn about emotional intelligence and safety.',
    icon: BookOpenIcon,
    href: '/content',
    color: 'bg-purple-500'
  },
  {
    name: 'Crisis Resources',
    description: 'Quick access to helplines, emergency services, and local support organizations.',
    icon: PhoneIcon,
    href: '/resources',
    color: 'bg-red-500'
  },
]

export default function Home() {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <div className="flex justify-center mb-8">
              <HeartIcon className="h-16 w-16 text-pink-500" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Mental Wellness Support for{' '}
              <span className="text-pink-600">Women & Children</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              AI-powered emotional support, therapeutic games, safety monitoring, and educational resources
              designed specifically for women and children's mental wellness needs.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/avatar-chat"
                className="rounded-md bg-purple-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600"
              >
                Try 3D Avatar Chat
              </Link>
              <Link
                href="/chat"
                className="rounded-md bg-pink-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-pink-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600"
              >
                Start Chat Support
              </Link>
              <Link href="/resources" className="text-sm font-semibold leading-6 text-gray-900">
                Emergency Resources <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-pink-600">Comprehensive Support</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need for mental wellness
            </p>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Our platform combines AI technology with human expertise to provide safe, accessible,
              and effective mental health support tailored for women and children.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
              {features.map((feature) => (
                <div key={feature.name} className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className={`h-10 w-10 flex items-center justify-center rounded-lg ${feature.color}`}>
                      <feature.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                    {feature.name}
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">{feature.description}</p>
                    <p className="mt-6">
                      <Link
                        href={feature.href}
                        className="text-sm font-semibold leading-6 text-pink-600 hover:text-pink-500"
                      >
                        Learn more <span aria-hidden="true">→</span>
                      </Link>
                    </p>
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="bg-pink-50">
        <div className="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Need immediate help?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-600">
              If you're in crisis or need immediate support, don't hesitate to reach out.
              Help is available 24/7.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/resources"
                className="rounded-md bg-red-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
              >
                Emergency Resources
              </Link>
              <a href="tel:988" className="text-sm font-semibold leading-6 text-gray-900">
                Call 988 <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
