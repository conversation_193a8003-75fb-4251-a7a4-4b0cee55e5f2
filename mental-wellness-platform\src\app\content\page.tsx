'use client'

import { useState } from 'react'
import { PlayIcon, BookOpenIcon, ClockIcon, EyeIcon, HeartIcon, AcademicCapIcon } from '@heroicons/react/24/outline'
import { CONTENT_CATEGORIES, AGE_GROUPS } from '@/lib/constants'

interface ContentItem {
  id: string
  title: string
  type: 'video' | 'article' | 'comic' | 'story' | 'exercise'
  category: keyof typeof CONTENT_CATEGORIES
  ageGroup: keyof typeof AGE_GROUPS | 'all'
  duration?: number
  thumbnail: string
  description: string
  tags: string[]
  views: number
  rating: number
  featured: boolean
}

const contentItems: ContentItem[] = [
  {
    id: '1',
    title: 'Understanding Your Emotions',
    type: 'video',
    category: 'emotional_intelligence',
    ageGroup: 'child',
    duration: 8,
    thumbnail: '🎭',
    description: 'A fun animated video that helps children identify and understand different emotions.',
    tags: ['emotions', 'feelings', 'kids', 'animation'],
    views: 15420,
    rating: 4.8,
    featured: true
  },
  {
    id: '2',
    title: 'Breathing Exercises for Anxiety',
    type: 'exercise',
    category: 'coping_skills',
    ageGroup: 'all',
    duration: 5,
    thumbnail: '🫁',
    description: 'Simple breathing techniques to help manage anxiety and stress in daily life.',
    tags: ['breathing', 'anxiety', 'relaxation', 'mindfulness'],
    views: 23150,
    rating: 4.9,
    featured: true
  },
  {
    id: '3',
    title: 'Recognizing Safe vs Unsafe Situations',
    type: 'comic',
    category: 'safety',
    ageGroup: 'child',
    thumbnail: '🛡️',
    description: 'An interactive comic that teaches children how to identify safe and unsafe situations.',
    tags: ['safety', 'protection', 'awareness', 'comic'],
    views: 8930,
    rating: 4.7,
    featured: false
  },
  {
    id: '4',
    title: 'Building Self-Confidence',
    type: 'article',
    category: 'wellness',
    ageGroup: 'teen',
    thumbnail: '💪',
    description: 'Practical strategies for teenagers to build self-confidence and positive self-image.',
    tags: ['confidence', 'self-esteem', 'teens', 'empowerment'],
    views: 12680,
    rating: 4.6,
    featured: false
  },
  {
    id: '5',
    title: 'The Worry Monster Story',
    type: 'story',
    category: 'emotional_intelligence',
    ageGroup: 'child',
    duration: 12,
    thumbnail: '👹',
    description: 'A gentle story about a child who learns to tame their worry monster with help and courage.',
    tags: ['worry', 'anxiety', 'story', 'coping'],
    views: 19240,
    rating: 4.8,
    featured: true
  },
  {
    id: '6',
    title: 'Mindfulness for Busy Moms',
    type: 'video',
    category: 'wellness',
    ageGroup: 'adult',
    duration: 15,
    thumbnail: '🧘‍♀️',
    description: 'Quick mindfulness practices that busy mothers can incorporate into their daily routine.',
    tags: ['mindfulness', 'mothers', 'stress-relief', 'self-care'],
    views: 31200,
    rating: 4.9,
    featured: true
  },
  {
    id: '7',
    title: 'Healthy Boundaries Guide',
    type: 'article',
    category: 'safety',
    ageGroup: 'teen',
    thumbnail: '🚧',
    description: 'Understanding and setting healthy boundaries in relationships and social situations.',
    tags: ['boundaries', 'relationships', 'safety', 'teens'],
    views: 14560,
    rating: 4.7,
    featured: false
  },
  {
    id: '8',
    title: 'Progressive Muscle Relaxation',
    type: 'exercise',
    category: 'coping_skills',
    ageGroup: 'all',
    duration: 20,
    thumbnail: '🧘',
    description: 'A guided progressive muscle relaxation exercise to reduce tension and promote calm.',
    tags: ['relaxation', 'stress-relief', 'muscle', 'guided'],
    views: 18750,
    rating: 4.8,
    featured: false
  }
]

export default function ContentPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedAgeGroup, setSelectedAgeGroup] = useState<string>('all')

  const filteredContent = contentItems.filter(item => {
    const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory
    const typeMatch = selectedType === 'all' || item.type === selectedType
    const ageMatch = selectedAgeGroup === 'all' || item.ageGroup === selectedAgeGroup || item.ageGroup === 'all'
    return categoryMatch && typeMatch && ageMatch
  })

  const featuredContent = contentItems.filter(item => item.featured)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return PlayIcon
      case 'article': return BookOpenIcon
      case 'comic': return EyeIcon
      case 'story': return BookOpenIcon
      case 'exercise': return HeartIcon
      default: return AcademicCapIcon
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video': return 'bg-red-100 text-red-800'
      case 'article': return 'bg-blue-100 text-blue-800'
      case 'comic': return 'bg-purple-100 text-purple-800'
      case 'story': return 'bg-green-100 text-green-800'
      case 'exercise': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Educational Content
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Videos, articles, stories, and interactive content to learn about emotional intelligence, 
            safety, and mental wellness for women and children.
          </p>
        </div>

        {/* Featured Content */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Content</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredContent.map((item) => {
              const TypeIcon = getTypeIcon(item.type)
              return (
                <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-4xl">{item.thumbnail}</div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                        {item.type}
                      </span>
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {item.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-4 text-sm">
                      {item.description}
                    </p>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      {item.duration && (
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {item.duration} min
                        </div>
                      )}
                      <div className="flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        {item.views.toLocaleString()}
                      </div>
                    </div>

                    <button className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center">
                      <TypeIcon className="h-4 w-4 mr-2" />
                      View Content
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">All Categories</option>
                {Object.entries(CONTENT_CATEGORIES).map(([key, label]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">All Types</option>
                <option value="video">Videos</option>
                <option value="article">Articles</option>
                <option value="comic">Comics</option>
                <option value="story">Stories</option>
                <option value="exercise">Exercises</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Age Group
              </label>
              <select
                value={selectedAgeGroup}
                onChange={(e) => setSelectedAgeGroup(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">All Ages</option>
                {Object.entries(AGE_GROUPS).map(([key, group]) => (
                  <option key={key} value={key}>{group.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* All Content */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">All Content</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContent.map((item) => {
              const TypeIcon = getTypeIcon(item.type)
              return (
                <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-3xl">{item.thumbnail}</div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>
                        {item.type}
                      </span>
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {item.title}
                    </h3>

                    <p className="text-gray-600 mb-4 text-sm">
                      {item.description}
                    </p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {item.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      {item.duration && (
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {item.duration} min
                        </div>
                      )}
                      <div className="flex items-center">
                        <EyeIcon className="h-4 w-4 mr-1" />
                        {item.views.toLocaleString()}
                      </div>
                    </div>

                    <button className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center">
                      <TypeIcon className="h-4 w-4 mr-2" />
                      View Content
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {filteredContent.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No content found for the selected filters.</p>
          </div>
        )}

        {/* Learning Paths */}
        <div className="mt-16 bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Learning Paths</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="border border-gray-200 rounded-lg p-6">
              <div className="text-3xl mb-4">🌱</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Emotional Basics for Kids</h3>
              <p className="text-gray-600 text-sm mb-4">
                A structured path to help children understand and express emotions healthily.
              </p>
              <div className="text-sm text-gray-500 mb-4">5 lessons • Ages 5-12</div>
              <button className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                Start Learning Path
              </button>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <div className="text-3xl mb-4">🛡️</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Safety & Protection</h3>
              <p className="text-gray-600 text-sm mb-4">
                Essential safety knowledge and protection strategies for teens and adults.
              </p>
              <div className="text-sm text-gray-500 mb-4">7 lessons • Ages 13+</div>
              <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                Start Learning Path
              </button>
            </div>

            <div className="border border-gray-200 rounded-lg p-6">
              <div className="text-3xl mb-4">💪</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Building Resilience</h3>
              <p className="text-gray-600 text-sm mb-4">
                Develop coping skills and emotional resilience for life's challenges.
              </p>
              <div className="text-sm text-gray-500 mb-4">6 lessons • All ages</div>
              <button className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors">
                Start Learning Path
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
