{"name": "@react-three/postprocessing", "version": "3.0.4", "description": "postprocessing wrapper for React and @react-three/fiber", "keywords": ["postprocessing", "react", "three", "@react-three/fiber", "webgl", "3d"], "license": "MIT", "files": ["dist", "src"], "type": "module", "main": "./dist/index.js", "exports": "./dist/index.js", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/pmndrs/react-postprocessing.git"}, "scripts": {"build": "rimraf dist && vite build && tsc", "eslint": "eslint . --fix --ext=js,ts,jsx,tsx", "eslint:ci": "eslint . --ext=js,ts,jsx,tsx", "test": "vitest run", "typecheck": "tsc --noEmit"}, "dependencies": {"maath": "^0.6.0", "n8ao": "^1.9.4", "postprocessing": "^6.36.6"}, "devDependencies": {"@react-three/fiber": "^9.0.4", "@types/node": "^22.10.7", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/three": "^0.156.0", "@typescript-eslint/eslint-plugin": "^5.59.1", "@typescript-eslint/parser": "^5.59.1", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^6.0.1", "three": "^0.156.0", "typescript": "^5.0.4", "vite": "^4.3.5", "vitest": "^2.1.8"}, "peerDependencies": {"@react-three/fiber": "^9.0.0", "react": "^19.0", "three": ">= 0.156.0"}}