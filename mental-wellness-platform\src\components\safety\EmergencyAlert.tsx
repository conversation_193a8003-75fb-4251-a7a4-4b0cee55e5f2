'use client'

import { useState } from 'react'
import { ExclamationTriangleIcon, PhoneIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { EMERGENCY_CONTACTS } from '@/lib/constants'
import Button from '@/components/ui/Button'

interface EmergencyAlertProps {
  riskLevel?: 'low' | 'medium' | 'high' | 'critical'
  message?: string
  onClose?: () => void
  autoShow?: boolean
}

export default function EmergencyAlert({ 
  riskLevel = 'medium', 
  message, 
  onClose,
  autoShow = false 
}: EmergencyAlertProps) {
  const [isVisible, setIsVisible] = useState(autoShow)

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  const getRiskConfig = (level: string) => {
    switch (level) {
      case 'critical':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-400',
          textColor: 'text-red-800',
          iconColor: 'text-red-400',
          title: 'IMMEDIATE HELP NEEDED',
          urgency: 'Call 911 or emergency services right now'
        }
      case 'high':
        return {
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-400',
          textColor: 'text-orange-800',
          iconColor: 'text-orange-400',
          title: 'Crisis Support Available',
          urgency: 'Please reach out for immediate support'
        }
      case 'medium':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-400',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-400',
          title: 'Support Resources',
          urgency: 'Help is available when you need it'
        }
      default:
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-400',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-400',
          title: 'Resources Available',
          urgency: 'Support is here for you'
        }
    }
  }

  const config = getRiskConfig(riskLevel)

  if (!isVisible && !autoShow) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="danger"
          className="shadow-lg"
        >
          <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
          Emergency Help
        </Button>
      </div>
    )
  }

  return (
    <div className={`${config.bgColor} border-l-4 ${config.borderColor} p-6 mb-6`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon className={`h-6 w-6 ${config.iconColor}`} />
        </div>
        <div className="ml-3 flex-1">
          <div className="flex items-center justify-between">
            <h3 className={`text-lg font-medium ${config.textColor}`}>
              {config.title}
            </h3>
            {onClose && (
              <button
                onClick={handleClose}
                className={`${config.textColor} hover:opacity-75`}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>
          
          <p className={`mt-2 text-sm ${config.textColor}`}>
            {message || config.urgency}
          </p>

          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {riskLevel === 'critical' ? (
              <div className="col-span-full">
                <div className="bg-red-100 border border-red-300 rounded-lg p-4">
                  <h4 className="font-bold text-red-900 mb-2">CALL IMMEDIATELY:</h4>
                  <div className="space-y-2">
                    <a
                      href="tel:911"
                      className="flex items-center text-red-900 font-bold text-lg hover:underline"
                    >
                      <PhoneIcon className="h-5 w-5 mr-2" />
                      911 - Emergency Services
                    </a>
                    <a
                      href="tel:988"
                      className="flex items-center text-red-900 font-bold hover:underline"
                    >
                      <PhoneIcon className="h-5 w-5 mr-2" />
                      988 - Crisis Lifeline
                    </a>
                  </div>
                </div>
              </div>
            ) : (
              EMERGENCY_CONTACTS.slice(0, 2).map((contact, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-1">{contact.name}</h4>
                  <a
                    href={`tel:${contact.phone}`}
                    className="text-blue-600 hover:text-blue-800 font-mono text-sm block mb-1"
                  >
                    {contact.phone}
                  </a>
                  <p className="text-xs text-gray-600">{contact.description}</p>
                </div>
              ))
            )}
          </div>

          <div className="mt-4 flex flex-wrap gap-2">
            <Button
              size="sm"
              onClick={() => window.open('/resources', '_blank')}
              variant="outline"
            >
              View All Resources
            </Button>
            <Button
              size="sm"
              onClick={() => window.open('/chat', '_blank')}
              variant="outline"
            >
              Chat Support
            </Button>
            {riskLevel !== 'critical' && (
              <Button
                size="sm"
                onClick={handleClose}
                variant="ghost"
              >
                I'm Safe Now
              </Button>
            )}
          </div>

          <div className="mt-4 text-xs text-gray-600">
            <p>
              <strong>Remember:</strong> This platform provides support but is not a substitute for professional medical care. 
              If you're in immediate danger, please call emergency services.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
