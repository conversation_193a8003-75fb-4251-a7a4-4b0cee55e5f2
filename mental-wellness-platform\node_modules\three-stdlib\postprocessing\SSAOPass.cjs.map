{"version": 3, "file": "SSAOPass.cjs", "sources": ["../../src/postprocessing/SSAOPass.js"], "sourcesContent": ["import {\n  AddEquation,\n  Color,\n  CustomBlending,\n  DataTexture,\n  DepthTexture,\n  DstAlphaFactor,\n  DstColorFactor,\n  FloatType,\n  MathUtils,\n  MeshNormalMaterial,\n  NearestFilter,\n  NoBlending,\n  RedFormat,\n  DepthStencilFormat,\n  UnsignedInt248Type,\n  RepeatWrapping,\n  ShaderMaterial,\n  UniformsUtils,\n  Vector3,\n  WebGLRenderTarget,\n  ZeroFactor,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { SimplexNoise } from '../math/SimplexNoise'\nimport { SSAOShader, SSAOBlurShader, SSAODepthShader } from '../shaders/SSAOShader'\n\nimport { CopyShader } from '../shaders/CopyShader'\n\nconst SSAOPass = /* @__PURE__ */ (() => {\n  class SSAOPass extends Pass {\n    static OUTPUT = {\n      Default: 0,\n      SSAO: 1,\n      Blur: 2,\n      Beauty: 3,\n      Depth: 4,\n      Normal: 5,\n    }\n\n    constructor(scene, camera, width, height) {\n      super()\n\n      this.width = width !== undefined ? width : 512\n      this.height = height !== undefined ? height : 512\n\n      this.clear = true\n\n      this.camera = camera\n      this.scene = scene\n\n      this.kernelRadius = 8\n      this.kernelSize = 32\n      this.kernel = []\n      this.noiseTexture = null\n      this.output = 0\n\n      this.minDistance = 0.005\n      this.maxDistance = 0.1\n\n      this._visibilityCache = new Map()\n\n      //\n\n      this.generateSampleKernel()\n      this.generateRandomKernelRotations()\n\n      // beauty render target\n\n      const depthTexture = new DepthTexture()\n      depthTexture.format = DepthStencilFormat\n      depthTexture.type = UnsignedInt248Type\n\n      this.beautyRenderTarget = new WebGLRenderTarget(this.width, this.height)\n\n      // normal render target with depth buffer\n\n      this.normalRenderTarget = new WebGLRenderTarget(this.width, this.height, {\n        minFilter: NearestFilter,\n        magFilter: NearestFilter,\n        depthTexture: depthTexture,\n      })\n\n      // ssao render target\n\n      this.ssaoRenderTarget = new WebGLRenderTarget(this.width, this.height)\n\n      this.blurRenderTarget = this.ssaoRenderTarget.clone()\n\n      // ssao material\n\n      if (SSAOShader === undefined) {\n        console.error('THREE.SSAOPass: The pass relies on SSAOShader.')\n      }\n\n      this.ssaoMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOShader.defines),\n        uniforms: UniformsUtils.clone(SSAOShader.uniforms),\n        vertexShader: SSAOShader.vertexShader,\n        fragmentShader: SSAOShader.fragmentShader,\n        blending: NoBlending,\n      })\n\n      this.ssaoMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n      this.ssaoMaterial.uniforms['tNormal'].value = this.normalRenderTarget.texture\n      this.ssaoMaterial.uniforms['tDepth'].value = this.normalRenderTarget.depthTexture\n      this.ssaoMaterial.uniforms['tNoise'].value = this.noiseTexture\n      this.ssaoMaterial.uniforms['kernel'].value = this.kernel\n      this.ssaoMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.ssaoMaterial.uniforms['cameraFar'].value = this.camera.far\n      this.ssaoMaterial.uniforms['resolution'].value.set(this.width, this.height)\n      this.ssaoMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssaoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      // normal material\n\n      this.normalMaterial = new MeshNormalMaterial()\n      this.normalMaterial.blending = NoBlending\n\n      // blur material\n\n      this.blurMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAOBlurShader.defines),\n        uniforms: UniformsUtils.clone(SSAOBlurShader.uniforms),\n        vertexShader: SSAOBlurShader.vertexShader,\n        fragmentShader: SSAOBlurShader.fragmentShader,\n      })\n      this.blurMaterial.uniforms['tDiffuse'].value = this.ssaoRenderTarget.texture\n      this.blurMaterial.uniforms['resolution'].value.set(this.width, this.height)\n\n      // material for rendering the depth\n\n      this.depthRenderMaterial = new ShaderMaterial({\n        defines: Object.assign({}, SSAODepthShader.defines),\n        uniforms: UniformsUtils.clone(SSAODepthShader.uniforms),\n        vertexShader: SSAODepthShader.vertexShader,\n        fragmentShader: SSAODepthShader.fragmentShader,\n        blending: NoBlending,\n      })\n      this.depthRenderMaterial.uniforms['tDepth'].value = this.normalRenderTarget.depthTexture\n      this.depthRenderMaterial.uniforms['cameraNear'].value = this.camera.near\n      this.depthRenderMaterial.uniforms['cameraFar'].value = this.camera.far\n\n      // material for rendering the content of a render target\n\n      this.copyMaterial = new ShaderMaterial({\n        uniforms: UniformsUtils.clone(CopyShader.uniforms),\n        vertexShader: CopyShader.vertexShader,\n        fragmentShader: CopyShader.fragmentShader,\n        transparent: true,\n        depthTest: false,\n        depthWrite: false,\n        blendSrc: DstColorFactor,\n        blendDst: ZeroFactor,\n        blendEquation: AddEquation,\n        blendSrcAlpha: DstAlphaFactor,\n        blendDstAlpha: ZeroFactor,\n        blendEquationAlpha: AddEquation,\n      })\n\n      this.fsQuad = new FullScreenQuad(null)\n\n      this.originalClearColor = new Color()\n    }\n\n    dispose() {\n      // dispose render targets\n\n      this.beautyRenderTarget.dispose()\n      this.normalRenderTarget.dispose()\n      this.ssaoRenderTarget.dispose()\n      this.blurRenderTarget.dispose()\n\n      // dispose materials\n\n      this.normalMaterial.dispose()\n      this.blurMaterial.dispose()\n      this.copyMaterial.dispose()\n      this.depthRenderMaterial.dispose()\n\n      // dipsose full screen quad\n\n      this.fsQuad.dispose()\n    }\n\n    render(renderer, writeBuffer /*, readBuffer, deltaTime, maskActive */) {\n      // render beauty\n\n      renderer.setRenderTarget(this.beautyRenderTarget)\n      renderer.clear()\n      renderer.render(this.scene, this.camera)\n\n      // render normals and depth (honor only meshes, points and lines do not contribute to SSAO)\n\n      this.overrideVisibility()\n      this.renderOverride(renderer, this.normalMaterial, this.normalRenderTarget, 0x7777ff, 1.0)\n      this.restoreVisibility()\n\n      // render SSAO\n\n      this.ssaoMaterial.uniforms['kernelRadius'].value = this.kernelRadius\n      this.ssaoMaterial.uniforms['minDistance'].value = this.minDistance\n      this.ssaoMaterial.uniforms['maxDistance'].value = this.maxDistance\n      this.renderPass(renderer, this.ssaoMaterial, this.ssaoRenderTarget)\n\n      // render blur\n\n      this.renderPass(renderer, this.blurMaterial, this.blurRenderTarget)\n\n      // output result to screen\n\n      switch (this.output) {\n        case SSAOPass.OUTPUT.SSAO:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.ssaoRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Blur:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Beauty:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Depth:\n          this.renderPass(renderer, this.depthRenderMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Normal:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.normalRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        case SSAOPass.OUTPUT.Default:\n          this.copyMaterial.uniforms['tDiffuse'].value = this.beautyRenderTarget.texture\n          this.copyMaterial.blending = NoBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          this.copyMaterial.uniforms['tDiffuse'].value = this.blurRenderTarget.texture\n          this.copyMaterial.blending = CustomBlending\n          this.renderPass(renderer, this.copyMaterial, this.renderToScreen ? null : writeBuffer)\n\n          break\n\n        default:\n          console.warn('THREE.SSAOPass: Unknown output type.')\n      }\n    }\n\n    renderPass(renderer, passMaterial, renderTarget, clearColor, clearAlpha) {\n      // save original state\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n\n      // setup pass state\n      renderer.autoClear = false\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.fsQuad.material = passMaterial\n      this.fsQuad.render(renderer)\n\n      // restore original state\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    renderOverride(renderer, overrideMaterial, renderTarget, clearColor, clearAlpha) {\n      renderer.getClearColor(this.originalClearColor)\n      const originalClearAlpha = renderer.getClearAlpha()\n      const originalAutoClear = renderer.autoClear\n\n      renderer.setRenderTarget(renderTarget)\n      renderer.autoClear = false\n\n      clearColor = overrideMaterial.clearColor || clearColor\n      clearAlpha = overrideMaterial.clearAlpha || clearAlpha\n\n      if (clearColor !== undefined && clearColor !== null) {\n        renderer.setClearColor(clearColor)\n        renderer.setClearAlpha(clearAlpha || 0.0)\n        renderer.clear()\n      }\n\n      this.scene.overrideMaterial = overrideMaterial\n      renderer.render(this.scene, this.camera)\n      this.scene.overrideMaterial = null\n\n      // restore original state\n\n      renderer.autoClear = originalAutoClear\n      renderer.setClearColor(this.originalClearColor)\n      renderer.setClearAlpha(originalClearAlpha)\n    }\n\n    setSize(width, height) {\n      this.width = width\n      this.height = height\n\n      this.beautyRenderTarget.setSize(width, height)\n      this.ssaoRenderTarget.setSize(width, height)\n      this.normalRenderTarget.setSize(width, height)\n      this.blurRenderTarget.setSize(width, height)\n\n      this.ssaoMaterial.uniforms['resolution'].value.set(width, height)\n      this.ssaoMaterial.uniforms['cameraProjectionMatrix'].value.copy(this.camera.projectionMatrix)\n      this.ssaoMaterial.uniforms['cameraInverseProjectionMatrix'].value.copy(this.camera.projectionMatrixInverse)\n\n      this.blurMaterial.uniforms['resolution'].value.set(width, height)\n    }\n\n    generateSampleKernel() {\n      const kernelSize = this.kernelSize\n      const kernel = this.kernel\n\n      for (let i = 0; i < kernelSize; i++) {\n        const sample = new Vector3()\n        sample.x = Math.random() * 2 - 1\n        sample.y = Math.random() * 2 - 1\n        sample.z = Math.random()\n\n        sample.normalize()\n\n        let scale = i / kernelSize\n        scale = MathUtils.lerp(0.1, 1, scale * scale)\n        sample.multiplyScalar(scale)\n\n        kernel.push(sample)\n      }\n    }\n\n    generateRandomKernelRotations() {\n      const width = 4,\n        height = 4\n\n      if (SimplexNoise === undefined) {\n        console.error('THREE.SSAOPass: The pass relies on SimplexNoise.')\n      }\n\n      const simplex = new SimplexNoise()\n\n      const size = width * height\n      const data = new Float32Array(size)\n\n      for (let i = 0; i < size; i++) {\n        const x = Math.random() * 2 - 1\n        const y = Math.random() * 2 - 1\n        const z = 0\n\n        data[i] = simplex.noise3d(x, y, z)\n      }\n\n      this.noiseTexture = new DataTexture(data, width, height, RedFormat, FloatType)\n      this.noiseTexture.wrapS = RepeatWrapping\n      this.noiseTexture.wrapT = RepeatWrapping\n      this.noiseTexture.needsUpdate = true\n    }\n\n    overrideVisibility() {\n      const scene = this.scene\n      const cache = this._visibilityCache\n\n      scene.traverse(function (object) {\n        cache.set(object, object.visible)\n\n        if (object.isPoints || object.isLine) object.visible = false\n      })\n    }\n\n    restoreVisibility() {\n      const scene = this.scene\n      const cache = this._visibilityCache\n\n      scene.traverse(function (object) {\n        const visible = cache.get(object)\n        object.visible = visible\n      })\n\n      cache.clear()\n    }\n  }\n\n  return SSAOPass\n})()\n\nexport { SSAOPass }\n"], "names": ["Pass", "DepthTexture", "DepthStencilFormat", "UnsignedInt248Type", "WebGLRenderTarget", "NearestFilter", "SSA<PERSON><PERSON><PERSON>", "ShaderMaterial", "UniformsUtils", "NoBlending", "MeshNormalMaterial", "SSAOB<PERSON>r<PERSON><PERSON><PERSON>", "SSAOD<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DstColorFactor", "ZeroFactor", "AddEquation", "DstAlphaFactor", "FullScreenQuad", "Color", "CustomBlending", "Vector3", "MathUtils", "SimplexNoise", "DataTexture", "RedFormat", "FloatType", "RepeatWrapping", "SSAOPass"], "mappings": ";;;;;;;;;;;;;AA6BK,MAAC,WAA4B,uBAAM;AACtC,QAAM,YAAN,cAAuBA,KAAAA,KAAK;AAAA,IAU1B,YAAY,OAAO,QAAQ,OAAO,QAAQ;AACxC,YAAO;AAEP,WAAK,QAAQ,UAAU,SAAY,QAAQ;AAC3C,WAAK,SAAS,WAAW,SAAY,SAAS;AAE9C,WAAK,QAAQ;AAEb,WAAK,SAAS;AACd,WAAK,QAAQ;AAEb,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,SAAS,CAAE;AAChB,WAAK,eAAe;AACpB,WAAK,SAAS;AAEd,WAAK,cAAc;AACnB,WAAK,cAAc;AAEnB,WAAK,mBAAmB,oBAAI,IAAK;AAIjC,WAAK,qBAAsB;AAC3B,WAAK,8BAA+B;AAIpC,YAAM,eAAe,IAAIC,mBAAc;AACvC,mBAAa,SAASC,MAAkB;AACxC,mBAAa,OAAOC,MAAkB;AAEtC,WAAK,qBAAqB,IAAIC,MAAiB,kBAAC,KAAK,OAAO,KAAK,MAAM;AAIvE,WAAK,qBAAqB,IAAIA,MAAiB,kBAAC,KAAK,OAAO,KAAK,QAAQ;AAAA,QACvE,WAAWC,MAAa;AAAA,QACxB,WAAWA,MAAa;AAAA,QACxB;AAAA,MACR,CAAO;AAID,WAAK,mBAAmB,IAAID,MAAiB,kBAAC,KAAK,OAAO,KAAK,MAAM;AAErE,WAAK,mBAAmB,KAAK,iBAAiB,MAAO;AAIrD,UAAIE,WAAAA,eAAe,QAAW;AAC5B,gBAAQ,MAAM,gDAAgD;AAAA,MAC/D;AAED,WAAK,eAAe,IAAIC,qBAAe;AAAA,QACrC,SAAS,OAAO,OAAO,CAAA,GAAID,WAAAA,WAAW,OAAO;AAAA,QAC7C,UAAUE,MAAa,cAAC,MAAMF,WAAAA,WAAW,QAAQ;AAAA,QACjD,cAAcA,WAAU,WAAC;AAAA,QACzB,gBAAgBA,WAAU,WAAC;AAAA,QAC3B,UAAUG,MAAU;AAAA,MAC5B,CAAO;AAED,WAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,WAAK,aAAa,SAAS,SAAS,EAAE,QAAQ,KAAK,mBAAmB;AACtE,WAAK,aAAa,SAAS,QAAQ,EAAE,QAAQ,KAAK,mBAAmB;AACrE,WAAK,aAAa,SAAS,QAAQ,EAAE,QAAQ,KAAK;AAClD,WAAK,aAAa,SAAS,QAAQ,EAAE,QAAQ,KAAK;AAClD,WAAK,aAAa,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AAC7D,WAAK,aAAa,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAC5D,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,MAAM;AAC1E,WAAK,aAAa,SAAS,wBAAwB,EAAE,MAAM,KAAK,KAAK,OAAO,gBAAgB;AAC5F,WAAK,aAAa,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AAI1G,WAAK,iBAAiB,IAAIC,yBAAoB;AAC9C,WAAK,eAAe,WAAWD,MAAU;AAIzC,WAAK,eAAe,IAAIF,qBAAe;AAAA,QACrC,SAAS,OAAO,OAAO,CAAA,GAAII,WAAAA,eAAe,OAAO;AAAA,QACjD,UAAUH,MAAa,cAAC,MAAMG,WAAAA,eAAe,QAAQ;AAAA,QACrD,cAAcA,WAAc,eAAC;AAAA,QAC7B,gBAAgBA,WAAc,eAAC;AAAA,MACvC,CAAO;AACD,WAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACrE,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,KAAK,OAAO,KAAK,MAAM;AAI1E,WAAK,sBAAsB,IAAIJ,qBAAe;AAAA,QAC5C,SAAS,OAAO,OAAO,CAAA,GAAIK,WAAAA,gBAAgB,OAAO;AAAA,QAClD,UAAUJ,MAAa,cAAC,MAAMI,WAAAA,gBAAgB,QAAQ;AAAA,QACtD,cAAcA,WAAe,gBAAC;AAAA,QAC9B,gBAAgBA,WAAe,gBAAC;AAAA,QAChC,UAAUH,MAAU;AAAA,MAC5B,CAAO;AACD,WAAK,oBAAoB,SAAS,QAAQ,EAAE,QAAQ,KAAK,mBAAmB;AAC5E,WAAK,oBAAoB,SAAS,YAAY,EAAE,QAAQ,KAAK,OAAO;AACpE,WAAK,oBAAoB,SAAS,WAAW,EAAE,QAAQ,KAAK,OAAO;AAInE,WAAK,eAAe,IAAIF,qBAAe;AAAA,QACrC,UAAUC,MAAa,cAAC,MAAMK,WAAAA,WAAW,QAAQ;AAAA,QACjD,cAAcA,WAAU,WAAC;AAAA,QACzB,gBAAgBA,WAAU,WAAC;AAAA,QAC3B,aAAa;AAAA,QACb,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,UAAUC,MAAc;AAAA,QACxB,UAAUC,MAAU;AAAA,QACpB,eAAeC,MAAW;AAAA,QAC1B,eAAeC,MAAc;AAAA,QAC7B,eAAeF,MAAU;AAAA,QACzB,oBAAoBC,MAAW;AAAA,MACvC,CAAO;AAED,WAAK,SAAS,IAAIE,KAAc,eAAC,IAAI;AAErC,WAAK,qBAAqB,IAAIC,YAAO;AAAA,IACtC;AAAA,IAED,UAAU;AAGR,WAAK,mBAAmB,QAAS;AACjC,WAAK,mBAAmB,QAAS;AACjC,WAAK,iBAAiB,QAAS;AAC/B,WAAK,iBAAiB,QAAS;AAI/B,WAAK,eAAe,QAAS;AAC7B,WAAK,aAAa,QAAS;AAC3B,WAAK,aAAa,QAAS;AAC3B,WAAK,oBAAoB,QAAS;AAIlC,WAAK,OAAO,QAAS;AAAA,IACtB;AAAA,IAED,OAAO,UAAU,aAAsD;AAGrE,eAAS,gBAAgB,KAAK,kBAAkB;AAChD,eAAS,MAAO;AAChB,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAIvC,WAAK,mBAAoB;AACzB,WAAK,eAAe,UAAU,KAAK,gBAAgB,KAAK,oBAAoB,SAAU,CAAG;AACzF,WAAK,kBAAmB;AAIxB,WAAK,aAAa,SAAS,cAAc,EAAE,QAAQ,KAAK;AACxD,WAAK,aAAa,SAAS,aAAa,EAAE,QAAQ,KAAK;AACvD,WAAK,aAAa,SAAS,aAAa,EAAE,QAAQ,KAAK;AACvD,WAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAIlE,WAAK,WAAW,UAAU,KAAK,cAAc,KAAK,gBAAgB;AAIlE,cAAQ,KAAK,QAAM;AAAA,QACjB,KAAK,UAAS,OAAO;AACnB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACrE,eAAK,aAAa,WAAWV,MAAU;AACvC,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,UAAS,OAAO;AACnB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACrE,eAAK,aAAa,WAAWA,MAAU;AACvC,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,UAAS,OAAO;AACnB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,WAAWA,MAAU;AACvC,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,UAAS,OAAO;AACnB,eAAK,WAAW,UAAU,KAAK,qBAAqB,KAAK,iBAAiB,OAAO,WAAW;AAE5F;AAAA,QAEF,KAAK,UAAS,OAAO;AACnB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,WAAWA,MAAU;AACvC,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF,KAAK,UAAS,OAAO;AACnB,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,mBAAmB;AACvE,eAAK,aAAa,WAAWA,MAAU;AACvC,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF,eAAK,aAAa,SAAS,UAAU,EAAE,QAAQ,KAAK,iBAAiB;AACrE,eAAK,aAAa,WAAWW,MAAc;AAC3C,eAAK,WAAW,UAAU,KAAK,cAAc,KAAK,iBAAiB,OAAO,WAAW;AAErF;AAAA,QAEF;AACE,kBAAQ,KAAK,sCAAsC;AAAA,MACtD;AAAA,IACF;AAAA,IAED,WAAW,UAAU,cAAc,cAAc,YAAY,YAAY;AAEvE,eAAS,cAAc,KAAK,kBAAkB;AAC9C,YAAM,qBAAqB,SAAS,cAAe;AACnD,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AAGrC,eAAS,YAAY;AACrB,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,OAAO,WAAW;AACvB,WAAK,OAAO,OAAO,QAAQ;AAG3B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,eAAe,UAAU,kBAAkB,cAAc,YAAY,YAAY;AAC/E,eAAS,cAAc,KAAK,kBAAkB;AAC9C,YAAM,qBAAqB,SAAS,cAAe;AACnD,YAAM,oBAAoB,SAAS;AAEnC,eAAS,gBAAgB,YAAY;AACrC,eAAS,YAAY;AAErB,mBAAa,iBAAiB,cAAc;AAC5C,mBAAa,iBAAiB,cAAc;AAE5C,UAAI,eAAe,UAAa,eAAe,MAAM;AACnD,iBAAS,cAAc,UAAU;AACjC,iBAAS,cAAc,cAAc,CAAG;AACxC,iBAAS,MAAO;AAAA,MACjB;AAED,WAAK,MAAM,mBAAmB;AAC9B,eAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AACvC,WAAK,MAAM,mBAAmB;AAI9B,eAAS,YAAY;AACrB,eAAS,cAAc,KAAK,kBAAkB;AAC9C,eAAS,cAAc,kBAAkB;AAAA,IAC1C;AAAA,IAED,QAAQ,OAAO,QAAQ;AACrB,WAAK,QAAQ;AACb,WAAK,SAAS;AAEd,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,iBAAiB,QAAQ,OAAO,MAAM;AAC3C,WAAK,mBAAmB,QAAQ,OAAO,MAAM;AAC7C,WAAK,iBAAiB,QAAQ,OAAO,MAAM;AAE3C,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,OAAO,MAAM;AAChE,WAAK,aAAa,SAAS,wBAAwB,EAAE,MAAM,KAAK,KAAK,OAAO,gBAAgB;AAC5F,WAAK,aAAa,SAAS,+BAA+B,EAAE,MAAM,KAAK,KAAK,OAAO,uBAAuB;AAE1G,WAAK,aAAa,SAAS,YAAY,EAAE,MAAM,IAAI,OAAO,MAAM;AAAA,IACjE;AAAA,IAED,uBAAuB;AACrB,YAAM,aAAa,KAAK;AACxB,YAAM,SAAS,KAAK;AAEpB,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAM,SAAS,IAAIC,cAAS;AAC5B,eAAO,IAAI,KAAK,OAAQ,IAAG,IAAI;AAC/B,eAAO,IAAI,KAAK,OAAQ,IAAG,IAAI;AAC/B,eAAO,IAAI,KAAK,OAAQ;AAExB,eAAO,UAAW;AAElB,YAAI,QAAQ,IAAI;AAChB,gBAAQC,MAAS,UAAC,KAAK,KAAK,GAAG,QAAQ,KAAK;AAC5C,eAAO,eAAe,KAAK;AAE3B,eAAO,KAAK,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,IAED,gCAAgC;AAC9B,YAAM,QAAQ,GACZ,SAAS;AAEX,UAAIC,aAAAA,iBAAiB,QAAW;AAC9B,gBAAQ,MAAM,kDAAkD;AAAA,MACjE;AAED,YAAM,UAAU,IAAIA,0BAAc;AAElC,YAAM,OAAO,QAAQ;AACrB,YAAM,OAAO,IAAI,aAAa,IAAI;AAElC,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAM,IAAI,KAAK,OAAQ,IAAG,IAAI;AAC9B,cAAM,IAAI,KAAK,OAAQ,IAAG,IAAI;AAC9B,cAAM,IAAI;AAEV,aAAK,CAAC,IAAI,QAAQ,QAAQ,GAAG,GAAG,CAAC;AAAA,MAClC;AAED,WAAK,eAAe,IAAIC,kBAAY,MAAM,OAAO,QAAQC,MAAS,WAAEC,eAAS;AAC7E,WAAK,aAAa,QAAQC,MAAc;AACxC,WAAK,aAAa,QAAQA,MAAc;AACxC,WAAK,aAAa,cAAc;AAAA,IACjC;AAAA,IAED,qBAAqB;AACnB,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AAEnB,YAAM,SAAS,SAAU,QAAQ;AAC/B,cAAM,IAAI,QAAQ,OAAO,OAAO;AAEhC,YAAI,OAAO,YAAY,OAAO;AAAQ,iBAAO,UAAU;AAAA,MAC/D,CAAO;AAAA,IACF;AAAA,IAED,oBAAoB;AAClB,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AAEnB,YAAM,SAAS,SAAU,QAAQ;AAC/B,cAAM,UAAU,MAAM,IAAI,MAAM;AAChC,eAAO,UAAU;AAAA,MACzB,CAAO;AAED,YAAM,MAAO;AAAA,IACd;AAAA,EACF;AAjXD,MAAMC,YAAN;AACE,gBADIA,WACG,UAAS;AAAA,IACd,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AA2WH,SAAOA;AACT,GAAC;;"}