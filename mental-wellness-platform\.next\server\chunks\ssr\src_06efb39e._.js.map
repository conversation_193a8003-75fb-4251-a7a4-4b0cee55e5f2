{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/[<>]/g, '')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,OAAO,CAAC,uDAAuD,IACzE,OAAO,CAAC,SAAS,IACjB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline'\nimport { generateId } from '@/lib/utils'\nimport VoiceChat from '@/components/chatbot/VoiceChat'\n\ninterface Message {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n  isTyping?: boolean\n  typedContent?: string\n  safetyAlert?: {\n    riskLevel: string\n    keywords: string[]\n  }\n  language?: 'en' | 'ta'\n}\n\nexport default function ChatPage() {\n  const [messages, setMessages] = useState<Message[]>([])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [language, setLanguage] = useState<'en' | 'ta'>('en')\n  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const typingTimeoutRef = useRef<NodeJS.Timeout>()\n\n  // Initialize with welcome message\n  useEffect(() => {\n    const welcomeMessage = {\n      id: generateId(),\n      role: 'assistant' as const,\n      content: language === 'ta'\n        ? 'வணக்கம்! நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்? இது ஒரு பாதுகாப்பான இடம், இங்கே நீங்கள் உங்கள் மனதில் உள்ளதை பகிர்ந்து கொள்ளலாம்.'\n        : 'Hello! I\\'m here to provide emotional support and guidance. How are you feeling today? Remember, this is a safe space where you can share what\\'s on your mind.',\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages([welcomeMessage])\n\n    // Auto-speak welcome message\n    setTimeout(() => {\n      if (speakResponse) {\n        speakResponse(welcomeMessage.content, language)\n      }\n    }, 1000)\n  }, [language, speakResponse])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  // Typing effect for assistant messages\n  const typeMessage = useCallback((message: Message) => {\n    const words = message.content.split(' ')\n    let currentIndex = 0\n\n    const typeNextWord = () => {\n      if (currentIndex < words.length) {\n        const typedContent = words.slice(0, currentIndex + 1).join(' ')\n\n        setMessages(prev => prev.map(msg =>\n          msg.id === message.id\n            ? { ...msg, typedContent, isTyping: true }\n            : msg\n        ))\n\n        currentIndex++\n        typingTimeoutRef.current = setTimeout(typeNextWord, 100 + Math.random() * 100)\n      } else {\n        setMessages(prev => prev.map(msg =>\n          msg.id === message.id\n            ? { ...msg, isTyping: false, typedContent: message.content }\n            : msg\n        ))\n\n        // Auto-speak the response\n        if (speakResponse && message.role === 'assistant') {\n          setTimeout(() => {\n            speakResponse(message.content, message.language || language)\n          }, 500)\n        }\n      }\n    }\n\n    typeNextWord()\n  }, [speakResponse, language])\n\n  // Handle voice transcript\n  const handleVoiceTranscript = useCallback((transcript: string) => {\n    setInputMessage(transcript)\n    // Auto-send voice messages\n    setTimeout(() => {\n      sendMessage(transcript)\n    }, 500)\n  }, [])\n\n  // Handle voice response setup\n  const handleSpeakResponse = useCallback((speakFn: any) => {\n    setSpeakResponse(() => speakFn)\n  }, [])\n\n  const sendMessage = async (messageText?: string) => {\n    const textToSend = messageText || inputMessage.trim()\n    if (!textToSend || isLoading) return\n\n    const userMessage: Message = {\n      id: generateId(),\n      role: 'user',\n      content: textToSend,\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    // Show typing indicator\n    const typingMessage: Message = {\n      id: generateId(),\n      role: 'assistant',\n      content: language === 'ta' ? 'தட்டச்சு செய்கிறது...' : 'Typing...',\n      timestamp: new Date().toISOString(),\n      isTyping: true,\n      language\n    }\n\n    setMessages(prev => [...prev, typingMessage])\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: textToSend,\n          sessionId: generateId(),\n          userId: 'anonymous',\n          language\n        }),\n      })\n\n      const data = await response.json()\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      if (data.success) {\n        let responseContent = data.data.content\n\n        // Translate response if needed (basic translation for demo)\n        if (language === 'ta' && !responseContent.includes('தமிழ்')) {\n          // Add Tamil translation for common responses\n          const translations: { [key: string]: string } = {\n            'I understand': 'நான் புரிந்துகொள்கிறேன்',\n            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',\n            'I\\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',\n            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',\n            'That sounds difficult': 'அது கடினமாக இருக்கும்',\n            'You\\'re not alone': 'நீங்கள் தனியாக இல்லை'\n          }\n\n          Object.entries(translations).forEach(([en, ta]) => {\n            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)\n          })\n        }\n\n        const assistantMessage: Message = {\n          id: generateId(),\n          role: 'assistant',\n          content: responseContent,\n          timestamp: data.data.timestamp,\n          safetyAlert: data.data.safetyAlert,\n          language,\n          isTyping: true,\n          typedContent: ''\n        }\n\n        setMessages(prev => [...prev, assistantMessage])\n\n        // Start typing effect\n        setTimeout(() => {\n          typeMessage(assistantMessage)\n        }, 500)\n\n      } else {\n        throw new Error(data.error || 'Failed to get response')\n      }\n    } catch (error) {\n      console.error('Error sending message:', error)\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      const errorMessage: Message = {\n        id: generateId(),\n        role: 'assistant',\n        content: language === 'ta'\n          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும், அல்லது இது அவசரநிலை என்றால், 911 அல்லது நெருக்கடி ஹாட்லைனை தொடர்பு கொள்ளவும்.'\n          : 'I apologize, but I\\'m having trouble responding right now. Please try again, or if this is an emergency, please call 911 or contact a crisis hotline.',\n        timestamp: new Date().toISOString(),\n        language\n      }\n      setMessages(prev => [...prev, errorMessage])\n      typeMessage(errorMessage)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-4 h-[calc(100vh-200px)]\">\n      <div className=\"bg-white rounded-lg shadow-lg h-full flex flex-col\">\n        {/* Header */}\n        <div className=\"bg-pink-600 text-white p-4 rounded-t-lg\">\n          <h1 className=\"text-xl font-semibold\">AI Mental Health Support</h1>\n          <p className=\"text-pink-100 text-sm\">\n            Safe, confidential emotional support • Available 24/7\n          </p>\n        </div>\n\n        {/* Emergency Notice */}\n        <div className=\"bg-red-50 border-l-4 border-red-400 p-4\">\n          <div className=\"flex\">\n            <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-700\">\n                <strong>Emergency:</strong> If you're in immediate danger, call 911. \n                For crisis support, call 988 or text HOME to 741741.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div\n                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                  message.role === 'user'\n                    ? 'bg-pink-600 text-white'\n                    : 'bg-gray-100 text-gray-900'\n                }`}\n              >\n                {message.safetyAlert && (\n                  <div className={`mb-2 p-2 rounded text-xs ${\n                    message.safetyAlert.riskLevel === 'critical' \n                      ? 'bg-red-100 text-red-800 border border-red-200'\n                      : message.safetyAlert.riskLevel === 'high'\n                      ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                      : 'bg-yellow-100 text-yellow-800 border border-yellow-200'\n                  }`}>\n                    ⚠️ Safety alert detected - {message.safetyAlert.riskLevel} risk\n                  </div>\n                )}\n                <p className=\"whitespace-pre-wrap\">{message.content}</p>\n                <p className={`text-xs mt-1 ${\n                  message.role === 'user' ? 'text-pink-200' : 'text-gray-500'\n                }`}>\n                  {new Date(message.timestamp).toLocaleTimeString()}\n                </p>\n              </div>\n            </div>\n          ))}\n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg\">\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                </div>\n              </div>\n            </div>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Input */}\n        <div className=\"border-t p-4\">\n          <div className=\"flex space-x-2\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Share what's on your mind... (Press Enter to send)\"\n              className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none\"\n              rows={2}\n              disabled={isLoading}\n            />\n            <button\n              onClick={sendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              className=\"bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <PaperAirplaneIcon className=\"h-5 w-5\" />\n            </button>\n          </div>\n          <p className=\"text-xs text-gray-500 mt-2\">\n            This AI provides support but is not a substitute for professional therapy or medical care.\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IACvG,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE9B,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAClB,gNACA;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY;YAAC;SAAe;QAE5B,6BAA6B;QAC7B,WAAW;YACT,IAAI,eAAe;gBACjB,cAAc,eAAe,OAAO,EAAE;YACxC;QACF,GAAG;IACL,GAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,CAAC;QACpC,IAAI,eAAe;QAEnB,MAAM,eAAe;YACnB,IAAI,eAAe,MAAM,MAAM,EAAE;gBAC/B,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,eAAe,GAAG,IAAI,CAAC;gBAE3D,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE;4BAAc,UAAU;wBAAK,IACvC;gBAGN;gBACA,iBAAiB,OAAO,GAAG,WAAW,cAAc,MAAM,KAAK,MAAM,KAAK;YAC5E,OAAO;gBACL,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE,UAAU;4BAAO,cAAc,QAAQ,OAAO;wBAAC,IACzD;gBAGN,0BAA0B;gBAC1B,IAAI,iBAAiB,QAAQ,IAAI,KAAK,aAAa;oBACjD,WAAW;wBACT,cAAc,QAAQ,OAAO,EAAE,QAAQ,QAAQ,IAAI;oBACrD,GAAG;gBACL;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,gBAAgB;QAChB,2BAA2B;QAC3B,WAAW;YACT,YAAY;QACd,GAAG;IACL,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,iBAAiB,IAAM;IACzB,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,MAAM,aAAa,eAAe,aAAa,IAAI;QACnD,IAAI,CAAC,cAAc,WAAW;QAE9B,MAAM,cAAuB;YAC3B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,wBAAwB;QACxB,MAAM,gBAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAAO,0BAA0B;YACvD,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACpB,QAAQ;oBACR;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBAEvC,4DAA4D;gBAC5D,IAAI,aAAa,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,UAAU;oBAC3D,6CAA6C;oBAC7C,MAAM,eAA0C;wBAC9C,gBAAgB;wBAChB,uBAAuB;wBACvB,qBAAqB;wBACrB,yBAAyB;wBACzB,yBAAyB;wBACzB,qBAAqB;oBACvB;oBAEA,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;wBAC5C,kBAAkB,gBAAgB,OAAO,CAAC,IAAI,OAAO,IAAI,OAAO;oBAClE;gBACF;gBAEA,MAAM,mBAA4B;oBAChC,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,SAAS;oBACT,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC;oBACA,UAAU;oBACV,cAAc;gBAChB;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE/C,sBAAsB;gBACtB,WAAW;oBACT,YAAY;gBACd,GAAG;YAEL,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,MAAM,eAAwB;gBAC5B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,MAAM;gBACN,SAAS,aAAa,OAClB,+JACA;gBACJ,WAAW,IAAI,OAAO,WAAW;gBACjC;YACF;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC3C,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6OAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAO;;;;;;wCAAmB;;;;;;;;;;;;;;;;;;;;;;;8BAQnC,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;0CAE9E,cAAA,8OAAC;oCACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;;wCAED,QAAQ,WAAW,kBAClB,8OAAC;4CAAI,WAAW,CAAC,yBAAyB,EACxC,QAAQ,WAAW,CAAC,SAAS,KAAK,aAC9B,kDACA,QAAQ,WAAW,CAAC,SAAS,KAAK,SAClC,2DACA,0DACJ;;gDAAE;gDAC0B,QAAQ,WAAW,CAAC,SAAS;gDAAC;;;;;;;sDAG9D,8OAAC;4CAAE,WAAU;sDAAuB,QAAQ,OAAO;;;;;;sDACnD,8OAAC;4CAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;sDACC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;+BAzB9C,QAAQ,EAAE;;;;;wBA8BlB,2BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;sCAKzG,8OAAC;4BAAI,KAAK;;;;;;;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aAAY;oCACZ,WAAU;oCACV,MAAM;oCACN,UAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,aAAa,IAAI,MAAM;oCAClC,WAAU;8CAEV,cAAA,8OAAC,iOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGjC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}]}