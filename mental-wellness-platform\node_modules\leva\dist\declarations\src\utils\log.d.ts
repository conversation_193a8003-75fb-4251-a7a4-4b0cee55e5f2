export declare enum LevaErrors {
    UNSUPPORTED_INPUT = 0,
    NO_COMPONENT_FOR_TYPE = 1,
    UNKNOWN_INPUT = 2,
    DUPLICATE_KEYS = 3,
    ALREADY_REGISTERED_TYPE = 4,
    CLIPBOARD_ERROR = 5,
    THEME_ERROR = 6,
    PATH_DOESNT_EXIST = 7,
    INPUT_TYPE_OVERRIDE = 8,
    EMPTY_KEY = 9
}
export declare const warn: (...args: [errorType: LevaErrors] | [errorType: LevaErrors, type: string, path: string] | [errorType: LevaErrors, type: string, path: string] | [errorType: LevaErrors, path: string, value: unknown] | [errorType: LevaErrors, key: string, path: string, prevPath: string] | [errorType: LevaErrors, type: string] | [errorType: LevaErrors, value: unknown] | [errorType: LevaErrors, category: string, key: string] | [errorType: LevaErrors, path: string] | [errorType: LevaErrors, path: string, type: string, wrongType: string]) => void;
export declare const log: (...args: [errorType: LevaErrors] | [errorType: LevaErrors, type: string, path: string] | [errorType: LevaErrors, type: string, path: string] | [errorType: LevaErrors, path: string, value: unknown] | [errorType: LevaErrors, key: string, path: string, prevPath: string] | [errorType: LevaErrors, type: string] | [errorType: LevaErrors, value: unknown] | [errorType: LevaErrors, category: string, key: string] | [errorType: LevaErrors, path: string] | [errorType: LevaErrors, path: string, type: string, wrongType: string]) => void;
