import{rectToClientRect as t,computePosition as e}from"@floating-ui/core";export{arrow,autoPlacement,detectOverflow,flip,hide,inline,limitShift,offset,shift,size}from"@floating-ui/core";function n(t){return t&&t.document&&t.location&&t.alert&&t.setInterval}function o(t){if(null==t)return window;if(!n(t)){const e=t.ownerDocument;return e&&e.defaultView||window}return t}function i(t){return o(t).getComputedStyle(t)}function r(t){return n(t)?"":t?(t.nodeName||"").toLowerCase():""}function l(){const t=navigator.userAgentData;return null!=t&&t.brands?t.brands.map((t=>t.brand+"/"+t.version)).join(" "):navigator.userAgent}function c(t){return t instanceof o(t).HTMLElement}function f(t){return t instanceof o(t).Element}function s(t){if("undefined"==typeof ShadowRoot)return!1;return t instanceof o(t).ShadowRoot||t instanceof ShadowRoot}function u(t){const{overflow:e,overflowX:n,overflowY:o}=i(t);return/auto|scroll|overlay|hidden/.test(e+o+n)}function d(t){return["table","td","th"].includes(r(t))}function h(t){const e=/firefox/i.test(l()),n=i(t);return"none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||["transform","perspective"].includes(n.willChange)||e&&"filter"===n.willChange||e&&!!n.filter&&"none"!==n.filter}function a(){return!/^((?!chrome|android).)*safari/i.test(l())}const g=Math.min,p=Math.max,m=Math.round;function w(t,e,n){var i,r,l,s;void 0===e&&(e=!1),void 0===n&&(n=!1);const u=t.getBoundingClientRect();let d=1,h=1;e&&c(t)&&(d=t.offsetWidth>0&&m(u.width)/t.offsetWidth||1,h=t.offsetHeight>0&&m(u.height)/t.offsetHeight||1);const g=f(t)?o(t):window,p=!a()&&n,w=(u.left+(p&&null!=(i=null==(r=g.visualViewport)?void 0:r.offsetLeft)?i:0))/d,v=(u.top+(p&&null!=(l=null==(s=g.visualViewport)?void 0:s.offsetTop)?l:0))/h,y=u.width/d,x=u.height/h;return{width:y,height:x,top:v,right:w+y,bottom:v+x,left:w,x:w,y:v}}function v(t){return(e=t,(e instanceof o(e).Node?t.ownerDocument:t.document)||window.document).documentElement;var e}function y(t){return f(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function x(t){return w(v(t)).left+y(t).scrollLeft}function b(t,e,n){const o=c(e),i=v(e),l=w(t,o&&function(t){const e=w(t);return m(e.width)!==t.offsetWidth||m(e.height)!==t.offsetHeight}(e),"fixed"===n);let f={scrollLeft:0,scrollTop:0};const s={x:0,y:0};if(o||!o&&"fixed"!==n)if(("body"!==r(e)||u(i))&&(f=y(e)),c(e)){const t=w(e,!0);s.x=t.x+e.clientLeft,s.y=t.y+e.clientTop}else i&&(s.x=x(i));return{x:l.left+f.scrollLeft-s.x,y:l.top+f.scrollTop-s.y,width:l.width,height:l.height}}function L(t){return"html"===r(t)?t:t.assignedSlot||t.parentNode||(s(t)?t.host:null)||v(t)}function R(t){return c(t)&&"fixed"!==getComputedStyle(t).position?t.offsetParent:null}function T(t){const e=o(t);let n=R(t);for(;n&&d(n)&&"static"===getComputedStyle(n).position;)n=R(n);return n&&("html"===r(n)||"body"===r(n)&&"static"===getComputedStyle(n).position&&!h(n))?e:n||function(t){let e=L(t);for(s(e)&&(e=e.host);c(e)&&!["html","body"].includes(r(e));){if(h(e))return e;e=e.parentNode}return null}(t)||e}function W(t){if(c(t))return{width:t.offsetWidth,height:t.offsetHeight};const e=w(t);return{width:e.width,height:e.height}}function E(t){const e=L(t);return["html","body","#document"].includes(r(e))?t.ownerDocument.body:c(e)&&u(e)?e:E(e)}function H(t,e){var n;void 0===e&&(e=[]);const i=E(t),r=i===(null==(n=t.ownerDocument)?void 0:n.body),l=o(i),c=r?[l].concat(l.visualViewport||[],u(i)?i:[]):i,f=e.concat(c);return r?f:f.concat(H(c))}function C(e,n,r){return"viewport"===n?t(function(t,e){const n=o(t),i=v(t),r=n.visualViewport;let l=i.clientWidth,c=i.clientHeight,f=0,s=0;if(r){l=r.width,c=r.height;const t=a();(t||!t&&"fixed"===e)&&(f=r.offsetLeft,s=r.offsetTop)}return{width:l,height:c,x:f,y:s}}(e,r)):f(n)?function(t,e){const n=w(t,!1,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft;return{top:o,left:i,x:i,y:o,right:i+t.clientWidth,bottom:o+t.clientHeight,width:t.clientWidth,height:t.clientHeight}}(n,r):t(function(t){var e;const n=v(t),o=y(t),r=null==(e=t.ownerDocument)?void 0:e.body,l=p(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),c=p(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0);let f=-o.scrollLeft+x(t);const s=-o.scrollTop;return"rtl"===i(r||n).direction&&(f+=p(n.clientWidth,r?r.clientWidth:0)-l),{width:l,height:c,x:f,y:s}}(v(e)))}function S(t){const e=H(t),n=["absolute","fixed"].includes(i(t).position)&&c(t)?T(t):t;return f(n)?e.filter((t=>f(t)&&function(t,e){const n=null==e.getRootNode?void 0:e.getRootNode();if(t.contains(e))return!0;if(n&&s(n)){let n=e;do{if(n&&t===n)return!0;n=n.parentNode||n.host}while(n)}return!1}(t,n)&&"body"!==r(t))):[]}const D={getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[..."clippingAncestors"===n?S(e):[].concat(n),o],l=r[0],c=r.reduce(((t,n)=>{const o=C(e,n,i);return t.top=p(o.top,t.top),t.right=g(o.right,t.right),t.bottom=g(o.bottom,t.bottom),t.left=p(o.left,t.left),t}),C(e,l,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{rect:e,offsetParent:n,strategy:o}=t;const i=c(n),l=v(n);if(n===l)return e;let f={scrollLeft:0,scrollTop:0};const s={x:0,y:0};if((i||!i&&"fixed"!==o)&&(("body"!==r(n)||u(l))&&(f=y(n)),c(n))){const t=w(n,!0);s.x=t.x+n.clientLeft,s.y=t.y+n.clientTop}return{...e,x:e.x-f.scrollLeft+s.x,y:e.y-f.scrollTop+s.y}},isElement:f,getDimensions:W,getOffsetParent:T,getDocumentElement:v,getElementRects:t=>{let{reference:e,floating:n,strategy:o}=t;return{reference:b(e,T(n),o),floating:{...W(n),x:0,y:0}}},getClientRects:t=>Array.from(t.getClientRects()),isRTL:t=>"rtl"===i(t).direction};function N(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:l=!0,animationFrame:c=!1}=o,s=i&&!c,u=r&&!c,d=s||u?[...f(t)?H(t):[],...H(e)]:[];d.forEach((t=>{s&&t.addEventListener("scroll",n,{passive:!0}),u&&t.addEventListener("resize",n)}));let h,a=null;if(l){let o=!0;a=new ResizeObserver((()=>{o||n(),o=!1})),f(t)&&!c&&a.observe(t),a.observe(e)}let g=c?w(t):null;return c&&function e(){const o=w(t);!g||o.x===g.x&&o.y===g.y&&o.width===g.width&&o.height===g.height||n();g=o,h=requestAnimationFrame(e)}(),n(),()=>{var t;d.forEach((t=>{s&&t.removeEventListener("scroll",n),u&&t.removeEventListener("resize",n)})),null==(t=a)||t.disconnect(),a=null,c&&cancelAnimationFrame(h)}}const z=(t,n,o)=>e(t,n,{platform:D,...o});export{N as autoUpdate,z as computePosition,H as getOverflowAncestors};
