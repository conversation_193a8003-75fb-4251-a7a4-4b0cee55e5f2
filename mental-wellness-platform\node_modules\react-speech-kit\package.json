{"name": "react-speech-kit", "version": "3.0.1", "description": "React hooks for in-browser Speech Recognition and Speech Synthesis.", "main": "dist/index.js", "scripts": {"build": "babel src -d dist --copy-files", "start": "webpack-dev-server --mode development", "lint": "./node_modules/.bin/eslint src examples/src --ext .js,.jsx", "test": "jest", "build-demo": "webpack --mode production", "deploy-demo": "gh-pages -d examples/dist", "publish-demo": "npm run build-demo && npm run deploy-demo"}, "author": "<PERSON>", "license": "MIT", "peerDependencies": {"prop-types": "^15.7.2", "react": "^16.8.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "^10.0.1", "babel-jest": "^23.0.0", "babel-loader": "7", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "codecov": "^3.2.0", "enzyme": "^3.9.0", "enzyme-adapter-react-16": "^1.11.2", "eslint": "^5.15.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jest": "^22.3.2", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-react": "^7.12.4", "file-loader": "^3.0.1", "gh-pages": "^2.0.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.2.0", "jest": "^23.0.0", "prettier": "^2.0.5", "prop-types": "^15.7.2", "react": "^16.8.0", "react-dom": "^16.8.3", "styled-components": "^4.1.3", "webpack": "^4.29.6", "webpack-cli": "^3.2.3", "webpack-dev-server": "^3.2.1"}, "dependencies": {}, "jest": {"coverageDirectory": "./coverage/", "collectCoverage": true, "moduleFileExtensions": ["jsx", "js"], "setupFiles": ["<rootDir>/jest.setup.js"]}}