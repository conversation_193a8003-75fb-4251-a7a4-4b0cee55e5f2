{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/constants.ts"], "sourcesContent": ["// App configuration\nexport const APP_CONFIG = {\n  name: 'Mental Wellness Platform',\n  description: 'AI-powered mental wellness support for women and children',\n  version: '1.0.0',\n  supportEmail: '<EMAIL>',\n  emergencyNumber: '911',\n} as const\n\n// API endpoints\nexport const API_ENDPOINTS = {\n  chat: '/api/chat',\n  emotions: '/api/emotions',\n  safety: '/api/safety',\n  games: '/api/games',\n  content: '/api/content',\n  resources: '/api/resources',\n  users: '/api/users',\n} as const\n\n// Safety keywords for monitoring\nexport const SAFETY_KEYWORDS = {\n  distress: [\n    'help me', 'scared', 'afraid', 'hurt', 'pain', 'crying',\n    'sad', 'depressed', 'anxious', 'worried', 'stressed'\n  ],\n  abuse: [\n    'abuse', 'hit', 'hurt me', 'touch me', 'secret', 'don\\'t tell',\n    'inappropriate', 'uncomfortable', 'forced', 'threatened'\n  ],\n  selfHarm: [\n    'hurt myself', 'kill myself', 'suicide', 'cut myself', 'die',\n    'end it all', 'not worth living', 'better off dead'\n  ],\n  emergency: [\n    'emergency', 'call 911', 'help now', 'immediate help',\n    'danger', 'unsafe', 'call police'\n  ]\n} as const\n\n// Emotion categories\nexport const EMOTIONS = {\n  positive: ['joy', 'happiness', 'excitement', 'calm', 'peaceful', 'confident'],\n  negative: ['sadness', 'anger', 'fear', 'anxiety', 'stress', 'frustration'],\n  neutral: ['neutral', 'content', 'focused', 'curious']\n} as const\n\n// Age groups\nexport const AGE_GROUPS = {\n  child: { min: 5, max: 12, label: 'Children (5-12)' },\n  teen: { min: 13, max: 17, label: 'Teenagers (13-17)' },\n  adult: { min: 18, max: 100, label: 'Adults (18+)' }\n} as const\n\n// Game categories\nexport const GAME_CATEGORIES = {\n  stress_relief: 'Stress Relief',\n  coping_skills: 'Coping Skills',\n  mindfulness: 'Mindfulness',\n  emotional_regulation: 'Emotional Regulation'\n} as const\n\n// Content categories\nexport const CONTENT_CATEGORIES = {\n  emotional_intelligence: 'Emotional Intelligence',\n  safety: 'Safety & Protection',\n  coping_skills: 'Coping Skills',\n  wellness: 'General Wellness'\n} as const\n\n// Resource types\nexport const RESOURCE_TYPES = {\n  helpline: 'Helpline',\n  ngo: 'NGO/Organization',\n  emergency: 'Emergency Service',\n  educational: 'Educational Resource',\n  therapy: 'Therapy/Counseling'\n} as const\n\n// Risk levels\nexport const RISK_LEVELS = {\n  low: { color: 'green', label: 'Low Risk' },\n  medium: { color: 'yellow', label: 'Medium Risk' },\n  high: { color: 'orange', label: 'High Risk' },\n  critical: { color: 'red', label: 'Critical Risk' }\n} as const\n\n// Default emergency contacts\nexport const EMERGENCY_CONTACTS = [\n  {\n    name: 'National Suicide Prevention Lifeline',\n    phone: '988',\n    description: '24/7 crisis support'\n  },\n  {\n    name: 'Crisis Text Line',\n    phone: 'Text HOME to 741741',\n    description: 'Text-based crisis support'\n  },\n  {\n    name: 'National Child Abuse Hotline',\n    phone: '1-800-4-A-CHILD (**************)',\n    description: '24/7 child abuse prevention and treatment'\n  },\n  {\n    name: 'National Domestic Violence Hotline',\n    phone: '**************',\n    description: '24/7 domestic violence support'\n  }\n] as const\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;AACb,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,cAAc;IACd,iBAAiB;AACnB;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,WAAW;IACX,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,UAAU;QACR;QAAW;QAAU;QAAU;QAAQ;QAAQ;QAC/C;QAAO;QAAa;QAAW;QAAW;KAC3C;IACD,OAAO;QACL;QAAS;QAAO;QAAW;QAAY;QAAU;QACjD;QAAiB;QAAiB;QAAU;KAC7C;IACD,UAAU;QACR;QAAe;QAAe;QAAW;QAAc;QACvD;QAAc;QAAoB;KACnC;IACD,WAAW;QACT;QAAa;QAAY;QAAY;QACrC;QAAU;QAAU;KACrB;AACH;AAGO,MAAM,WAAW;IACtB,UAAU;QAAC;QAAO;QAAa;QAAc;QAAQ;QAAY;KAAY;IAC7E,UAAU;QAAC;QAAW;QAAS;QAAQ;QAAW;QAAU;KAAc;IAC1E,SAAS;QAAC;QAAW;QAAW;QAAW;KAAU;AACvD;AAGO,MAAM,aAAa;IACxB,OAAO;QAAE,KAAK;QAAG,KAAK;QAAI,OAAO;IAAkB;IACnD,MAAM;QAAE,KAAK;QAAI,KAAK;QAAI,OAAO;IAAoB;IACrD,OAAO;QAAE,KAAK;QAAI,KAAK;QAAK,OAAO;IAAe;AACpD;AAGO,MAAM,kBAAkB;IAC7B,eAAe;IACf,eAAe;IACf,aAAa;IACb,sBAAsB;AACxB;AAGO,MAAM,qBAAqB;IAChC,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,KAAK;IACL,WAAW;IACX,aAAa;IACb,SAAS;AACX;AAGO,MAAM,cAAc;IACzB,KAAK;QAAE,OAAO;QAAS,OAAO;IAAW;IACzC,QAAQ;QAAE,OAAO;QAAU,OAAO;IAAc;IAChD,MAAM;QAAE,OAAO;QAAU,OAAO;IAAY;IAC5C,UAAU;QAAE,OAAO;QAAO,OAAO;IAAgB;AACnD;AAGO,MAAM,qBAAqB;IAChC;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Bars3Icon, XMarkIcon, HeartIcon } from '@heroicons/react/24/outline'\nimport { APP_CONFIG } from '@/lib/constants'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Chat Support', href: '/chat' },\n  { name: 'Games', href: '/games' },\n  { name: 'Resources', href: '/resources' },\n  { name: 'Content', href: '/content' },\n]\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <HeartIcon className=\"h-8 w-8 text-pink-500\" />\n              <span className=\"text-xl font-bold text-gray-900\">\n                {APP_CONFIG.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          <div className=\"hidden md:flex md:items-center md:space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n            <button className=\"bg-pink-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-pink-700 transition-colors\">\n              Emergency Help\n            </button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-gray-900\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <button className=\"w-full text-left bg-pink-600 text-white px-3 py-2 text-base font-medium hover:bg-pink-700 transition-colors\">\n                Emergency Help\n              </button>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAgB,MAAM;IAAQ;IACtC;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;YAAyC,cAAW;;8BACjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;kDACb,uHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;8CAOlB,8OAAC;oCAAO,WAAU;8CAAsG;;;;;;;;;;;;sCAM1H,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB,CAAC;;kDAElC,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,+BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,eAAY;;;;;6DAE3C,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;;;;;;;;;;;;gBAOlD,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,kBAAkB;8CAEhC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAO,WAAU;0CAA8G;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9I", "debugId": null}}]}