{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/content/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { PlayIcon, BookOpenIcon, ClockIcon, EyeIcon, HeartIcon, AcademicCapIcon } from '@heroicons/react/24/outline'\nimport { CONTENT_CATEGORIES, AGE_GROUPS } from '@/lib/constants'\n\ninterface ContentItem {\n  id: string\n  title: string\n  type: 'video' | 'article' | 'comic' | 'story' | 'exercise'\n  category: keyof typeof CONTENT_CATEGORIES\n  ageGroup: keyof typeof AGE_GROUPS | 'all'\n  duration?: number\n  thumbnail: string\n  description: string\n  tags: string[]\n  views: number\n  rating: number\n  featured: boolean\n}\n\nconst contentItems: ContentItem[] = [\n  {\n    id: '1',\n    title: 'Understanding Your Emotions',\n    type: 'video',\n    category: 'emotional_intelligence',\n    ageGroup: 'child',\n    duration: 8,\n    thumbnail: '🎭',\n    description: 'A fun animated video that helps children identify and understand different emotions.',\n    tags: ['emotions', 'feelings', 'kids', 'animation'],\n    views: 15420,\n    rating: 4.8,\n    featured: true\n  },\n  {\n    id: '2',\n    title: 'Breathing Exercises for Anxiety',\n    type: 'exercise',\n    category: 'coping_skills',\n    ageGroup: 'all',\n    duration: 5,\n    thumbnail: '🫁',\n    description: 'Simple breathing techniques to help manage anxiety and stress in daily life.',\n    tags: ['breathing', 'anxiety', 'relaxation', 'mindfulness'],\n    views: 23150,\n    rating: 4.9,\n    featured: true\n  },\n  {\n    id: '3',\n    title: 'Recognizing Safe vs Unsafe Situations',\n    type: 'comic',\n    category: 'safety',\n    ageGroup: 'child',\n    thumbnail: '🛡️',\n    description: 'An interactive comic that teaches children how to identify safe and unsafe situations.',\n    tags: ['safety', 'protection', 'awareness', 'comic'],\n    views: 8930,\n    rating: 4.7,\n    featured: false\n  },\n  {\n    id: '4',\n    title: 'Building Self-Confidence',\n    type: 'article',\n    category: 'wellness',\n    ageGroup: 'teen',\n    thumbnail: '💪',\n    description: 'Practical strategies for teenagers to build self-confidence and positive self-image.',\n    tags: ['confidence', 'self-esteem', 'teens', 'empowerment'],\n    views: 12680,\n    rating: 4.6,\n    featured: false\n  },\n  {\n    id: '5',\n    title: 'The Worry Monster Story',\n    type: 'story',\n    category: 'emotional_intelligence',\n    ageGroup: 'child',\n    duration: 12,\n    thumbnail: '👹',\n    description: 'A gentle story about a child who learns to tame their worry monster with help and courage.',\n    tags: ['worry', 'anxiety', 'story', 'coping'],\n    views: 19240,\n    rating: 4.8,\n    featured: true\n  },\n  {\n    id: '6',\n    title: 'Mindfulness for Busy Moms',\n    type: 'video',\n    category: 'wellness',\n    ageGroup: 'adult',\n    duration: 15,\n    thumbnail: '🧘‍♀️',\n    description: 'Quick mindfulness practices that busy mothers can incorporate into their daily routine.',\n    tags: ['mindfulness', 'mothers', 'stress-relief', 'self-care'],\n    views: 31200,\n    rating: 4.9,\n    featured: true\n  },\n  {\n    id: '7',\n    title: 'Healthy Boundaries Guide',\n    type: 'article',\n    category: 'safety',\n    ageGroup: 'teen',\n    thumbnail: '🚧',\n    description: 'Understanding and setting healthy boundaries in relationships and social situations.',\n    tags: ['boundaries', 'relationships', 'safety', 'teens'],\n    views: 14560,\n    rating: 4.7,\n    featured: false\n  },\n  {\n    id: '8',\n    title: 'Progressive Muscle Relaxation',\n    type: 'exercise',\n    category: 'coping_skills',\n    ageGroup: 'all',\n    duration: 20,\n    thumbnail: '🧘',\n    description: 'A guided progressive muscle relaxation exercise to reduce tension and promote calm.',\n    tags: ['relaxation', 'stress-relief', 'muscle', 'guided'],\n    views: 18750,\n    rating: 4.8,\n    featured: false\n  }\n]\n\nexport default function ContentPage() {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [selectedType, setSelectedType] = useState<string>('all')\n  const [selectedAgeGroup, setSelectedAgeGroup] = useState<string>('all')\n\n  const filteredContent = contentItems.filter(item => {\n    const categoryMatch = selectedCategory === 'all' || item.category === selectedCategory\n    const typeMatch = selectedType === 'all' || item.type === selectedType\n    const ageMatch = selectedAgeGroup === 'all' || item.ageGroup === selectedAgeGroup || item.ageGroup === 'all'\n    return categoryMatch && typeMatch && ageMatch\n  })\n\n  const featuredContent = contentItems.filter(item => item.featured)\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'video': return PlayIcon\n      case 'article': return BookOpenIcon\n      case 'comic': return EyeIcon\n      case 'story': return BookOpenIcon\n      case 'exercise': return HeartIcon\n      default: return AcademicCapIcon\n    }\n  }\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'video': return 'bg-red-100 text-red-800'\n      case 'article': return 'bg-blue-100 text-blue-800'\n      case 'comic': return 'bg-purple-100 text-purple-800'\n      case 'story': return 'bg-green-100 text-green-800'\n      case 'exercise': return 'bg-orange-100 text-orange-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Educational Content\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Videos, articles, stories, and interactive content to learn about emotional intelligence, \n            safety, and mental wellness for women and children.\n          </p>\n        </div>\n\n        {/* Featured Content */}\n        <div className=\"mb-12\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Featured Content</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {featuredContent.map((item) => {\n              const TypeIcon = getTypeIcon(item.type)\n              return (\n                <div key={item.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"text-4xl\">{item.thumbnail}</div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>\n                        {item.type}\n                      </span>\n                    </div>\n                    \n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {item.title}\n                    </h3>\n                    \n                    <p className=\"text-gray-600 mb-4 text-sm\">\n                      {item.description}\n                    </p>\n\n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      {item.duration && (\n                        <div className=\"flex items-center\">\n                          <ClockIcon className=\"h-4 w-4 mr-1\" />\n                          {item.duration} min\n                        </div>\n                      )}\n                      <div className=\"flex items-center\">\n                        <EyeIcon className=\"h-4 w-4 mr-1\" />\n                        {item.views.toLocaleString()}\n                      </div>\n                    </div>\n\n                    <button className=\"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center\">\n                      <TypeIcon className=\"h-4 w-4 mr-2\" />\n                      View Content\n                    </button>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category\n              </label>\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              >\n                <option value=\"all\">All Categories</option>\n                {Object.entries(CONTENT_CATEGORIES).map(([key, label]) => (\n                  <option key={key} value={key}>{label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Content Type\n              </label>\n              <select\n                value={selectedType}\n                onChange={(e) => setSelectedType(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"video\">Videos</option>\n                <option value=\"article\">Articles</option>\n                <option value=\"comic\">Comics</option>\n                <option value=\"story\">Stories</option>\n                <option value=\"exercise\">Exercises</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Age Group\n              </label>\n              <select\n                value={selectedAgeGroup}\n                onChange={(e) => setSelectedAgeGroup(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n              >\n                <option value=\"all\">All Ages</option>\n                {Object.entries(AGE_GROUPS).map(([key, group]) => (\n                  <option key={key} value={key}>{group.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* All Content */}\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">All Content</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredContent.map((item) => {\n              const TypeIcon = getTypeIcon(item.type)\n              return (\n                <div key={item.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"text-3xl\">{item.thumbnail}</div>\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(item.type)}`}>\n                        {item.type}\n                      </span>\n                    </div>\n\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                      {item.title}\n                    </h3>\n\n                    <p className=\"text-gray-600 mb-4 text-sm\">\n                      {item.description}\n                    </p>\n\n                    <div className=\"flex flex-wrap gap-1 mb-4\">\n                      {item.tags.slice(0, 3).map((tag, index) => (\n                        <span key={index} className=\"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded\">\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n\n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      {item.duration && (\n                        <div className=\"flex items-center\">\n                          <ClockIcon className=\"h-4 w-4 mr-1\" />\n                          {item.duration} min\n                        </div>\n                      )}\n                      <div className=\"flex items-center\">\n                        <EyeIcon className=\"h-4 w-4 mr-1\" />\n                        {item.views.toLocaleString()}\n                      </div>\n                    </div>\n\n                    <button className=\"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center\">\n                      <TypeIcon className=\"h-4 w-4 mr-2\" />\n                      View Content\n                    </button>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {filteredContent.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 text-lg\">No content found for the selected filters.</p>\n          </div>\n        )}\n\n        {/* Learning Paths */}\n        <div className=\"mt-16 bg-white rounded-lg shadow-md p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Learning Paths</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <div className=\"border border-gray-200 rounded-lg p-6\">\n              <div className=\"text-3xl mb-4\">🌱</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Emotional Basics for Kids</h3>\n              <p className=\"text-gray-600 text-sm mb-4\">\n                A structured path to help children understand and express emotions healthily.\n              </p>\n              <div className=\"text-sm text-gray-500 mb-4\">5 lessons • Ages 5-12</div>\n              <button className=\"w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors\">\n                Start Learning Path\n              </button>\n            </div>\n\n            <div className=\"border border-gray-200 rounded-lg p-6\">\n              <div className=\"text-3xl mb-4\">🛡️</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Safety & Protection</h3>\n              <p className=\"text-gray-600 text-sm mb-4\">\n                Essential safety knowledge and protection strategies for teens and adults.\n              </p>\n              <div className=\"text-sm text-gray-500 mb-4\">7 lessons • Ages 13+</div>\n              <button className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors\">\n                Start Learning Path\n              </button>\n            </div>\n\n            <div className=\"border border-gray-200 rounded-lg p-6\">\n              <div className=\"text-3xl mb-4\">💪</div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Building Resilience</h3>\n              <p className=\"text-gray-600 text-sm mb-4\">\n                Develop coping skills and emotional resilience for life's challenges.\n              </p>\n              <div className=\"text-sm text-gray-500 mb-4\">6 lessons • All ages</div>\n              <button className=\"w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors\">\n                Start Learning Path\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAqBA,MAAM,eAA8B;IAClC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAY;YAAY;YAAQ;SAAY;QACnD,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAa;YAAW;YAAc;SAAc;QAC3D,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAU;YAAc;YAAa;SAAQ;QACpD,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAc;YAAe;YAAS;SAAc;QAC3D,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAS;YAAW;YAAS;SAAS;QAC7C,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAe;YAAW;YAAiB;SAAY;QAC9D,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAc;YAAiB;YAAU;SAAQ;QACxD,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,MAAM;YAAC;YAAc;YAAiB;YAAU;SAAS;QACzD,OAAO;QACP,QAAQ;QACR,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA;QAC1C,MAAM,gBAAgB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACtE,MAAM,YAAY,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAC1D,MAAM,WAAW,qBAAqB,SAAS,KAAK,QAAQ,KAAK,oBAAoB,KAAK,QAAQ,KAAK;QACvG,OAAO,iBAAiB,aAAa;IACvC;IAEA,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAEjE,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,OAAO,+MAAA,CAAA,WAAQ;YAC7B,KAAK;gBAAW,OAAO,uNAAA,CAAA,eAAY;YACnC,KAAK;gBAAS,OAAO,6MAAA,CAAA,UAAO;YAC5B,KAAK;gBAAS,OAAO,uNAAA,CAAA,eAAY;YACjC,KAAK;gBAAY,OAAO,iNAAA,CAAA,YAAS;YACjC;gBAAS,OAAO,6NAAA,CAAA,kBAAe;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,WAAW,YAAY,KAAK,IAAI;gCACtC,qBACE,8OAAC;oCAAkB,WAAU;8CAC3B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAY,KAAK,SAAS;;;;;;kEACzC,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,aAAa,KAAK,IAAI,GAAG;kEACrF,KAAK,IAAI;;;;;;;;;;;;0DAId,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAGb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,kBACZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DACpB,KAAK,QAAQ;4DAAC;;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAClB,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;0DAI9B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAS,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;mCA/BjC,KAAK,EAAE;;;;;4BAqCrB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,OAAO,OAAO,CAAC,uHAAA,CAAA,qBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACnD,8OAAC;oDAAiB,OAAO;8DAAM;mDAAlB;;;;;;;;;;;;;;;;;0CAInB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;0CAG7B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,OAAO,OAAO,CAAC,uHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC3C,8OAAC;oDAAiB,OAAO;8DAAM,MAAM,KAAK;mDAA7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,WAAW,YAAY,KAAK,IAAI;gCACtC,qBACE,8OAAC;oCAAkB,WAAU;8CAC3B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAY,KAAK,SAAS;;;;;;kEACzC,8OAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,aAAa,KAAK,IAAI,GAAG;kEACrF,KAAK,IAAI;;;;;;;;;;;;0DAId,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAGb,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;;;;;;0DAMf,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,QAAQ,kBACZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DACpB,KAAK,QAAQ;4DAAC;;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAClB,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;0DAI9B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAS,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;mCAvCjC,KAAK,EAAE;;;;;4BA6CrB;;;;;;;;;;;;gBAIH,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,8OAAC;4CAAO,WAAU;sDAA2F;;;;;;;;;;;;8CAK/G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,8OAAC;4CAAO,WAAU;sDAAyF;;;;;;;;;;;;8CAK7G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,8OAAC;4CAAO,WAAU;sDAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7H", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/PlayIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlayIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/BookOpenIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BookOpenIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BookOpenIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/AcademicCapIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction AcademicCapIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(AcademicCapIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}