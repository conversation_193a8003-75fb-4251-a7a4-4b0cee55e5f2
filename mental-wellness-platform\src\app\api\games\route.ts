import { NextRequest, NextResponse } from 'next/server'

// Mock games data - in a real app, this would come from a database
const games = [
  {
    id: '1',
    title: 'Breathing Garden',
    description: 'A peaceful breathing exercise game where you help flowers bloom by following breathing patterns.',
    category: 'stress_relief',
    ageGroup: 'all',
    duration: 5,
    difficulty: 'easy',
    thumbnail: '🌸',
    rating: 4.8,
    players: 1,
    benefits: ['Reduces anxiety', 'Improves focus', 'Calms mind'],
    gameUrl: '/games/breathing-garden'
  },
  {
    id: '2',
    title: 'Emotion Detective',
    description: 'Learn to identify and understand different emotions through interactive scenarios.',
    category: 'emotional_regulation',
    ageGroup: 'child',
    duration: 15,
    difficulty: 'easy',
    thumbnail: '🕵️‍♀️',
    rating: 4.6,
    players: 1,
    benefits: ['Emotional awareness', 'Empathy building', 'Social skills'],
    gameUrl: '/games/emotion-detective'
  },
  {
    id: '3',
    title: 'Mindful Maze',
    description: 'Navigate through calming mazes while practicing mindfulness techniques.',
    category: 'mindfulness',
    ageGroup: 'teen',
    duration: 10,
    difficulty: 'medium',
    thumbnail: '🧩',
    rating: 4.7,
    players: 1,
    benefits: ['Mindfulness', 'Problem solving', 'Stress relief'],
    gameUrl: '/games/mindful-maze'
  },
  {
    id: '4',
    title: 'Coping Castle',
    description: 'Build a castle while learning different coping strategies for difficult situations.',
    category: 'coping_skills',
    ageGroup: 'all',
    duration: 20,
    difficulty: 'medium',
    thumbnail: '🏰',
    rating: 4.9,
    players: 1,
    benefits: ['Coping strategies', 'Resilience', 'Problem solving'],
    gameUrl: '/games/coping-castle'
  },
  {
    id: '5',
    title: 'Worry Warriors',
    description: 'Transform worries into positive thoughts in this empowering adventure game.',
    category: 'emotional_regulation',
    ageGroup: 'teen',
    duration: 25,
    difficulty: 'hard',
    thumbnail: '⚔️',
    rating: 4.5,
    players: 1,
    benefits: ['Anxiety management', 'Positive thinking', 'Self-confidence'],
    gameUrl: '/games/worry-warriors'
  },
  {
    id: '6',
    title: 'Gratitude Garden',
    description: 'Plant and grow a beautiful garden by expressing gratitude and positive thoughts.',
    category: 'mindfulness',
    ageGroup: 'all',
    duration: 12,
    difficulty: 'easy',
    thumbnail: '🌻',
    rating: 4.8,
    players: 1,
    benefits: ['Gratitude practice', 'Positive mindset', 'Emotional wellbeing'],
    gameUrl: '/games/gratitude-garden'
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const ageGroup = searchParams.get('ageGroup')
    const difficulty = searchParams.get('difficulty')

    let filteredGames = games

    // Filter by category
    if (category && category !== 'all') {
      filteredGames = filteredGames.filter(game => game.category === category)
    }

    // Filter by age group
    if (ageGroup && ageGroup !== 'all') {
      filteredGames = filteredGames.filter(game => 
        game.ageGroup === ageGroup || game.ageGroup === 'all'
      )
    }

    // Filter by difficulty
    if (difficulty && difficulty !== 'all') {
      filteredGames = filteredGames.filter(game => game.difficulty === difficulty)
    }

    return NextResponse.json({
      success: true,
      data: filteredGames,
      total: filteredGames.length
    })

  } catch (error) {
    console.error('Games API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { gameId, userId, action } = await request.json()

    if (!gameId || !action) {
      return NextResponse.json(
        { error: 'Game ID and action are required' },
        { status: 400 }
      )
    }

    // Handle different game actions
    switch (action) {
      case 'start':
        // Log game start
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        return NextResponse.json({
          success: true,
          data: {
            sessionId,
            message: 'Game session started',
            startTime: new Date().toISOString()
          }
        })

      case 'complete':
        // Log game completion
        const { score, duration } = await request.json()
        return NextResponse.json({
          success: true,
          data: {
            message: 'Game completed successfully',
            score: score || 0,
            duration: duration || 0,
            completedAt: new Date().toISOString()
          }
        })

      case 'progress':
        // Save game progress
        const { progress } = await request.json()
        return NextResponse.json({
          success: true,
          data: {
            message: 'Progress saved',
            progress: progress || 0,
            savedAt: new Date().toISOString()
          }
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Games API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
