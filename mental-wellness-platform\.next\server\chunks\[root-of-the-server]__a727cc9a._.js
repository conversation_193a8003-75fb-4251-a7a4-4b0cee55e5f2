module.exports = {

"[project]/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/constants.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// App configuration
__turbopack_context__.s({
    "AGE_GROUPS": ()=>AGE_GROUPS,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "APP_CONFIG": ()=>APP_CONFIG,
    "CONTENT_CATEGORIES": ()=>CONTENT_CATEGORIES,
    "EMERGENCY_CONTACTS": ()=>EMERGENCY_CONTACTS,
    "EMOTIONS": ()=>EMOTIONS,
    "GAME_CATEGORIES": ()=>GAME_CATEGORIES,
    "RESOURCE_TYPES": ()=>RESOURCE_TYPES,
    "RISK_LEVELS": ()=>RISK_LEVELS,
    "SAFETY_KEYWORDS": ()=>SAFETY_KEYWORDS
});
const APP_CONFIG = {
    name: 'Mental Wellness Platform',
    description: 'AI-powered mental wellness support for women and children',
    version: '1.0.0',
    supportEmail: '<EMAIL>',
    emergencyNumber: '911'
};
const API_ENDPOINTS = {
    chat: '/api/chat',
    emotions: '/api/emotions',
    safety: '/api/safety',
    games: '/api/games',
    content: '/api/content',
    resources: '/api/resources',
    users: '/api/users'
};
const SAFETY_KEYWORDS = {
    distress: [
        'help me',
        'scared',
        'afraid',
        'hurt',
        'pain',
        'crying',
        'sad',
        'depressed',
        'anxious',
        'worried',
        'stressed'
    ],
    abuse: [
        'abuse',
        'hit',
        'hurt me',
        'touch me',
        'secret',
        'don\'t tell',
        'inappropriate',
        'uncomfortable',
        'forced',
        'threatened'
    ],
    selfHarm: [
        'hurt myself',
        'kill myself',
        'suicide',
        'cut myself',
        'die',
        'end it all',
        'not worth living',
        'better off dead'
    ],
    emergency: [
        'emergency',
        'call 911',
        'help now',
        'immediate help',
        'danger',
        'unsafe',
        'call police'
    ]
};
const EMOTIONS = {
    positive: [
        'joy',
        'happiness',
        'excitement',
        'calm',
        'peaceful',
        'confident'
    ],
    negative: [
        'sadness',
        'anger',
        'fear',
        'anxiety',
        'stress',
        'frustration'
    ],
    neutral: [
        'neutral',
        'content',
        'focused',
        'curious'
    ]
};
const AGE_GROUPS = {
    child: {
        min: 5,
        max: 12,
        label: 'Children (5-12)'
    },
    teen: {
        min: 13,
        max: 17,
        label: 'Teenagers (13-17)'
    },
    adult: {
        min: 18,
        max: 100,
        label: 'Adults (18+)'
    }
};
const GAME_CATEGORIES = {
    stress_relief: 'Stress Relief',
    coping_skills: 'Coping Skills',
    mindfulness: 'Mindfulness',
    emotional_regulation: 'Emotional Regulation'
};
const CONTENT_CATEGORIES = {
    emotional_intelligence: 'Emotional Intelligence',
    safety: 'Safety & Protection',
    coping_skills: 'Coping Skills',
    wellness: 'General Wellness'
};
const RESOURCE_TYPES = {
    helpline: 'Helpline',
    ngo: 'NGO/Organization',
    emergency: 'Emergency Service',
    educational: 'Educational Resource',
    therapy: 'Therapy/Counseling'
};
const RISK_LEVELS = {
    low: {
        color: 'green',
        label: 'Low Risk'
    },
    medium: {
        color: 'yellow',
        label: 'Medium Risk'
    },
    high: {
        color: 'orange',
        label: 'High Risk'
    },
    critical: {
        color: 'red',
        label: 'Critical Risk'
    }
};
const EMERGENCY_CONTACTS = [
    {
        name: 'National Suicide Prevention Lifeline',
        phone: '988',
        description: '24/7 crisis support'
    },
    {
        name: 'Crisis Text Line',
        phone: 'Text HOME to 741741',
        description: 'Text-based crisis support'
    },
    {
        name: 'National Child Abuse Hotline',
        phone: '1-800-4-A-CHILD (**************)',
        description: '24/7 child abuse prevention and treatment'
    },
    {
        name: 'National Domestic Violence Hotline',
        phone: '**************',
        description: '24/7 domestic violence support'
    }
];
}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "cn": ()=>cn,
    "debounce": ()=>debounce,
    "formatDate": ()=>formatDate,
    "formatTime": ()=>formatTime,
    "generateId": ()=>generateId,
    "sanitizeInput": ()=>sanitizeInput
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}
function formatTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function sanitizeInput(input) {
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '').replace(/[<>]/g, '').trim();
}
}),
"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
;
;
// Initialize OpenAI client (optional - will work without API key)
const openai = process.env.OPENAI_API_KEY ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
}) : null;
// Safety monitoring function
function detectSafetyRisk(message) {
    const lowerMessage = message.toLowerCase();
    const detectedKeywords = [];
    let riskLevel = 'low';
    // Check for emergency keywords
    for (const keyword of __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SAFETY_KEYWORDS"].emergency){
        if (lowerMessage.includes(keyword)) {
            detectedKeywords.push(keyword);
            riskLevel = 'critical';
        }
    }
    // Check for self-harm keywords
    if (riskLevel !== 'critical') {
        for (const keyword of __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SAFETY_KEYWORDS"].selfHarm){
            if (lowerMessage.includes(keyword)) {
                detectedKeywords.push(keyword);
                riskLevel = 'high';
            }
        }
    }
    // Check for abuse keywords
    if (riskLevel === 'low') {
        for (const keyword of __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SAFETY_KEYWORDS"].abuse){
            if (lowerMessage.includes(keyword)) {
                detectedKeywords.push(keyword);
                riskLevel = 'high';
            }
        }
    }
    // Check for distress keywords
    if (riskLevel === 'low') {
        for (const keyword of __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SAFETY_KEYWORDS"].distress){
            if (lowerMessage.includes(keyword)) {
                detectedKeywords.push(keyword);
                riskLevel = 'medium';
            }
        }
    }
    return {
        riskLevel,
        keywords: detectedKeywords
    };
}
// System prompt for the AI counselor
const SYSTEM_PROMPT = `You are a compassionate AI mental health counselor specializing in supporting women and children. Your role is to:

1. Provide empathetic, non-judgmental emotional support
2. Offer practical coping strategies and techniques
3. Validate feelings and experiences
4. Encourage professional help when appropriate
5. Maintain a safe, supportive environment

Guidelines:
- Always prioritize safety and well-being
- Use age-appropriate language
- Be culturally sensitive
- Never provide medical diagnoses
- Encourage professional help for serious concerns
- If someone mentions self-harm or abuse, provide crisis resources immediately

Remember: You are here to listen, support, and guide - not to replace professional therapy or medical care.`;
async function POST(request) {
    try {
        const { message, sessionId, userId } = await request.json();
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Message is required'
            }, {
                status: 400
            });
        }
        // Sanitize input
        const sanitizedMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeInput"])(message);
        // Perform safety monitoring
        const safetyCheck = detectSafetyRisk(sanitizedMessage);
        // If critical risk detected, provide immediate crisis resources
        if (safetyCheck.riskLevel === 'critical') {
            const crisisResponse = {
                role: 'assistant',
                content: `I'm very concerned about what you've shared. Your safety is the most important thing right now. Please reach out for immediate help:

🚨 **Emergency Services: Call 911**
📞 **Crisis Hotline: Call 988** (Suicide & Crisis Lifeline)
💬 **Crisis Text Line: Text HOME to 741741**

You don't have to go through this alone. There are people who want to help you right now. Please reach out to one of these resources immediately.

Would you like me to help you find local emergency resources or someone you can talk to?`,
                timestamp: new Date().toISOString(),
                safetyAlert: {
                    riskLevel: safetyCheck.riskLevel,
                    keywords: safetyCheck.keywords
                }
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: crisisResponse
            });
        }
        // Generate AI response using OpenAI
        let aiResponse = '';
        if (openai) {
            try {
                const completion = await openai.chat.completions.create({
                    model: 'gpt-4',
                    messages: [
                        {
                            role: 'system',
                            content: SYSTEM_PROMPT
                        },
                        {
                            role: 'user',
                            content: sanitizedMessage
                        }
                    ],
                    max_tokens: 500,
                    temperature: 0.7
                });
                aiResponse = completion.choices[0]?.message?.content || 'I understand you\'re reaching out. Can you tell me more about how you\'re feeling?';
            } catch (openaiError) {
                console.error('OpenAI API error:', openaiError);
                // Fallback response if OpenAI fails
                aiResponse = 'I hear you and I\'m here to listen. Can you tell me more about what\'s on your mind today?';
            }
        } else {
            // Fallback response when no API key is configured
            aiResponse = 'Thank you for sharing with me. I\'m here to support you. Can you tell me more about how you\'re feeling right now?';
        }
        // Add safety resources if medium or high risk
        if (safetyCheck.riskLevel === 'high') {
            aiResponse += '\n\n**If you need immediate support:**\n📞 Crisis Hotline: 988\n💬 Text HOME to 741741';
        } else if (safetyCheck.riskLevel === 'medium') {
            aiResponse += '\n\n**Remember:** If you ever need immediate help, call 988 or text HOME to 741741';
        }
        const response = {
            role: 'assistant',
            content: aiResponse,
            timestamp: new Date().toISOString(),
            safetyAlert: safetyCheck.riskLevel !== 'low' ? {
                riskLevel: safetyCheck.riskLevel,
                keywords: safetyCheck.keywords
            } : undefined
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: response
        });
    } catch (error) {
        console.error('Chat API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a727cc9a._.js.map