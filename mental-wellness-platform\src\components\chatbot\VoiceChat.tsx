'use client'

import { useState, useEffect, useRef } from 'react'
import { MicrophoneIcon, SpeakerWaveIcon, StopIcon, LanguageIcon } from '@heroicons/react/24/outline'

interface VoiceChatProps {
  onTranscript: (text: string) => void
  onSpeakResponse: (text: string, language: 'en' | 'ta') => void
  isListening: boolean
  setIsListening: (listening: boolean) => void
  language: 'en' | 'ta'
  setLanguage: (lang: 'en' | 'ta') => void
}

export default function VoiceChat({
  onTranscript,
  onSpeakResponse,
  isListening,
  setIsListening,
  language,
  setLanguage
}: VoiceChatProps) {
  const [isSupported, setIsSupported] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [transcript, setTranscript] = useState('')
  
  const recognitionRef = useRef<any>(null)
  const synthRef = useRef<SpeechSynthesis | null>(null)

  useEffect(() => {
    // Check for speech recognition support
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    const speechSynthesis = window.speechSynthesis

    if (SpeechRecognition && speechSynthesis) {
      setIsSupported(true)
      synthRef.current = speechSynthesis

      // Initialize speech recognition
      const recognition = new SpeechRecognition()
      recognition.continuous = true
      recognition.interimResults = true
      recognition.lang = language === 'ta' ? 'ta-IN' : 'en-US'

      recognition.onstart = () => {
        console.log('Voice recognition started')
      }

      recognition.onresult = (event: any) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        setTranscript(interimTranscript)

        if (finalTranscript) {
          onTranscript(finalTranscript)
          setTranscript('')
          setIsListening(false)
        }
      }

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setIsListening(false)
      }

      recognition.onend = () => {
        setIsListening(false)
      }

      recognitionRef.current = recognition
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [language, onTranscript, setIsListening])

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      recognitionRef.current.lang = language === 'ta' ? 'ta-IN' : 'en-US'
      recognitionRef.current.start()
      setIsListening(true)
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
      setIsListening(false)
    }
  }

  const speakText = (text: string, lang: 'en' | 'ta' = language) => {
    if (synthRef.current && text) {
      // Stop any current speech
      synthRef.current.cancel()

      const utterance = new SpeechSynthesisUtterance(text)
      
      // Set language and voice
      utterance.lang = lang === 'ta' ? 'ta-IN' : 'en-US'
      utterance.rate = 0.9
      utterance.pitch = 1
      utterance.volume = 1

      // Try to find a suitable voice
      const voices = synthRef.current.getVoices()
      const preferredVoice = voices.find(voice => 
        lang === 'ta' 
          ? voice.lang.includes('ta') || voice.lang.includes('Tamil')
          : voice.lang.includes('en') && voice.lang.includes('US')
      )
      
      if (preferredVoice) {
        utterance.voice = preferredVoice
      }

      utterance.onstart = () => setIsSpeaking(true)
      utterance.onend = () => setIsSpeaking(false)
      utterance.onerror = () => setIsSpeaking(false)

      synthRef.current.speak(utterance)
    }
  }

  const stopSpeaking = () => {
    if (synthRef.current) {
      synthRef.current.cancel()
      setIsSpeaking(false)
    }
  }

  // Expose speakText function to parent
  useEffect(() => {
    onSpeakResponse(speakText as any, language)
  }, [language, onSpeakResponse])

  if (!isSupported) {
    return (
      <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">
          Voice features are not supported in your browser. Please use Chrome or Edge for voice interaction.
        </p>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg">
      {/* Language Toggle */}
      <div className="flex items-center space-x-2">
        <LanguageIcon className="h-5 w-5 text-gray-500" />
        <select
          value={language}
          onChange={(e) => setLanguage(e.target.value as 'en' | 'ta')}
          className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500"
        >
          <option value="en">English</option>
          <option value="ta">தமிழ் (Tamil)</option>
        </select>
      </div>

      {/* Voice Input */}
      <div className="flex items-center space-x-2">
        {!isListening ? (
          <button
            onClick={startListening}
            className="flex items-center space-x-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
          >
            <MicrophoneIcon className="h-5 w-5" />
            <span className="text-sm">
              {language === 'ta' ? 'பேசுங்கள்' : 'Speak'}
            </span>
          </button>
        ) : (
          <button
            onClick={stopListening}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse"
          >
            <StopIcon className="h-5 w-5" />
            <span className="text-sm">
              {language === 'ta' ? 'நிறுத்து' : 'Stop'}
            </span>
          </button>
        )}
      </div>

      {/* Speech Output Control */}
      <div className="flex items-center space-x-2">
        {!isSpeaking ? (
          <button
            onClick={() => speakText(language === 'ta' 
              ? 'வணக்கம்! நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்.' 
              : 'Hello! I am here to help you.'
            )}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <SpeakerWaveIcon className="h-5 w-5" />
            <span className="text-sm">
              {language === 'ta' ? 'சோதனை' : 'Test Voice'}
            </span>
          </button>
        ) : (
          <button
            onClick={stopSpeaking}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <StopIcon className="h-5 w-5" />
            <span className="text-sm">
              {language === 'ta' ? 'அமைதி' : 'Stop Voice'}
            </span>
          </button>
        )}
      </div>

      {/* Live Transcript */}
      {transcript && (
        <div className="flex-1 text-sm text-gray-600 italic">
          {language === 'ta' ? 'கேட்கிறது: ' : 'Listening: '}{transcript}
        </div>
      )}

      {/* Status Indicators */}
      <div className="flex space-x-2">
        {isListening && (
          <div className="flex items-center space-x-1 text-red-600">
            <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
            <span className="text-xs">
              {language === 'ta' ? 'கேட்கிறது' : 'Listening'}
            </span>
          </div>
        )}
        {isSpeaking && (
          <div className="flex items-center space-x-1 text-blue-600">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
            <span className="text-xs">
              {language === 'ta' ? 'பேசுகிறது' : 'Speaking'}
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
