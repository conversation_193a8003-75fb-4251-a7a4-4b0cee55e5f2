import type { SelectInput, InternalSelectSettings } from './select-types';
export declare const schema: (_o: any, s: any) => boolean;
export declare const sanitize: (value: any, { values }: InternalSelectSettings) => any;
export declare const format: (value: any, { values }: InternalSelectSettings) => number;
export declare const normalize: (input: SelectInput) => {
    value: unknown;
    settings: {
        keys: string[];
        values: unknown[];
    };
};
