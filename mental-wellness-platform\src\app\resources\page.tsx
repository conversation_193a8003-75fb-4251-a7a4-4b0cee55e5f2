'use client'

import { useState } from 'react'
import { PhoneIcon, EnvelopeIcon, GlobeAltIcon, MapPinIcon, ExclamationTriangleIcon, ClockIcon } from '@heroicons/react/24/outline'
import { EMERGENCY_CONTACTS } from '@/lib/constants'

interface Resource {
  id: string
  name: string
  type: 'helpline' | 'ngo' | 'emergency' | 'educational' | 'therapy'
  category: 'mental_health' | 'abuse' | 'emergency' | 'education' | 'women' | 'children'
  contact: {
    phone?: string
    email?: string
    website?: string
    address?: string
  }
  availability: string
  location: string
  description: string
  isEmergency: boolean
  languages?: string[]
  ageGroup?: string
}

const resources: Resource[] = [
  {
    id: '1',
    name: 'National Suicide Prevention Lifeline',
    type: 'emergency',
    category: 'emergency',
    contact: {
      phone: '988',
      website: 'https://suicidepreventionlifeline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Free and confidential emotional support for people in suicidal crisis or emotional distress.',
    isEmergency: true,
    languages: ['English', 'Spanish']
  },
  {
    id: '2',
    name: 'Crisis Text Line',
    type: 'emergency',
    category: 'emergency',
    contact: {
      phone: 'Text HOME to 741741',
      website: 'https://crisistextline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Free, 24/7 support for those in crisis. Text with a trained crisis counselor.',
    isEmergency: true,
    languages: ['English', 'Spanish']
  },
  {
    id: '3',
    name: 'National Child Abuse Hotline',
    type: 'helpline',
    category: 'children',
    contact: {
      phone: '1-800-4-A-CHILD (**************)',
      website: 'https://childhelp.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Professional crisis counselors provide intervention, information and referrals to thousands of callers each day.',
    isEmergency: true,
    languages: ['English', 'Spanish'],
    ageGroup: 'Children & Teens'
  },
  {
    id: '4',
    name: 'National Domestic Violence Hotline',
    type: 'helpline',
    category: 'women',
    contact: {
      phone: '**************',
      website: 'https://thehotline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Confidential support for women experiencing domestic violence, available 24/7 in over 200 languages.',
    isEmergency: true,
    languages: ['200+ languages available']
  },
  {
    id: '5',
    name: 'SAMHSA National Helpline',
    type: 'helpline',
    category: 'mental_health',
    contact: {
      phone: '**************',
      website: 'https://samhsa.gov'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Treatment referral and information service for individuals and families facing mental health and/or substance use disorders.',
    isEmergency: false,
    languages: ['English', 'Spanish']
  },
  {
    id: '6',
    name: 'National Alliance on Mental Illness (NAMI)',
    type: 'ngo',
    category: 'mental_health',
    contact: {
      phone: '**************',
      email: '<EMAIL>',
      website: 'https://nami.org'
    },
    availability: 'Mon-Fri 10am-10pm ET',
    location: 'National (USA)',
    description: 'Support, education and advocacy for individuals and families affected by mental illness.',
    isEmergency: false,
    languages: ['English', 'Spanish']
  },
  {
    id: '7',
    name: 'Girls on the Run',
    type: 'ngo',
    category: 'children',
    contact: {
      website: 'https://girlsontherun.org',
      email: '<EMAIL>'
    },
    availability: 'Program schedules vary',
    location: 'Multiple locations',
    description: 'Physical activity-based positive youth development program for girls in 3rd-8th grade.',
    isEmergency: false,
    ageGroup: 'Girls 3rd-8th grade'
  },
  {
    id: '8',
    name: 'National Women\'s Health Network',
    type: 'educational',
    category: 'women',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'https://nwhn.org'
    },
    availability: 'Business hours',
    location: 'National (USA)',
    description: 'Advocacy organization dedicated to women\'s health and rights, providing educational resources.',
    isEmergency: false
  }
]

export default function ResourcesPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')

  const filteredResources = resources.filter(resource => {
    const categoryMatch = selectedCategory === 'all' || resource.category === selectedCategory
    const typeMatch = selectedType === 'all' || resource.type === selectedType
    return categoryMatch && typeMatch
  })

  const emergencyResources = resources.filter(resource => resource.isEmergency)
  const nonEmergencyResources = filteredResources.filter(resource => !resource.isEmergency)

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'emergency': return 'bg-red-100 text-red-800'
      case 'helpline': return 'bg-blue-100 text-blue-800'
      case 'ngo': return 'bg-green-100 text-green-800'
      case 'educational': return 'bg-purple-100 text-purple-800'
      case 'therapy': return 'bg-pink-100 text-pink-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Crisis Resources & Support
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Immediate access to helplines, emergency services, and support organizations 
            for women and children in need of assistance.
          </p>
        </div>

        {/* Emergency Alert */}
        <div className="bg-red-50 border-l-4 border-red-400 p-6 mb-8">
          <div className="flex">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-400" />
            <div className="ml-3">
              <h3 className="text-lg font-medium text-red-800">
                In Immediate Danger?
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p className="mb-2">If you are in immediate physical danger, call <strong>911</strong> right away.</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {EMERGENCY_CONTACTS.slice(0, 2).map((contact, index) => (
                    <div key={index} className="bg-red-100 p-3 rounded">
                      <p className="font-semibold text-red-900">{contact.name}</p>
                      <p className="text-red-800 font-mono">{contact.phone}</p>
                      <p className="text-xs text-red-600">{contact.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                <option value="emergency">Emergency</option>
                <option value="mental_health">Mental Health</option>
                <option value="abuse">Abuse Support</option>
                <option value="women">Women's Resources</option>
                <option value="children">Children's Resources</option>
                <option value="education">Educational</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="emergency">Emergency Services</option>
                <option value="helpline">Helplines</option>
                <option value="ngo">Organizations</option>
                <option value="educational">Educational Resources</option>
                <option value="therapy">Therapy Services</option>
              </select>
            </div>
          </div>
        </div>

        {/* Emergency Resources */}
        {selectedCategory === 'all' || selectedCategory === 'emergency' ? (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-500 mr-2" />
              Emergency Resources
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {emergencyResources.map((resource) => (
                <div key={resource.id} className="bg-white border-l-4 border-red-500 rounded-lg shadow-md p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">{resource.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(resource.type)}`}>
                      {resource.type}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{resource.description}</p>
                  
                  <div className="space-y-2 mb-4">
                    {resource.contact.phone && (
                      <div className="flex items-center">
                        <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <a href={`tel:${resource.contact.phone}`} className="text-blue-600 hover:text-blue-800 font-mono">
                          {resource.contact.phone}
                        </a>
                      </div>
                    )}
                    {resource.contact.website && (
                      <div className="flex items-center">
                        <GlobeAltIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <a href={resource.contact.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                          Visit Website
                        </a>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {resource.availability}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : null}

        {/* Other Resources */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Support Resources
          </h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {nonEmergencyResources.map((resource) => (
              <div key={resource.id} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-semibold text-gray-900">{resource.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(resource.type)}`}>
                    {resource.type}
                  </span>
                </div>
                
                <p className="text-gray-600 mb-4">{resource.description}</p>
                
                <div className="space-y-2 mb-4">
                  {resource.contact.phone && (
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`tel:${resource.contact.phone}`} className="text-blue-600 hover:text-blue-800">
                        {resource.contact.phone}
                      </a>
                    </div>
                  )}
                  {resource.contact.email && (
                    <div className="flex items-center">
                      <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${resource.contact.email}`} className="text-blue-600 hover:text-blue-800">
                        {resource.contact.email}
                      </a>
                    </div>
                  )}
                  {resource.contact.website && (
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={resource.contact.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        Visit Website
                      </a>
                    </div>
                  )}
                  {resource.contact.address && (
                    <div className="flex items-center">
                      <MapPinIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">{resource.contact.address}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {resource.availability}
                  </div>
                  <span>{resource.location}</span>
                </div>
                
                {resource.languages && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">Languages: {resource.languages.join(', ')}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {filteredResources.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No resources found for the selected filters.</p>
          </div>
        )}

        {/* Safety Tips */}
        <div className="mt-16 bg-blue-50 rounded-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Safety Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">🔒 Online Safety</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Use private browsing mode when seeking help</li>
                <li>• Clear your browser history after visiting support sites</li>
                <li>• Use a safe computer or phone if possible</li>
                <li>• Have a safety plan for online activities</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">📞 When Calling for Help</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Find a safe, private place to make the call</li>
                <li>• Have important information ready</li>
                <li>• Know that calls to hotlines are confidential</li>
                <li>• It's okay to hang up if you don't feel safe</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
