export * from './Vector3d';
declare const _default: import("../../types").InternalPlugin<import("../../types").InputWithSettings<number[] | {
    [x: string]: number;
}, import("../Vector/vector-types").VectorSettings<number[] | {
    [x: string]: number;
}, "x" | "y" | "z">, "value">, number[] | {
    [x: string]: number;
}, unknown, {
    [x: string]: import("../Number/number-types").InternalNumberSettings;
} & {
    format: import("../Vector/vector-types").Format;
    keys: string[];
    lock: boolean;
    locked: boolean;
}>;
export default _default;
