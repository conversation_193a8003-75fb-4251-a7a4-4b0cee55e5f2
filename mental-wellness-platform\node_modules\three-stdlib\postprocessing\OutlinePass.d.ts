import { Pass, FullScreenQuad } from './Pass';
import { Camera, Color, Matrix4, MeshDepthMaterial, Object3D, Scene, ShaderMaterial, Texture, Vector2, WebGLRenderer, WebGLRenderTarget } from 'three';
declare class OutlinePass extends Pass {
    renderScene: Scene;
    renderCamera: Camera;
    selectedObjects: Object3D[];
    visibleEdgeColor: Color;
    hiddenEdgeColor: Color;
    edgeGlow: number;
    usePatternTexture: boolean;
    edgeThickness: number;
    edgeStrength: number;
    downSampleRatio: number;
    pulsePeriod: number;
    resolution: Vector2;
    renderTargetMaskBuffer: WebGLRenderTarget;
    depthMaterial: MeshDepthMaterial;
    prepareMaskMaterial: ShaderMaterial;
    renderTargetDepthBuffer: WebGLRenderTarget;
    renderTargetMaskDownSampleBuffer: WebGLRenderTarget;
    renderTargetBlurBuffer1: WebGLRenderTarget;
    renderTargetBlurBuffer2: WebGLRenderTarget;
    edgeDetectionMaterial: ShaderMaterial;
    renderTargetEdgeBuffer1: WebGLRenderTarget;
    renderTargetEdgeBuffer2: WebGLRenderTarget;
    separableBlurMaterial1: ShaderMaterial;
    separableBlurMaterial2: ShaderMaterial;
    overlayMaterial: ShaderMaterial;
    materialCopy: ShaderMaterial;
    oldClearAlpha: number;
    fsQuad: FullScreenQuad;
    tempPulseColor1: Color;
    tempPulseColor2: Color;
    textureMatrix: Matrix4;
    patternTexture?: Texture;
    private _visibilityCache;
    private _oldClearColor;
    copyUniforms: any;
    BlurDirectionX: Vector2;
    BlurDirectionY: Vector2;
    constructor(resolution: Vector2, scene: Scene, camera: Camera, selectedObjects?: Object3D[]);
    dispose(): void;
    setSize(width: number, height: number): void;
    changeVisibilityOfSelectedObjects(bVisible: boolean): void;
    changeVisibilityOfNonSelectedObjects(bVisible: boolean): void;
    updateTextureMatrix(): void;
    render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget, deltaTime: number, maskActive: boolean): void;
    getPrepareMaskMaterial(): ShaderMaterial;
    getEdgeDetectionMaterial(): ShaderMaterial;
    getSeperableBlurMaterial(maxRadius: number): ShaderMaterial;
    getOverlayMaterial(): ShaderMaterial;
}
export { OutlinePass };
