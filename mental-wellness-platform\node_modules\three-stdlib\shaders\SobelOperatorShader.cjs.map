{"version": 3, "file": "SobelOperatorShader.cjs", "sources": ["../../src/shaders/SobelOperatorShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Sobel Edge Detection (see https://youtu.be/uihBwtPIBxM)\n *\n * As mentioned in the video the Sobel operator expects a grayscale image as input.\n *\n */\n\nexport const SobelOperatorShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2() },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform vec2 resolution;\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 texel = vec2( 1.0 / resolution.x, 1.0 / resolution.y );\n\n    // kernel definition (in glsl matrices are filled in column-major order)\n\n    \tconst mat3 Gx = mat3( -1, -2, -1, 0, 0, 0, 1, 2, 1 ); // x direction kernel\n    \tconst mat3 Gy = mat3( -1, 0, 1, -2, 0, 2, -1, 0, 1 ); // y direction kernel\n\n    // fetch the 3x3 neighbourhood of a fragment\n\n    // first column\n\n    \tfloat tx0y0 = texture2D( tDiffuse, vUv + texel * vec2( -1, -1 ) ).r;\n    \tfloat tx0y1 = texture2D( tDiffuse, vUv + texel * vec2( -1,  0 ) ).r;\n    \tfloat tx0y2 = texture2D( tDiffuse, vUv + texel * vec2( -1,  1 ) ).r;\n\n    // second column\n\n    \tfloat tx1y0 = texture2D( tDiffuse, vUv + texel * vec2(  0, -1 ) ).r;\n    \tfloat tx1y1 = texture2D( tDiffuse, vUv + texel * vec2(  0,  0 ) ).r;\n    \tfloat tx1y2 = texture2D( tDiffuse, vUv + texel * vec2(  0,  1 ) ).r;\n\n    // third column\n\n    \tfloat tx2y0 = texture2D( tDiffuse, vUv + texel * vec2(  1, -1 ) ).r;\n    \tfloat tx2y1 = texture2D( tDiffuse, vUv + texel * vec2(  1,  0 ) ).r;\n    \tfloat tx2y2 = texture2D( tDiffuse, vUv + texel * vec2(  1,  1 ) ).r;\n\n    // gradient value in x direction\n\n    \tfloat valueGx = Gx[0][0] * tx0y0 + Gx[1][0] * tx1y0 + Gx[2][0] * tx2y0 + \n    \t\tGx[0][1] * tx0y1 + Gx[1][1] * tx1y1 + Gx[2][1] * tx2y1 + \n    \t\tGx[0][2] * tx0y2 + Gx[1][2] * tx1y2 + Gx[2][2] * tx2y2; \n\n    // gradient value in y direction\n\n    \tfloat valueGy = Gy[0][0] * tx0y0 + Gy[1][0] * tx1y0 + Gy[2][0] * tx2y0 + \n    \t\tGy[0][1] * tx0y1 + Gy[1][1] * tx1y1 + Gy[2][1] * tx2y1 + \n    \t\tGy[0][2] * tx0y2 + Gy[1][2] * tx1y2 + Gy[2][2] * tx2y2; \n\n    // magnitute of the total gradient\n\n    \tfloat G = sqrt( ( valueGx * valueGx ) + ( valueGy * valueGy ) );\n\n    \tgl_FragColor = vec4( vec3( G ), 1 );\n\n    }\n  `,\n}\n"], "names": ["Vector2"], "mappings": ";;;AASO,MAAM,sBAAsB;AAAA,EACjC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAuB,oBAAIA,MAAAA,UAAU;AAAA,EACrD;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsD7B;;"}