{"version": 3, "file": "PRWMLoader.js", "sources": ["../../src/loaders/PRWMLoader.js"], "sourcesContent": ["import { <PERSON>uffer<PERSON>tt<PERSON><PERSON><PERSON>, <PERSON>ufferGeometry, FileLoader, Loader } from 'three'\n\n/**\n * See https://github.com/kchapelier/PRWM for more informations about this file format\n */\n\nlet bigEndianPlatform = null\n\n/**\n * Check if the endianness of the platform is big-endian (most significant bit first)\n * @returns {boolean} True if big-endian, false if little-endian\n */\nfunction isBigEndianPlatform() {\n  if (bigEndianPlatform === null) {\n    const buffer = new ArrayBuffer(2),\n      uint8Array = new Uint8Array(buffer),\n      uint16Array = new Uint16Array(buffer)\n\n    uint8Array[0] = 0xaa // set first byte\n    uint8Array[1] = 0xbb // set second byte\n    bigEndianPlatform = uint16Array[0] === 0xaabb\n  }\n\n  return bigEndianPlatform\n}\n\n// match the values defined in the spec to the TypedArray types\nconst InvertedEncodingTypes = [\n  null,\n  Float32Array,\n  null,\n  Int8Array,\n  Int16Array,\n  null,\n  Int32<PERSON>rray,\n  U<PERSON>8<PERSON><PERSON>y,\n  Uint16<PERSON>rray,\n  null,\n  Uint32Array,\n]\n\n// define the method to use on a DataView, corresponding the TypedArray type\nconst getMethods = {\n  Uint16Array: 'getUint16',\n  Uint32Array: 'getUint32',\n  Int16Array: 'getInt16',\n  Int32Array: 'getInt32',\n  Float32Array: 'getFloat32',\n  Float64Array: 'getFloat64',\n}\n\nfunction copyFromBuffer(sourceArrayBuffer, viewType, position, length, fromBigEndian) {\n  const bytesPerElement = viewType.BYTES_PER_ELEMENT\n  let result\n\n  if (fromBigEndian === isBigEndianPlatform() || bytesPerElement === 1) {\n    result = new viewType(sourceArrayBuffer, position, length)\n  } else {\n    const readView = new DataView(sourceArrayBuffer, position, length * bytesPerElement),\n      getMethod = getMethods[viewType.name],\n      littleEndian = !fromBigEndian\n\n    result = new viewType(length)\n\n    for (let i = 0; i < length; i++) {\n      result[i] = readView[getMethod](i * bytesPerElement, littleEndian)\n    }\n  }\n\n  return result\n}\n\nfunction decodePrwm(buffer) {\n  const array = new Uint8Array(buffer),\n    version = array[0]\n\n  let flags = array[1]\n\n  const indexedGeometry = !!((flags >> 7) & 0x01),\n    indicesType = (flags >> 6) & 0x01,\n    bigEndian = ((flags >> 5) & 0x01) === 1,\n    attributesNumber = flags & 0x1f\n\n  let valuesNumber = 0,\n    indicesNumber = 0\n\n  if (bigEndian) {\n    valuesNumber = (array[2] << 16) + (array[3] << 8) + array[4]\n    indicesNumber = (array[5] << 16) + (array[6] << 8) + array[7]\n  } else {\n    valuesNumber = array[2] + (array[3] << 8) + (array[4] << 16)\n    indicesNumber = array[5] + (array[6] << 8) + (array[7] << 16)\n  }\n\n  /** PRELIMINARY CHECKS **/\n\n  if (version === 0) {\n    throw new Error('PRWM decoder: Invalid format version: 0')\n  } else if (version !== 1) {\n    throw new Error('PRWM decoder: Unsupported format version: ' + version)\n  }\n\n  if (!indexedGeometry) {\n    if (indicesType !== 0) {\n      throw new Error('PRWM decoder: Indices type must be set to 0 for non-indexed geometries')\n    } else if (indicesNumber !== 0) {\n      throw new Error('PRWM decoder: Number of indices must be set to 0 for non-indexed geometries')\n    }\n  }\n\n  /** PARSING **/\n\n  let pos = 8\n\n  const attributes = {}\n\n  for (let i = 0; i < attributesNumber; i++) {\n    let attributeName = ''\n\n    while (pos < array.length) {\n      const char = array[pos]\n      pos++\n\n      if (char === 0) {\n        break\n      } else {\n        attributeName += String.fromCharCode(char)\n      }\n    }\n\n    flags = array[pos]\n\n    const attributeType = (flags >> 7) & 0x01\n    const cardinality = ((flags >> 4) & 0x03) + 1\n    const encodingType = flags & 0x0f\n    const arrayType = InvertedEncodingTypes[encodingType]\n\n    pos++\n\n    // padding to next multiple of 4\n    pos = Math.ceil(pos / 4) * 4\n\n    const values = copyFromBuffer(buffer, arrayType, pos, cardinality * valuesNumber, bigEndian)\n\n    pos += arrayType.BYTES_PER_ELEMENT * cardinality * valuesNumber\n\n    attributes[attributeName] = {\n      type: attributeType,\n      cardinality: cardinality,\n      values: values,\n    }\n  }\n\n  pos = Math.ceil(pos / 4) * 4\n\n  let indices = null\n\n  if (indexedGeometry) {\n    indices = copyFromBuffer(buffer, indicesType === 1 ? Uint32Array : Uint16Array, pos, indicesNumber, bigEndian)\n  }\n\n  return {\n    version: version,\n    attributes: attributes,\n    indices: indices,\n  }\n}\n\n// Define the public interface\n\nconst PRWMLoader = /* @__PURE__ */ (() => {\n  class PRWMLoader extends Loader {\n    constructor(manager) {\n      super(manager)\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const scope = this\n\n      const loader = new FileLoader(scope.manager)\n      loader.setPath(scope.path)\n      loader.setResponseType('arraybuffer')\n      loader.setRequestHeader(scope.requestHeader)\n      loader.setWithCredentials(scope.withCredentials)\n\n      url = url.replace(/\\*/g, isBigEndianPlatform() ? 'be' : 'le')\n\n      loader.load(\n        url,\n        function (arrayBuffer) {\n          try {\n            onLoad(scope.parse(arrayBuffer))\n          } catch (e) {\n            if (onError) {\n              onError(e)\n            } else {\n              console.error(e)\n            }\n\n            scope.manager.itemError(url)\n          }\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    parse(arrayBuffer) {\n      const data = decodePrwm(arrayBuffer),\n        attributesKey = Object.keys(data.attributes),\n        bufferGeometry = new BufferGeometry()\n\n      for (let i = 0; i < attributesKey.length; i++) {\n        const attribute = data.attributes[attributesKey[i]]\n        bufferGeometry.setAttribute(\n          attributesKey[i],\n          new BufferAttribute(attribute.values, attribute.cardinality, attribute.normalized),\n        )\n      }\n\n      if (data.indices !== null) {\n        bufferGeometry.setIndex(new BufferAttribute(data.indices, 1))\n      }\n\n      return bufferGeometry\n    }\n\n    static isBigEndianPlatform() {\n      return isBigEndianPlatform()\n    }\n  }\n\n  return PRWMLoader\n})()\n\nexport { PRWMLoader }\n"], "names": ["PRW<PERSON><PERSON>der"], "mappings": ";AAMA,IAAI,oBAAoB;AAMxB,SAAS,sBAAsB;AAC7B,MAAI,sBAAsB,MAAM;AAC9B,UAAM,SAAS,IAAI,YAAY,CAAC,GAC9B,aAAa,IAAI,WAAW,MAAM,GAClC,cAAc,IAAI,YAAY,MAAM;AAEtC,eAAW,CAAC,IAAI;AAChB,eAAW,CAAC,IAAI;AAChB,wBAAoB,YAAY,CAAC,MAAM;AAAA,EACxC;AAED,SAAO;AACT;AAGA,MAAM,wBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,MAAM,aAAa;AAAA,EACjB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAChB;AAEA,SAAS,eAAe,mBAAmB,UAAU,UAAU,QAAQ,eAAe;AACpF,QAAM,kBAAkB,SAAS;AACjC,MAAI;AAEJ,MAAI,kBAAkB,yBAAyB,oBAAoB,GAAG;AACpE,aAAS,IAAI,SAAS,mBAAmB,UAAU,MAAM;AAAA,EAC7D,OAAS;AACL,UAAM,WAAW,IAAI,SAAS,mBAAmB,UAAU,SAAS,eAAe,GACjF,YAAY,WAAW,SAAS,IAAI,GACpC,eAAe,CAAC;AAElB,aAAS,IAAI,SAAS,MAAM;AAE5B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,aAAO,CAAC,IAAI,SAAS,SAAS,EAAE,IAAI,iBAAiB,YAAY;AAAA,IAClE;AAAA,EACF;AAED,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ;AAC1B,QAAM,QAAQ,IAAI,WAAW,MAAM,GACjC,UAAU,MAAM,CAAC;AAEnB,MAAI,QAAQ,MAAM,CAAC;AAEnB,QAAM,kBAAkB,CAAC,EAAG,SAAS,IAAK,IACxC,cAAe,SAAS,IAAK,GAC7B,aAAc,SAAS,IAAK,OAAU,GACtC,mBAAmB,QAAQ;AAE7B,MAAI,eAAe,GACjB,gBAAgB;AAElB,MAAI,WAAW;AACb,oBAAgB,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC;AAC3D,qBAAiB,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC;AAAA,EAChE,OAAS;AACL,mBAAe,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK;AACzD,oBAAgB,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK;AAAA,EAC3D;AAID,MAAI,YAAY,GAAG;AACjB,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC7D,WAAa,YAAY,GAAG;AACxB,UAAM,IAAI,MAAM,+CAA+C,OAAO;AAAA,EACvE;AAED,MAAI,CAAC,iBAAiB;AACpB,QAAI,gBAAgB,GAAG;AACrB,YAAM,IAAI,MAAM,wEAAwE;AAAA,IAC9F,WAAe,kBAAkB,GAAG;AAC9B,YAAM,IAAI,MAAM,6EAA6E;AAAA,IAC9F;AAAA,EACF;AAID,MAAI,MAAM;AAEV,QAAM,aAAa,CAAE;AAErB,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,QAAI,gBAAgB;AAEpB,WAAO,MAAM,MAAM,QAAQ;AACzB,YAAM,OAAO,MAAM,GAAG;AACtB;AAEA,UAAI,SAAS,GAAG;AACd;AAAA,MACR,OAAa;AACL,yBAAiB,OAAO,aAAa,IAAI;AAAA,MAC1C;AAAA,IACF;AAED,YAAQ,MAAM,GAAG;AAEjB,UAAM,gBAAiB,SAAS,IAAK;AACrC,UAAM,eAAgB,SAAS,IAAK,KAAQ;AAC5C,UAAM,eAAe,QAAQ;AAC7B,UAAM,YAAY,sBAAsB,YAAY;AAEpD;AAGA,UAAM,KAAK,KAAK,MAAM,CAAC,IAAI;AAE3B,UAAM,SAAS,eAAe,QAAQ,WAAW,KAAK,cAAc,cAAc,SAAS;AAE3F,WAAO,UAAU,oBAAoB,cAAc;AAEnD,eAAW,aAAa,IAAI;AAAA,MAC1B,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAED,QAAM,KAAK,KAAK,MAAM,CAAC,IAAI;AAE3B,MAAI,UAAU;AAEd,MAAI,iBAAiB;AACnB,cAAU,eAAe,QAAQ,gBAAgB,IAAI,cAAc,aAAa,KAAK,eAAe,SAAS;AAAA,EAC9G;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACH;AAIK,MAAC,aAA8B,uBAAM;AACxC,QAAMA,oBAAmB,OAAO;AAAA,IAC9B,YAAY,SAAS;AACnB,YAAM,OAAO;AAAA,IACd;AAAA,IAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,YAAM,QAAQ;AAEd,YAAM,SAAS,IAAI,WAAW,MAAM,OAAO;AAC3C,aAAO,QAAQ,MAAM,IAAI;AACzB,aAAO,gBAAgB,aAAa;AACpC,aAAO,iBAAiB,MAAM,aAAa;AAC3C,aAAO,mBAAmB,MAAM,eAAe;AAE/C,YAAM,IAAI,QAAQ,OAAO,oBAAqB,IAAG,OAAO,IAAI;AAE5D,aAAO;AAAA,QACL;AAAA,QACA,SAAU,aAAa;AACrB,cAAI;AACF,mBAAO,MAAM,MAAM,WAAW,CAAC;AAAA,UAChC,SAAQ,GAAP;AACA,gBAAI,SAAS;AACX,sBAAQ,CAAC;AAAA,YACvB,OAAmB;AACL,sBAAQ,MAAM,CAAC;AAAA,YAChB;AAED,kBAAM,QAAQ,UAAU,GAAG;AAAA,UAC5B;AAAA,QACF;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAAA,IACF;AAAA,IAED,MAAM,aAAa;AACjB,YAAM,OAAO,WAAW,WAAW,GACjC,gBAAgB,OAAO,KAAK,KAAK,UAAU,GAC3C,iBAAiB,IAAI,eAAgB;AAEvC,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,cAAM,YAAY,KAAK,WAAW,cAAc,CAAC,CAAC;AAClD,uBAAe;AAAA,UACb,cAAc,CAAC;AAAA,UACf,IAAI,gBAAgB,UAAU,QAAQ,UAAU,aAAa,UAAU,UAAU;AAAA,QAClF;AAAA,MACF;AAED,UAAI,KAAK,YAAY,MAAM;AACzB,uBAAe,SAAS,IAAI,gBAAgB,KAAK,SAAS,CAAC,CAAC;AAAA,MAC7D;AAED,aAAO;AAAA,IACR;AAAA,IAED,OAAO,sBAAsB;AAC3B,aAAO,oBAAqB;AAAA,IAC7B;AAAA,EACF;AAED,SAAOA;AACT,GAAC;"}