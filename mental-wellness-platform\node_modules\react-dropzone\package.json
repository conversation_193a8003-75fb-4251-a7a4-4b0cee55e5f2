{"name": "react-dropzone", "description": "Simple HTML5 drag-drop zone with React.js", "main": "dist/index.js", "module": "dist/es/index.js", "sideEffects": false, "scripts": {"cz": "git-cz", "clean": "rimraf ./dist", "build": "yarn clean && yarn build:umd && yarn build:es", "build:umd": "cross-env NODE_ENV=es rollup -c", "build:es": "cross-env BABEL_ENV=es babel ./src --out-dir ./dist/es --ignore '**/*.spec.js'", "start": "styleguidist server", "styleguide": "styleguidist build", "test": "cross-env NODE_ENV=test yarn lint && jest --coverage && yarn typescript", "test:watch": "cross-env NODE_ENV=test jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "commitmsg": "commitlint -e", "prepublish": "yarn build && yarn size", "_postinstall": "husky install", "prepublishOnly": "pinst --disable", "postpublish": "pinst --enable", "logo": "cd logo && sketchtool export artboards logo.sketch", "imagemin": "imagemin --out-dir=logo --plugin=pngquant --plugin=svgo", "size": "size-limit", "size:why": "size-limit --why", "typescript": "tsc --project ./typings/tests"}, "size-limit": [{"path": "dist/index.js", "limit": "7.5 KB"}, {"path": "dist/es/index.js", "limit": "7.5 KB"}], "lint-staged": {"*.js": ["eslint . --fix"], "*.ts": ["eslint ."], "*.{svg,png}": ["imagemin"]}, "config": {"commitizen": {"path": "@commitlint/prompt"}}, "jest": {"testEnvironment": "jsdom", "clearMocks": true, "setupFilesAfterEnv": ["<rootDir>/testSetup.js"], "coveragePathIgnorePatterns": ["/dist/", "/node_modules/", "<rootDir>/testSetup.js"], "testPathIgnorePatterns": ["/node_modules/", "/dist/"]}, "keywords": ["react-component", "react", "drag", "drop", "upload"], "repository": {"type": "git", "url": "https://github.com/react-dropzone/react-dropzone.git"}, "bugs": {"url": "https://github.com/react-dropzone/react-dropzone/issues"}, "homepage": "https://github.com/react-dropzone/react-dropzone", "author": "<PERSON><PERSON>", "contributors": ["<PERSON><PERSON> <<EMAIL>> (http://okonet.ru)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "peerDependencies": {"react": ">= 16.8"}, "dependencies": {"attr-accept": "^2.2.2", "file-selector": "^0.5.0", "prop-types": "^15.8.1"}, "devDependencies": {"@babel/cli": "^7.16.8", "@babel/core": "^7.16.12", "@babel/eslint-parser": "^7.16.5", "@babel/plugin-external-helpers": "^7.16.7", "@babel/plugin-proposal-do-expressions": "^7.16.7", "@babel/plugin-proposal-export-default-from": "^7.16.7", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-pipeline-operator": "^7.16.7", "@babel/plugin-transform-runtime": "^7.16.10", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.16.7", "@babel/register": "^7.16.9", "@commitlint/cli": "^16.1.0", "@commitlint/config-angular": "^16.0.0", "@commitlint/prompt": "^16.1.0", "@commitlint/prompt-cli": "^16.1.0", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "@size-limit/preset-small-lib": "^7.0.5", "@size-limit/webpack": "^7.0.5", "@size-limit/webpack-why": "^7.0.5", "@testing-library/dom": "^8.11.3", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/react-hooks": "^7.0.2", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "babel-jest": "^27.4.6", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-dynamic-import-node": "^2.3.3", "commitizen": "^4.2.4", "cross-env": "^7.0.3", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^7.0.4", "imagemin-cli": "^7.0.0", "imagemin-pngquant": "^9.0.2", "jest": "^27.4.7", "lint-staged": "^12.3.2", "markdownlint-cli": "^0.30.0", "pinst": "^2.1.6", "prettier": "^2.5.1", "react": "^17.0.0", "react-dom": "^17.0.0", "react-styleguidist": "^11.2.0", "react-test-renderer": "^17.0.0", "rimraf": "^3.0.2", "rollup": "^2.66.1", "rollup-plugin-terser": "^7.0.2", "size-limit": "^7.0.5", "style-loader": "^3.3.1", "styled-components": "^5.3.3", "typescript": "^4.0.2", "webpack": "^5.67.0", "webpack-blocks": "^2.1.0"}, "typings": "typings/react-dropzone.d.ts", "version": "12.1.0", "engines": {"node": ">= 10.13"}, "browserslist": ["defaults"]}