import type { InternalColorSettings, Format, ColorInput } from './color-types';
export declare const schema: (o: any) => any;
export declare const sanitize: (v: any, settings: InternalColorSettings) => any;
export declare const format: (v: any, settings: InternalColorSettings) => any;
export declare const normalize: ({ value }: ColorInput) => {
    value: any;
    settings: {
        format: Format;
        hasAlpha: boolean;
        isString: boolean;
    };
};
