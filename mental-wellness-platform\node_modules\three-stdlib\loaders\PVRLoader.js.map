{"version": 3, "file": "PVRLoader.js", "sources": ["../../src/loaders/PVRLoader.js"], "sourcesContent": ["import {\n  CompressedTextureLoader,\n  RGBA_PVRTC_2BPPV1_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGB_PVRTC_2BPPV1_Format,\n  RGB_PVRTC_4BPPV1_Format,\n} from 'three'\n\n/*\n *\t PVR v2 (legacy) parser\n *   TODO : Add Support for PVR v3 format\n *   TODO : implement loadMipmaps option\n */\n\nclass PVRLoader extends CompressedTextureLoader {\n  constructor(manager) {\n    super(manager)\n  }\n\n  parse(buffer, loadMipmaps) {\n    const headerLengthInt = 13\n    const header = new Uint32Array(buffer, 0, headerLengthInt)\n\n    const pvrDatas = {\n      buffer: buffer,\n      header: header,\n      loadMipmaps: loadMipmaps,\n    }\n\n    if (header[0] === 0x03525650) {\n      // PVR v3\n\n      return _parseV3(pvrDatas)\n    } else if (header[11] === 0x21525650) {\n      // PVR v2\n\n      return _parseV2(pvrDatas)\n    } else {\n      console.error('THREE.PVRLoader: Unknown PVR format.')\n    }\n  }\n}\n\nfunction _parseV3(pvrDatas) {\n  const header = pvrDatas.header\n  let bpp, format\n\n  const metaLen = header[12],\n    pixelFormat = header[2],\n    height = header[6],\n    width = header[7],\n    // numSurfs = header[ 9 ],\n    numFaces = header[10],\n    numMipmaps = header[11]\n\n  switch (pixelFormat) {\n    case 0: // PVRTC 2bpp RGB\n      bpp = 2\n      format = RGB_PVRTC_2BPPV1_Format\n      break\n\n    case 1: // PVRTC 2bpp RGBA\n      bpp = 2\n      format = RGBA_PVRTC_2BPPV1_Format\n      break\n\n    case 2: // PVRTC 4bpp RGB\n      bpp = 4\n      format = RGB_PVRTC_4BPPV1_Format\n      break\n\n    case 3: // PVRTC 4bpp RGBA\n      bpp = 4\n      format = RGBA_PVRTC_4BPPV1_Format\n      break\n\n    default:\n      console.error('THREE.PVRLoader: Unsupported PVR format:', pixelFormat)\n  }\n\n  pvrDatas.dataPtr = 52 + metaLen\n  pvrDatas.bpp = bpp\n  pvrDatas.format = format\n  pvrDatas.width = width\n  pvrDatas.height = height\n  pvrDatas.numSurfaces = numFaces\n  pvrDatas.numMipmaps = numMipmaps\n  pvrDatas.isCubemap = numFaces === 6\n\n  return _extract(pvrDatas)\n}\n\nfunction _parseV2(pvrDatas) {\n  const header = pvrDatas.header\n\n  const headerLength = header[0],\n    height = header[1],\n    width = header[2],\n    numMipmaps = header[3],\n    flags = header[4],\n    // dataLength = header[ 5 ],\n    // bpp =  header[ 6 ],\n    // bitmaskRed = header[ 7 ],\n    // bitmaskGreen = header[ 8 ],\n    // bitmaskBlue = header[ 9 ],\n    bitmaskAlpha = header[10],\n    // pvrTag = header[ 11 ],\n    numSurfs = header[12]\n\n  const TYPE_MASK = 0xff\n  const PVRTC_2 = 24,\n    PVRTC_4 = 25\n\n  const formatFlags = flags & TYPE_MASK\n\n  let bpp, format\n  const _hasAlpha = bitmaskAlpha > 0\n\n  if (formatFlags === PVRTC_4) {\n    format = _hasAlpha ? RGBA_PVRTC_4BPPV1_Format : RGB_PVRTC_4BPPV1_Format\n    bpp = 4\n  } else if (formatFlags === PVRTC_2) {\n    format = _hasAlpha ? RGBA_PVRTC_2BPPV1_Format : RGB_PVRTC_2BPPV1_Format\n    bpp = 2\n  } else {\n    console.error('THREE.PVRLoader: Unknown PVR format:', formatFlags)\n  }\n\n  pvrDatas.dataPtr = headerLength\n  pvrDatas.bpp = bpp\n  pvrDatas.format = format\n  pvrDatas.width = width\n  pvrDatas.height = height\n  pvrDatas.numSurfaces = numSurfs\n  pvrDatas.numMipmaps = numMipmaps + 1\n\n  // guess cubemap type seems tricky in v2\n  // it juste a pvr containing 6 surface (no explicit cubemap type)\n  pvrDatas.isCubemap = numSurfs === 6\n\n  return _extract(pvrDatas)\n}\n\nfunction _extract(pvrDatas) {\n  const pvr = {\n    mipmaps: [],\n    width: pvrDatas.width,\n    height: pvrDatas.height,\n    format: pvrDatas.format,\n    mipmapCount: pvrDatas.numMipmaps,\n    isCubemap: pvrDatas.isCubemap,\n  }\n\n  const buffer = pvrDatas.buffer\n\n  let dataOffset = pvrDatas.dataPtr,\n    dataSize = 0,\n    blockSize = 0,\n    blockWidth = 0,\n    blockHeight = 0,\n    widthBlocks = 0,\n    heightBlocks = 0\n\n  const bpp = pvrDatas.bpp,\n    numSurfs = pvrDatas.numSurfaces\n\n  if (bpp === 2) {\n    blockWidth = 8\n    blockHeight = 4\n  } else {\n    blockWidth = 4\n    blockHeight = 4\n  }\n\n  blockSize = (blockWidth * blockHeight * bpp) / 8\n\n  pvr.mipmaps.length = pvrDatas.numMipmaps * numSurfs\n\n  let mipLevel = 0\n\n  while (mipLevel < pvrDatas.numMipmaps) {\n    const sWidth = pvrDatas.width >> mipLevel,\n      sHeight = pvrDatas.height >> mipLevel\n\n    widthBlocks = sWidth / blockWidth\n    heightBlocks = sHeight / blockHeight\n\n    // Clamp to minimum number of blocks\n    if (widthBlocks < 2) widthBlocks = 2\n    if (heightBlocks < 2) heightBlocks = 2\n\n    dataSize = widthBlocks * heightBlocks * blockSize\n\n    for (let surfIndex = 0; surfIndex < numSurfs; surfIndex++) {\n      const byteArray = new Uint8Array(buffer, dataOffset, dataSize)\n\n      const mipmap = {\n        data: byteArray,\n        width: sWidth,\n        height: sHeight,\n      }\n\n      pvr.mipmaps[surfIndex * pvrDatas.numMipmaps + mipLevel] = mipmap\n\n      dataOffset += dataSize\n    }\n\n    mipLevel++\n  }\n\n  return pvr\n}\n\nexport { PVRLoader }\n"], "names": [], "mappings": ";AAcA,MAAM,kBAAkB,wBAAwB;AAAA,EAC9C,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACd;AAAA,EAED,MAAM,QAAQ,aAAa;AACzB,UAAM,kBAAkB;AACxB,UAAM,SAAS,IAAI,YAAY,QAAQ,GAAG,eAAe;AAEzD,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAED,QAAI,OAAO,CAAC,MAAM,UAAY;AAG5B,aAAO,SAAS,QAAQ;AAAA,IACzB,WAAU,OAAO,EAAE,MAAM,WAAY;AAGpC,aAAO,SAAS,QAAQ;AAAA,IAC9B,OAAW;AACL,cAAQ,MAAM,sCAAsC;AAAA,IACrD;AAAA,EACF;AACH;AAEA,SAAS,SAAS,UAAU;AAC1B,QAAM,SAAS,SAAS;AACxB,MAAI,KAAK;AAET,QAAM,UAAU,OAAO,EAAE,GACvB,cAAc,OAAO,CAAC,GACtB,SAAS,OAAO,CAAC,GACjB,QAAQ,OAAO,CAAC,GAEhB,WAAW,OAAO,EAAE,GACpB,aAAa,OAAO,EAAE;AAExB,UAAQ,aAAW;AAAA,IACjB,KAAK;AACH,YAAM;AACN,eAAS;AACT;AAAA,IAEF,KAAK;AACH,YAAM;AACN,eAAS;AACT;AAAA,IAEF,KAAK;AACH,YAAM;AACN,eAAS;AACT;AAAA,IAEF,KAAK;AACH,YAAM;AACN,eAAS;AACT;AAAA,IAEF;AACE,cAAQ,MAAM,4CAA4C,WAAW;AAAA,EACxE;AAED,WAAS,UAAU,KAAK;AACxB,WAAS,MAAM;AACf,WAAS,SAAS;AAClB,WAAS,QAAQ;AACjB,WAAS,SAAS;AAClB,WAAS,cAAc;AACvB,WAAS,aAAa;AACtB,WAAS,YAAY,aAAa;AAElC,SAAO,SAAS,QAAQ;AAC1B;AAEA,SAAS,SAAS,UAAU;AAC1B,QAAM,SAAS,SAAS;AAExB,QAAM,eAAe,OAAO,CAAC,GAC3B,SAAS,OAAO,CAAC,GACjB,QAAQ,OAAO,CAAC,GAChB,aAAa,OAAO,CAAC,GACrB,QAAQ,OAAO,CAAC,GAMhB,eAAe,OAAO,EAAE,GAExB,WAAW,OAAO,EAAE;AAEtB,QAAM,YAAY;AAClB,QAAM,UAAU,IACd,UAAU;AAEZ,QAAM,cAAc,QAAQ;AAE5B,MAAI,KAAK;AACT,QAAM,YAAY,eAAe;AAEjC,MAAI,gBAAgB,SAAS;AAC3B,aAAS,YAAY,2BAA2B;AAChD,UAAM;AAAA,EACV,WAAa,gBAAgB,SAAS;AAClC,aAAS,YAAY,2BAA2B;AAChD,UAAM;AAAA,EACV,OAAS;AACL,YAAQ,MAAM,wCAAwC,WAAW;AAAA,EAClE;AAED,WAAS,UAAU;AACnB,WAAS,MAAM;AACf,WAAS,SAAS;AAClB,WAAS,QAAQ;AACjB,WAAS,SAAS;AAClB,WAAS,cAAc;AACvB,WAAS,aAAa,aAAa;AAInC,WAAS,YAAY,aAAa;AAElC,SAAO,SAAS,QAAQ;AAC1B;AAEA,SAAS,SAAS,UAAU;AAC1B,QAAM,MAAM;AAAA,IACV,SAAS,CAAE;AAAA,IACX,OAAO,SAAS;AAAA,IAChB,QAAQ,SAAS;AAAA,IACjB,QAAQ,SAAS;AAAA,IACjB,aAAa,SAAS;AAAA,IACtB,WAAW,SAAS;AAAA,EACrB;AAED,QAAM,SAAS,SAAS;AAExB,MAAI,aAAa,SAAS,SACxB,WAAW,GACX,YAAY,GACZ,aAAa,GACb,cAAc,GACd,cAAc,GACd,eAAe;AAEjB,QAAM,MAAM,SAAS,KACnB,WAAW,SAAS;AAEtB,MAAI,QAAQ,GAAG;AACb,iBAAa;AACb,kBAAc;AAAA,EAClB,OAAS;AACL,iBAAa;AACb,kBAAc;AAAA,EACf;AAED,cAAa,aAAa,cAAc,MAAO;AAE/C,MAAI,QAAQ,SAAS,SAAS,aAAa;AAE3C,MAAI,WAAW;AAEf,SAAO,WAAW,SAAS,YAAY;AACrC,UAAM,SAAS,SAAS,SAAS,UAC/B,UAAU,SAAS,UAAU;AAE/B,kBAAc,SAAS;AACvB,mBAAe,UAAU;AAGzB,QAAI,cAAc;AAAG,oBAAc;AACnC,QAAI,eAAe;AAAG,qBAAe;AAErC,eAAW,cAAc,eAAe;AAExC,aAAS,YAAY,GAAG,YAAY,UAAU,aAAa;AACzD,YAAM,YAAY,IAAI,WAAW,QAAQ,YAAY,QAAQ;AAE7D,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAED,UAAI,QAAQ,YAAY,SAAS,aAAa,QAAQ,IAAI;AAE1D,oBAAc;AAAA,IACf;AAED;AAAA,EACD;AAED,SAAO;AACT;"}