import { NextRequest, NextResponse } from 'next/server'

// Mock resources data - in a real app, this would come from a database
const resources = [
  {
    id: '1',
    name: 'National Suicide Prevention Lifeline',
    type: 'emergency',
    category: 'emergency',
    contact: {
      phone: '988',
      website: 'https://suicidepreventionlifeline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Free and confidential emotional support for people in suicidal crisis or emotional distress.',
    isEmergency: true,
    languages: ['English', 'Spanish']
  },
  {
    id: '2',
    name: 'Crisis Text Line',
    type: 'emergency',
    category: 'emergency',
    contact: {
      phone: 'Text HOME to 741741',
      website: 'https://crisistextline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Free, 24/7 support for those in crisis. Text with a trained crisis counselor.',
    isEmergency: true,
    languages: ['English', 'Spanish']
  },
  {
    id: '3',
    name: 'National Child Abuse Hotline',
    type: 'helpline',
    category: 'children',
    contact: {
      phone: '1-800-4-A-CHILD (**************)',
      website: 'https://childhelp.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Professional crisis counselors provide intervention, information and referrals to thousands of callers each day.',
    isEmergency: true,
    languages: ['English', 'Spanish'],
    ageGroup: 'Children & Teens'
  },
  {
    id: '4',
    name: 'National Domestic Violence Hotline',
    type: 'helpline',
    category: 'women',
    contact: {
      phone: '1-************',
      website: 'https://thehotline.org'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Confidential support for women experiencing domestic violence, available 24/7 in over 200 languages.',
    isEmergency: true,
    languages: ['200+ languages available']
  },
  {
    id: '5',
    name: 'SAMHSA National Helpline',
    type: 'helpline',
    category: 'mental_health',
    contact: {
      phone: '1-************',
      website: 'https://samhsa.gov'
    },
    availability: '24/7',
    location: 'National (USA)',
    description: 'Treatment referral and information service for individuals and families facing mental health and/or substance use disorders.',
    isEmergency: false,
    languages: ['English', 'Spanish']
  },
  {
    id: '6',
    name: 'National Alliance on Mental Illness (NAMI)',
    type: 'ngo',
    category: 'mental_health',
    contact: {
      phone: '1-************',
      email: '<EMAIL>',
      website: 'https://nami.org'
    },
    availability: 'Mon-Fri 10am-10pm ET',
    location: 'National (USA)',
    description: 'Support, education and advocacy for individuals and families affected by mental illness.',
    isEmergency: false,
    languages: ['English', 'Spanish']
  },
  {
    id: '7',
    name: 'Girls on the Run',
    type: 'ngo',
    category: 'children',
    contact: {
      website: 'https://girlsontherun.org',
      email: '<EMAIL>'
    },
    availability: 'Program schedules vary',
    location: 'Multiple locations',
    description: 'Physical activity-based positive youth development program for girls in 3rd-8th grade.',
    isEmergency: false,
    ageGroup: 'Girls 3rd-8th grade'
  },
  {
    id: '8',
    name: 'National Women\'s Health Network',
    type: 'educational',
    category: 'women',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'https://nwhn.org'
    },
    availability: 'Business hours',
    location: 'National (USA)',
    description: 'Advocacy organization dedicated to women\'s health and rights, providing educational resources.',
    isEmergency: false
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const type = searchParams.get('type')
    const emergency = searchParams.get('emergency')
    const location = searchParams.get('location')

    let filteredResources = resources

    // Filter by category
    if (category && category !== 'all') {
      filteredResources = filteredResources.filter(resource => resource.category === category)
    }

    // Filter by type
    if (type && type !== 'all') {
      filteredResources = filteredResources.filter(resource => resource.type === type)
    }

    // Filter by emergency status
    if (emergency === 'true') {
      filteredResources = filteredResources.filter(resource => resource.isEmergency)
    } else if (emergency === 'false') {
      filteredResources = filteredResources.filter(resource => !resource.isEmergency)
    }

    // Filter by location (basic text search)
    if (location) {
      filteredResources = filteredResources.filter(resource => 
        resource.location.toLowerCase().includes(location.toLowerCase())
      )
    }

    // Separate emergency and non-emergency resources
    const emergencyResources = filteredResources.filter(resource => resource.isEmergency)
    const nonEmergencyResources = filteredResources.filter(resource => !resource.isEmergency)

    return NextResponse.json({
      success: true,
      data: {
        emergency: emergencyResources,
        nonEmergency: nonEmergencyResources,
        all: filteredResources
      },
      total: filteredResources.length
    })

  } catch (error) {
    console.error('Resources API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, resourceId, userId, feedback } = await request.json()

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'contact':
        // Log resource contact attempt
        return NextResponse.json({
          success: true,
          data: {
            message: 'Contact attempt logged',
            resourceId,
            contactedAt: new Date().toISOString()
          }
        })

      case 'feedback':
        // Save feedback about a resource
        if (!resourceId || !feedback) {
          return NextResponse.json(
            { error: 'Resource ID and feedback are required' },
            { status: 400 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            message: 'Feedback saved successfully',
            resourceId,
            feedback,
            submittedAt: new Date().toISOString()
          }
        })

      case 'report':
        // Report an issue with a resource
        const { issue, description } = await request.json()
        
        return NextResponse.json({
          success: true,
          data: {
            message: 'Issue reported successfully',
            resourceId,
            issue,
            description,
            reportedAt: new Date().toISOString()
          }
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Resources API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
