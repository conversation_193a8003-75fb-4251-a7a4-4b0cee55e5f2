{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/games/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { PlayI<PERSON>, ClockIcon, StarIcon, UserGroupIcon } from '@heroicons/react/24/outline'\nimport { GAME_CATEGORIES, AGE_GROUPS } from '@/lib/constants'\n\ninterface Game {\n  id: string\n  title: string\n  description: string\n  category: keyof typeof GAME_CATEGORIES\n  ageGroup: keyof typeof AGE_GROUPS | 'all'\n  duration: number\n  difficulty: 'easy' | 'medium' | 'hard'\n  thumbnail: string\n  rating: number\n  players: number\n  benefits: string[]\n}\n\nconst games: Game[] = [\n  {\n    id: '1',\n    title: 'Breathing Garden',\n    description: 'A peaceful breathing exercise game where you help flowers bloom by following breathing patterns.',\n    category: 'stress_relief',\n    ageGroup: 'all',\n    duration: 5,\n    difficulty: 'easy',\n    thumbnail: '🌸',\n    rating: 4.8,\n    players: 1,\n    benefits: ['Reduces anxiety', 'Improves focus', 'Calms mind']\n  },\n  {\n    id: '2',\n    title: 'Emotion Detective',\n    description: 'Learn to identify and understand different emotions through interactive scenarios.',\n    category: 'emotional_regulation',\n    ageGroup: 'child',\n    duration: 15,\n    difficulty: 'easy',\n    thumbnail: '🕵️‍♀️',\n    rating: 4.6,\n    players: 1,\n    benefits: ['Emotional awareness', 'Empathy building', 'Social skills']\n  },\n  {\n    id: '3',\n    title: 'Mindful Maze',\n    description: 'Navigate through calming mazes while practicing mindfulness techniques.',\n    category: 'mindfulness',\n    ageGroup: 'teen',\n    duration: 10,\n    difficulty: 'medium',\n    thumbnail: '🧩',\n    rating: 4.7,\n    players: 1,\n    benefits: ['Mindfulness', 'Problem solving', 'Stress relief']\n  },\n  {\n    id: '4',\n    title: 'Coping Castle',\n    description: 'Build a castle while learning different coping strategies for difficult situations.',\n    category: 'coping_skills',\n    ageGroup: 'all',\n    duration: 20,\n    difficulty: 'medium',\n    thumbnail: '🏰',\n    rating: 4.9,\n    players: 1,\n    benefits: ['Coping strategies', 'Resilience', 'Problem solving']\n  },\n  {\n    id: '5',\n    title: 'Worry Warriors',\n    description: 'Transform worries into positive thoughts in this empowering adventure game.',\n    category: 'emotional_regulation',\n    ageGroup: 'teen',\n    duration: 25,\n    difficulty: 'hard',\n    thumbnail: '⚔️',\n    rating: 4.5,\n    players: 1,\n    benefits: ['Anxiety management', 'Positive thinking', 'Self-confidence']\n  },\n  {\n    id: '6',\n    title: 'Gratitude Garden',\n    description: 'Plant and grow a beautiful garden by expressing gratitude and positive thoughts.',\n    category: 'mindfulness',\n    ageGroup: 'all',\n    duration: 12,\n    difficulty: 'easy',\n    thumbnail: '🌻',\n    rating: 4.8,\n    players: 1,\n    benefits: ['Gratitude practice', 'Positive mindset', 'Emotional wellbeing']\n  }\n]\n\nexport default function GamesPage() {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [selectedAgeGroup, setSelectedAgeGroup] = useState<string>('all')\n\n  const filteredGames = games.filter(game => {\n    const categoryMatch = selectedCategory === 'all' || game.category === selectedCategory\n    const ageMatch = selectedAgeGroup === 'all' || game.ageGroup === selectedAgeGroup || game.ageGroup === 'all'\n    return categoryMatch && ageMatch\n  })\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy': return 'text-green-600 bg-green-100'\n      case 'medium': return 'text-yellow-600 bg-yellow-100'\n      case 'hard': return 'text-red-600 bg-red-100'\n      default: return 'text-gray-600 bg-gray-100'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Therapeutic Games\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Fun, interactive games designed to help manage stress, build coping skills, \n            and promote emotional wellbeing for women and children.\n          </p>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category\n              </label>\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              >\n                <option value=\"all\">All Categories</option>\n                {Object.entries(GAME_CATEGORIES).map(([key, label]) => (\n                  <option key={key} value={key}>{label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Age Group\n              </label>\n              <select\n                value={selectedAgeGroup}\n                onChange={(e) => setSelectedAgeGroup(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              >\n                <option value=\"all\">All Ages</option>\n                {Object.entries(AGE_GROUPS).map(([key, group]) => (\n                  <option key={key} value={key}>{group.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Games Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredGames.map((game) => (\n            <div key={game.id} className=\"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"text-4xl\">{game.thumbnail}</div>\n                  <div className=\"flex items-center space-x-1\">\n                    <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                    <span className=\"text-sm text-gray-600\">{game.rating}</span>\n                  </div>\n                </div>\n                \n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  {game.title}\n                </h3>\n                \n                <p className=\"text-gray-600 mb-4 text-sm\">\n                  {game.description}\n                </p>\n\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(game.difficulty)}`}>\n                    {game.difficulty}\n                  </span>\n                  <span className=\"px-2 py-1 rounded-full text-xs font-medium text-purple-600 bg-purple-100\">\n                    {GAME_CATEGORIES[game.category]}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                  <div className=\"flex items-center\">\n                    <ClockIcon className=\"h-4 w-4 mr-1\" />\n                    {game.duration} min\n                  </div>\n                  <div className=\"flex items-center\">\n                    <UserGroupIcon className=\"h-4 w-4 mr-1\" />\n                    {game.players} player\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Benefits:</h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {game.benefits.map((benefit, index) => (\n                      <span key={index} className=\"px-2 py-1 bg-green-100 text-green-700 text-xs rounded\">\n                        {benefit}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => {\n                    if (game.id === '1') {\n                      window.location.href = '/games/breathing-garden'\n                    } else {\n                      alert('This game is coming soon! Try the Breathing Garden for now.')\n                    }\n                  }}\n                  className=\"w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors flex items-center justify-center\"\n                >\n                  <PlayIcon className=\"h-4 w-4 mr-2\" />\n                  {game.id === '1' ? 'Play Now' : 'Coming Soon'}\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {filteredGames.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 text-lg\">No games found for the selected filters.</p>\n          </div>\n        )}\n\n        {/* Info Section */}\n        <div className=\"mt-16 bg-white rounded-lg shadow-md p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">How Therapeutic Games Help</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🧠</div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Cognitive Skills</h3>\n              <p className=\"text-sm text-gray-600\">Improve problem-solving, memory, and focus through engaging gameplay.</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">💝</div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Emotional Regulation</h3>\n              <p className=\"text-sm text-gray-600\">Learn to identify, understand, and manage emotions effectively.</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🌱</div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Stress Relief</h3>\n              <p className=\"text-sm text-gray-600\">Reduce anxiety and stress through calming, mindful activities.</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">💪</div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">Coping Skills</h3>\n              <p className=\"text-sm text-gray-600\">Build resilience and learn healthy ways to handle challenges.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAoBA,MAAM,QAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAmB;YAAkB;SAAa;IAC/D;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAuB;YAAoB;SAAgB;IACxE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAe;YAAmB;SAAgB;IAC/D;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAqB;YAAc;SAAkB;IAClE;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAsB;YAAqB;SAAkB;IAC1E;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,SAAS;QACT,UAAU;YAAC;YAAsB;YAAoB;SAAsB;IAC7E;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,qBAAqB,SAAS,KAAK,QAAQ,KAAK;QACtE,MAAM,WAAW,qBAAqB,SAAS,KAAK,QAAQ,KAAK,oBAAoB,KAAK,QAAQ,KAAK;QACvG,OAAO,iBAAiB;IAC1B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,OAAO,OAAO,CAAC,uHAAA,CAAA,kBAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChD,8OAAC;oDAAiB,OAAO;8DAAM;mDAAlB;;;;;;;;;;;;;;;;;0CAInB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,OAAO,OAAO,CAAC,uHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC3C,8OAAC;oDAAiB,OAAO;8DAAM,MAAM,KAAK;mDAA7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;4BAAkB,WAAU;sCAC3B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAY,KAAK,SAAS;;;;;;0DACzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAyB,KAAK,MAAM;;;;;;;;;;;;;;;;;;kDAIxD,8OAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAGb,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,KAAK,UAAU,GAAG;0DACjG,KAAK,UAAU;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DACb,uHAAA,CAAA,kBAAe,CAAC,KAAK,QAAQ,CAAC;;;;;;;;;;;;kDAInC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,KAAK,QAAQ;oDAAC;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB,KAAK,OAAO;oDAAC;;;;;;;;;;;;;kDAIlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;;;;;;;;;;;;kDAOjB,8OAAC;wCACC,SAAS;4CACP,IAAI,KAAK,EAAE,KAAK,KAAK;gDACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACzB,OAAO;gDACL,MAAM;4CACR;wCACF;wCACA,WAAU;;0DAEV,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,KAAK,EAAE,KAAK,MAAM,aAAa;;;;;;;;;;;;;2BA5D5B,KAAK,EAAE;;;;;;;;;;gBAmEpB,cAAc,MAAM,KAAK,mBACxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/PlayIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlayIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/StarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction StarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(StarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/UserGroupIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction UserGroupIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserGroupIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,EACrB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}