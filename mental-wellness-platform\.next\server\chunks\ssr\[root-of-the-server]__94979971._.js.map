{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { HeartIcon, ChatBubbleLeftRightIcon, PuzzlePieceIcon, BookOpenIcon, PhoneIcon } from '@heroicons/react/24/outline'\nimport { APP_CONFIG } from '@/lib/constants'\n\nconst features = [\n  {\n    name: 'AI-Powered Chat Support',\n    description: 'Get instant emotional support and guidance from our AI counselor, available 24/7.',\n    icon: ChatBubbleLeftRightIcon,\n    href: '/chat',\n    color: 'bg-blue-500'\n  },\n  {\n    name: 'Therapeutic Games',\n    description: 'Fun, interactive games designed to help manage stress and build coping skills.',\n    icon: PuzzlePieceIcon,\n    href: '/games',\n    color: 'bg-green-500'\n  },\n  {\n    name: 'Educational Content',\n    description: 'Videos, stories, and resources to learn about emotional intelligence and safety.',\n    icon: BookOpenIcon,\n    href: '/content',\n    color: 'bg-purple-500'\n  },\n  {\n    name: 'Crisis Resources',\n    description: 'Quick access to helplines, emergency services, and local support organizations.',\n    icon: PhoneIcon,\n    href: '/resources',\n    color: 'bg-red-500'\n  },\n]\n\nexport default function Home() {\n  return (\n    <div className=\"bg-white\">\n      {/* Hero section */}\n      <div className=\"relative isolate px-6 pt-14 lg:px-8\">\n        <div className=\"mx-auto max-w-2xl py-32 sm:py-48 lg:py-56\">\n          <div className=\"text-center\">\n            <div className=\"flex justify-center mb-8\">\n              <HeartIcon className=\"h-16 w-16 text-pink-500\" />\n            </div>\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\">\n              Mental Wellness Support for{' '}\n              <span className=\"text-pink-600\">Women & Children</span>\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n              AI-powered emotional support, therapeutic games, safety monitoring, and educational resources\n              designed specifically for women and children's mental wellness needs.\n            </p>\n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <Link\n                href=\"/chat\"\n                className=\"rounded-md bg-pink-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-pink-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600\"\n              >\n                Start Chat Support\n              </Link>\n              <Link href=\"/resources\" className=\"text-sm font-semibold leading-6 text-gray-900\">\n                Emergency Resources <span aria-hidden=\"true\">→</span>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Features section */}\n      <div className=\"py-24 sm:py-32\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl lg:text-center\">\n            <h2 className=\"text-base font-semibold leading-7 text-pink-600\">Comprehensive Support</h2>\n            <p className=\"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need for mental wellness\n            </p>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n              Our platform combines AI technology with human expertise to provide safe, accessible,\n              and effective mental health support tailored for women and children.\n            </p>\n          </div>\n          <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n            <dl className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2\">\n              {features.map((feature) => (\n                <div key={feature.name} className=\"flex flex-col\">\n                  <dt className=\"flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900\">\n                    <div className={`h-10 w-10 flex items-center justify-center rounded-lg ${feature.color}`}>\n                      <feature.icon className=\"h-6 w-6 text-white\" aria-hidden=\"true\" />\n                    </div>\n                    {feature.name}\n                  </dt>\n                  <dd className=\"mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600\">\n                    <p className=\"flex-auto\">{feature.description}</p>\n                    <p className=\"mt-6\">\n                      <Link\n                        href={feature.href}\n                        className=\"text-sm font-semibold leading-6 text-pink-600 hover:text-pink-500\"\n                      >\n                        Learn more <span aria-hidden=\"true\">→</span>\n                      </Link>\n                    </p>\n                  </dd>\n                </div>\n              ))}\n            </dl>\n          </div>\n        </div>\n      </div>\n\n      {/* CTA section */}\n      <div className=\"bg-pink-50\">\n        <div className=\"px-6 py-24 sm:px-6 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Need immediate help?\n            </h2>\n            <p className=\"mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-600\">\n              If you're in crisis or need immediate support, don't hesitate to reach out.\n              Help is available 24/7.\n            </p>\n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <Link\n                href=\"/resources\"\n                className=\"rounded-md bg-red-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600\"\n              >\n                Emergency Resources\n              </Link>\n              <a href=\"tel:988\" className=\"text-sm font-semibold leading-6 text-gray-900\">\n                Call 988 <span aria-hidden=\"true\">→</span>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAGA,MAAM,WAAW;IACf;QACE,MAAM;QACN,aAAa;QACb,MAAM,6OAAA,CAAA,0BAAuB;QAC7B,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,6NAAA,CAAA,kBAAe;QACrB,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,uNAAA,CAAA,eAAY;QAClB,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,iNAAA,CAAA,YAAS;QACf,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAG,WAAU;;oCAA8D;oCAC9C;kDAC5B,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;4CAAgD;0DAC5D,8OAAC;gDAAK,eAAY;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAChE,8OAAC;oCAAE,WAAU;8CAAmE;;;;;;8CAGhF,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAAuB,WAAU;;0DAChC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAW,CAAC,sDAAsD,EAAE,QAAQ,KAAK,EAAE;kEACtF,cAAA,8OAAC,QAAQ,IAAI;4DAAC,WAAU;4DAAqB,eAAY;;;;;;;;;;;oDAE1D,QAAQ,IAAI;;;;;;;0DAEf,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAE,WAAU;kEAAa,QAAQ,WAAW;;;;;;kEAC7C,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,QAAQ,IAAI;4DAClB,WAAU;;gEACX;8EACY,8OAAC;oEAAK,eAAY;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;uCAdlC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0BhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;0CAAwD;;;;;;0CAIrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCAAE,MAAK;wCAAU,WAAU;;4CAAgD;0DACjE,8OAAC;gDAAK,eAAY;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}]}