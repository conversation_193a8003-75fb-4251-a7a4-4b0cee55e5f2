var v8n=function(){"use strict";var n=function(n,t,r,e){this.name=n,this.fn=t,this.args=r,this.modifiers=e};function t(n,t){return void 0===t&&(t="simple"),"object"==typeof n?n[t]:n}function r(n,e,u){if(n.length){var i=n.shift(),o=r(n,e,u);return i.perform(o,u)}return t(e)}function e(n,r,u){if(n.length){var i=n.shift(),o=e(n,r,u);return i.performAsync(o,u)}return function(n){return Promise.resolve(t(r,"async")(n))}}n.prototype._test=function(n){var t=this.fn;try{r(this.modifiers.slice(),t,this)(n)}catch(n){t=function(){return!1}}try{return r(this.modifiers.slice(),t,this)(n)}catch(n){return!1}},n.prototype._check=function(n){try{r(this.modifiers.slice(),this.fn,this)(n)}catch(n){if(r(this.modifiers.slice(),(function(n){return n}),this)(!1))return}if(!r(this.modifiers.slice(),this.fn,this)(n))throw null},n.prototype._testAsync=function(n){var t=this;return new Promise((function(r,u){e(t.modifiers.slice(),t.fn,t)(n).then((function(t){t?r(n):u(null)})).catch((function(n){return u(n)}))}))};var u=function(n,t,r){this.name=n,this.perform=t,this.performAsync=r},i=function(n){function t(r,e,u,i){for(var o=[],c=arguments.length-4;c-- >0;)o[c]=arguments[c+4];n.call(this,o),n.captureStackTrace&&n.captureStackTrace(this,t),this.rule=r,this.value=e,this.cause=u,this.target=i}return n&&(t.__proto__=n),t.prototype=Object.create(n&&n.prototype),t.prototype.constructor=t,t}(Error),o=function(n,t){void 0===n&&(n=[]),void 0===t&&(t=[]),this.chain=n,this.nextRuleModifiers=t};function c(n,t,r,e){if(t.length){var u=t.shift();u._testAsync(n).then((function(){c(n,t,r,e)}),(function(t){e(new i(u,n,t))}))}else r(n)}o.prototype._applyRule=function(t,r){var e=this;return function(){for(var u=[],i=arguments.length;i--;)u[i]=arguments[i];return e.chain.push(new n(r,t.apply(e,u),u,e.nextRuleModifiers)),e.nextRuleModifiers=[],e}},o.prototype._applyModifier=function(n,t){return this.nextRuleModifiers.push(new u(t,n.simple,n.async)),this},o.prototype._clone=function(){return new o(this.chain.slice(),this.nextRuleModifiers.slice())},o.prototype.test=function(n){return this.chain.every((function(t){return t._test(n)}))},o.prototype.testAll=function(n){var t=[];return this.chain.forEach((function(r){try{r._check(n)}catch(e){t.push(new i(r,n,e))}})),t},o.prototype.check=function(n){this.chain.forEach((function(t){try{t._check(n)}catch(r){throw new i(t,n,r)}}))},o.prototype.testAsync=function(n){var t=this;return new Promise((function(r,e){c(n,t.chain.slice(),r,e)}))};var f=function(n,t){return!(!t||"string"!=typeof n||0!==n.trim().length)||null==n};function s(){return"undefined"!=typeof Proxy?h(new o):l(new o)}var a={};function h(n){return new Proxy(n,{get:function(t,r){if(r in t)return t[r];var e=h(n._clone());return r in p?e._applyModifier(p[r],r):r in a?e._applyRule(a[r],r):r in v?e._applyRule(v[r],r):void 0}})}function l(n){var t=function(n,t){return Object.keys(n).forEach((function(r){t[r]=function(){for(var e=[],u=arguments.length;u--;)e[u]=arguments[u];var i=l(t._clone()),o=i._applyRule(n[r],r).apply(void 0,e);return o}})),t},r=t(v,n),e=t(a,r);return Object.keys(p).forEach((function(n){Object.defineProperty(e,n,{get:function(){return l(e._clone())._applyModifier(p[n],n)}})})),e}s.extend=function(n){Object.assign(a,n)},s.clearCustomRules=function(){a={}};var p={not:{simple:function(n){return function(t){return!n(t)}},async:function(n){return function(t){return Promise.resolve(n(t)).then((function(n){return!n})).catch((function(){return!0}))}}},some:{simple:function(n){return function(t){return m(t).some((function(t){try{return n(t)}catch(n){return!1}}))}},async:function(n){return function(t){return Promise.all(m(t).map((function(t){try{return n(t).catch((function(){return!1}))}catch(n){return!1}}))).then((function(n){return n.some(Boolean)}))}}},every:{simple:function(n){return function(t){return!1!==t&&m(t).every(n)}},async:function(n){return function(t){return Promise.all(m(t).map(n)).then((function(n){return n.every(Boolean)}))}}},strict:{simple:function(n,t){return function(r){return y(t)&&r&&"object"==typeof r?Object.keys(t.args[0]).length===Object.keys(r).length&&n(r):n(r)}},async:function(n,t){return function(r){return Promise.resolve(n(r)).then((function(n){return y(t)&&r&&"object"==typeof r?Object.keys(t.args[0]).length===Object.keys(r).length&&n:n})).catch((function(){return!1}))}}}};function y(n){return n&&"schema"===n.name&&n.args.length>0&&"object"==typeof n.args[0]}function m(n){return"string"==typeof n?n.split(""):n}var v={equal:function(n){return function(t){return t==n}},exact:function(n){return function(t){return t===n}},number:function(n){return void 0===n&&(n=!0),function(t){return"number"==typeof t&&(n||isFinite(t))}},integer:function(){return function(n){return(Number.isInteger||d)(n)}},numeric:function(){return function(n){return!isNaN(parseFloat(n))&&isFinite(n)}},string:function(){return g("string")},boolean:function(){return g("boolean")},undefined:function(){return g("undefined")},null:function(){return g("null")},array:function(){return g("array")},object:function(){return g("object")},instanceOf:function(n){return function(t){return t instanceof n}},pattern:function(n){return function(t){return n.test(t)}},lowercase:function(){return function(n){return"boolean"==typeof n||n===n.toLowerCase()&&""!==n.trim()}},uppercase:function(){return function(n){return n===n.toUpperCase()&&""!==n.trim()}},vowel:function(){return function(n){return/^[aeiou]+$/i.test(n)}},consonant:function(){return function(n){return/^(?=[^aeiou])([a-z]+)$/i.test(n)}},first:function(n){return function(t){return t[0]==n}},last:function(n){return function(t){return t[t.length-1]==n}},empty:function(){return function(n){return 0===n.length}},length:function(n,t){return function(r){return r.length>=n&&r.length<=(t||n)}},minLength:function(n){return function(t){return t.length>=n}},maxLength:function(n){return function(t){return t.length<=n}},negative:function(){return function(n){return n<0}},positive:function(){return function(n){return n>=0}},between:function(n,t){return function(r){return r>=n&&r<=t}},range:function(n,t){return function(r){return r>=n&&r<=t}},lessThan:function(n){return function(t){return t<n}},lessThanOrEqual:function(n){return function(t){return t<=n}},greaterThan:function(n){return function(t){return t>n}},greaterThanOrEqual:function(n){return function(t){return t>=n}},even:function(){return function(n){return n%2==0}},odd:function(){return function(n){return n%2!=0}},includes:function(n){return function(t){return~t.indexOf(n)}},schema:function(n){return function(n){return{simple:function(t){var r=[];if(Object.keys(n).forEach((function(e){var u=n[e];try{u.check((t||{})[e])}catch(n){n.target=e,r.push(n)}})),r.length>0)throw r;return!0},async:function(t){var r=[],e=Object.keys(n).map((function(e){return n[e].testAsync((t||{})[e]).catch((function(n){n.target=e,r.push(n)}))}));return Promise.all(e).then((function(){if(r.length>0)throw r;return!0}))}}}(n)},passesAnyOf:function(){for(var n=[],t=arguments.length;t--;)n[t]=arguments[t];return function(t){return n.some((function(n){return n.test(t)}))}},optional:function(n,t){return void 0===t&&(t=!1),{simple:function(r){return f(r,t)||void 0===n.check(r)},async:function(r){return f(r,t)||n.testAsync(r)}}}};function g(n){return function(t){return Array.isArray(t)&&"array"===n||null===t&&"null"===n||typeof t===n}}function d(n){return"number"==typeof n&&isFinite(n)&&Math.floor(n)===n}return s}();
//# sourceMappingURL=v8n.browser.min.js.map
