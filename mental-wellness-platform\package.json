{"name": "mental-wellness-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.1", "openai": "^5.10.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-speech-kit": "^3.0.1", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}