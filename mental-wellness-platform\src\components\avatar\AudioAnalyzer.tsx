'use client'

import { useRef, useEffect, useState, useCallback } from 'react'

interface AudioAnalyzerProps {
  isActive: boolean
  onAudioData: (data: {
    volume: number
    frequency: number
    formants: number[]
    pitch: number
    voiceActivity: boolean
  }) => void
}

export default function AudioAnalyzer({ isActive, onAudioData }: AudioAnalyzerProps) {
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const animationFrameRef = useRef<number>()
  
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize audio context and microphone
  const initializeAudio = useCallback(async () => {
    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }

      if (!analyserRef.current) {
        analyserRef.current = audioContextRef.current.createAnalyser()
        analyserRef.current.fftSize = 2048
        analyserRef.current.smoothingTimeConstant = 0.8
      }

      // Get microphone access
      if (!streamRef.current) {
        streamRef.current = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100
          } 
        })
        
        microphoneRef.current = audioContextRef.current.createMediaStreamSource(streamRef.current)
        microphoneRef.current.connect(analyserRef.current)
      }

      setIsInitialized(true)
    } catch (error) {
      console.error('Error initializing audio:', error)
    }
  }, [])

  // Advanced audio analysis
  const analyzeAudio = useCallback(() => {
    if (!analyserRef.current || !isActive) return

    const bufferLength = analyserRef.current.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    const frequencyData = new Float32Array(bufferLength)
    
    analyserRef.current.getByteFrequencyData(dataArray)
    analyserRef.current.getFloatFrequencyData(frequencyData)

    // Calculate volume (RMS)
    let sum = 0
    for (let i = 0; i < bufferLength; i++) {
      sum += dataArray[i] * dataArray[i]
    }
    const volume = Math.sqrt(sum / bufferLength) / 255

    // Find dominant frequency
    let maxIndex = 0
    let maxValue = 0
    for (let i = 0; i < bufferLength; i++) {
      if (dataArray[i] > maxValue) {
        maxValue = dataArray[i]
        maxIndex = i
      }
    }
    const frequency = (maxIndex * audioContextRef.current!.sampleRate) / (2 * bufferLength)

    // Estimate formants (simplified)
    const formants = []
    const formantRanges = [
      [200, 1000],   // F1
      [800, 2500],   // F2
      [1500, 4000]   // F3
    ]

    for (const [low, high] of formantRanges) {
      const startBin = Math.floor((low * bufferLength * 2) / audioContextRef.current!.sampleRate)
      const endBin = Math.floor((high * bufferLength * 2) / audioContextRef.current!.sampleRate)
      
      let maxFormantValue = 0
      let maxFormantIndex = startBin
      
      for (let i = startBin; i < Math.min(endBin, bufferLength); i++) {
        if (dataArray[i] > maxFormantValue) {
          maxFormantValue = dataArray[i]
          maxFormantIndex = i
        }
      }
      
      const formantFreq = (maxFormantIndex * audioContextRef.current!.sampleRate) / (2 * bufferLength)
      formants.push(formantFreq)
    }

    // Simple pitch detection using autocorrelation
    const pitch = estimatePitch(dataArray, audioContextRef.current!.sampleRate)

    // Voice activity detection
    const voiceActivity = volume > 0.01 && frequency > 80 && frequency < 1000

    onAudioData({
      volume,
      frequency,
      formants,
      pitch,
      voiceActivity
    })

    if (isActive) {
      animationFrameRef.current = requestAnimationFrame(analyzeAudio)
    }
  }, [isActive, onAudioData])

  // Simple pitch estimation using autocorrelation
  const estimatePitch = (buffer: Uint8Array, sampleRate: number): number => {
    const SIZE = buffer.length
    const MAX_SAMPLES = Math.floor(SIZE / 2)
    let bestOffset = -1
    let bestCorrelation = 0
    let rms = 0
    let foundGoodCorrelation = false
    const correlations = new Array(MAX_SAMPLES)

    for (let i = 0; i < SIZE; i++) {
      const val = buffer[i] - 128
      rms += val * val
    }
    rms = Math.sqrt(rms / SIZE)
    
    if (rms < 0.01) return -1

    let lastCorrelation = 1
    for (let offset = 1; offset < MAX_SAMPLES; offset++) {
      let correlation = 0
      for (let i = 0; i < MAX_SAMPLES; i++) {
        correlation += Math.abs((buffer[i] - 128) - (buffer[i + offset] - 128))
      }
      correlation = 1 - (correlation / MAX_SAMPLES)
      correlations[offset] = correlation
      
      if (correlation > 0.9 && correlation > lastCorrelation) {
        foundGoodCorrelation = true
        if (correlation > bestCorrelation) {
          bestCorrelation = correlation
          bestOffset = offset
        }
      } else if (foundGoodCorrelation) {
        const shift = (correlations[bestOffset + 1] - correlations[bestOffset - 1]) / correlations[bestOffset]
        return sampleRate / (bestOffset + (8 * shift))
      }
      lastCorrelation = correlation
    }
    
    if (bestCorrelation > 0.01) {
      return sampleRate / bestOffset
    }
    return -1
  }

  // Start/stop audio analysis
  useEffect(() => {
    if (isActive && !isInitialized) {
      initializeAudio()
    }
    
    if (isActive && isInitialized) {
      analyzeAudio()
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isActive, isInitialized, initializeAudio, analyzeAudio])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  return null // This is a logic-only component
}

// Utility function to map audio features to phonemes
export function audioToPhoneme(audioData: {
  volume: number
  frequency: number
  formants: number[]
  pitch: number
  voiceActivity: boolean
}): string {
  if (!audioData.voiceActivity || audioData.volume < 0.01) {
    return 'silence'
  }

  const [f1, f2, f3] = audioData.formants

  // Simplified vowel classification based on formants
  if (f1 < 400 && f2 > 2000) return 'I'
  if (f1 < 500 && f2 < 1500) return 'U'
  if (f1 > 700 && f2 > 1500) return 'A'
  if (f1 > 500 && f2 > 1800) return 'E'
  if (f1 > 500 && f2 < 1200) return 'O'

  // Consonant classification based on frequency and volume patterns
  if (audioData.frequency > 2000 && audioData.volume > 0.3) return 'S'
  if (audioData.frequency < 200 && audioData.volume > 0.5) return 'M'
  if (audioData.frequency > 1000 && audioData.volume > 0.4) return 'F'

  return 'silence'
}

// Real-time lip-sync data generator
export function generateLipSyncFromAudio(audioData: {
  volume: number
  frequency: number
  formants: number[]
  pitch: number
  voiceActivity: boolean
}) {
  const phoneme = audioToPhoneme(audioData)
  const baseShape = {
    mouthOpenness: 0.1,
    mouthWidth: 0.5,
    phoneme: 'silence'
  }

  if (!audioData.voiceActivity) {
    return baseShape
  }

  // Map volume to mouth openness
  const volumeInfluence = Math.min(audioData.volume * 2, 1)
  
  // Map formants to mouth shape
  const [f1, f2] = audioData.formants
  const mouthOpenness = Math.min(0.9, 0.2 + (f1 / 1000) * 0.6 + volumeInfluence * 0.3)
  const mouthWidth = Math.min(0.9, 0.3 + (f2 / 2500) * 0.6)

  return {
    mouthOpenness,
    mouthWidth,
    phoneme
  }
}
