{"version": 3, "file": "MMDPhysics.js", "sources": ["../../src/animation/MMDPhysics.js"], "sourcesContent": ["import {\n  Bone,\n  BoxGeometry,\n  Color,\n  Euler,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  Quaternion,\n  SphereGeometry,\n  Vector3,\n} from 'three'\nimport { CapsuleGeometry } from '../_polyfill/CapsuleGeometry'\n\n/**\n * Dependencies\n *  - Ammo.js https://github.com/kripken/ammo.js\n *\n * MMDPhysics calculates physics with Ammo(Bullet based JavaScript Physics engine)\n * for MMD model loaded by MMDLoader.\n *\n * TODO\n *  - Physics in Worker\n */\n\n/* global Ammo */\n\nclass MMDPhysics {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Array<Object>} rigidBodyParams\n   * @param {Array<Object>} (optional) constraintParams\n   * @param {Object} params - (optional)\n   * @param {Number} params.unitStep - Default is 1 / 65.\n   * @param {Integer} params.maxStepNum - Default is 3.\n   * @param {Vector3} params.gravity - Default is ( 0, - 9.8 * 10, 0 )\n   */\n  constructor(mesh, rigidBodyParams, constraintParams = [], params = {}) {\n    if (typeof Ammo === 'undefined') {\n      throw new Error('THREE.MMDPhysics: Import ammo.js https://github.com/kripken/ammo.js')\n    }\n\n    this.manager = new ResourceManager()\n\n    this.mesh = mesh\n\n    /*\n     * I don't know why but 1/60 unitStep easily breaks models\n     * so I set it 1/65 so far.\n     * Don't set too small unitStep because\n     * the smaller unitStep can make the performance worse.\n     */\n    this.unitStep = params.unitStep !== undefined ? params.unitStep : 1 / 65\n    this.maxStepNum = params.maxStepNum !== undefined ? params.maxStepNum : 3\n    this.gravity = new Vector3(0, -9.8 * 10, 0)\n\n    if (params.gravity !== undefined) this.gravity.copy(params.gravity)\n\n    this.world = params.world !== undefined ? params.world : null // experimental\n\n    this.bodies = []\n    this.constraints = []\n\n    this._init(mesh, rigidBodyParams, constraintParams)\n  }\n\n  /**\n   * Advances Physics calculation and updates bones.\n   *\n   * @param {Number} delta - time in second\n   * @return {MMDPhysics}\n   */\n  update(delta) {\n    const manager = this.manager\n    const mesh = this.mesh\n\n    // rigid bodies and constrains are for\n    // mesh's world scale (1, 1, 1).\n    // Convert to (1, 1, 1) if it isn't.\n\n    let isNonDefaultScale = false\n\n    const position = manager.allocThreeVector3()\n    const quaternion = manager.allocThreeQuaternion()\n    const scale = manager.allocThreeVector3()\n\n    mesh.matrixWorld.decompose(position, quaternion, scale)\n\n    if (scale.x !== 1 || scale.y !== 1 || scale.z !== 1) {\n      isNonDefaultScale = true\n    }\n\n    let parent\n\n    if (isNonDefaultScale) {\n      parent = mesh.parent\n\n      if (parent !== null) mesh.parent = null\n\n      scale.copy(this.mesh.scale)\n\n      mesh.scale.set(1, 1, 1)\n      mesh.updateMatrixWorld(true)\n    }\n\n    // calculate physics and update bones\n\n    this._updateRigidBodies()\n    this._stepSimulation(delta)\n    this._updateBones()\n\n    // restore mesh if converted above\n\n    if (isNonDefaultScale) {\n      if (parent !== null) mesh.parent = parent\n\n      mesh.scale.copy(scale)\n    }\n\n    manager.freeThreeVector3(scale)\n    manager.freeThreeQuaternion(quaternion)\n    manager.freeThreeVector3(position)\n\n    return this\n  }\n\n  /**\n   * Resets rigid bodies transorm to current bone's.\n   *\n   * @return {MMDPhysics}\n   */\n  reset() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].reset()\n    }\n\n    return this\n  }\n\n  /**\n   * Warm ups Rigid bodies. Calculates cycles steps.\n   *\n   * @param {Integer} cycles\n   * @return {MMDPhysics}\n   */\n  warmup(cycles) {\n    for (let i = 0; i < cycles; i++) {\n      this.update(1 / 60)\n    }\n\n    return this\n  }\n\n  /**\n   * Sets gravity.\n   *\n   * @param {Vector3} gravity\n   * @return {MMDPhysicsHelper}\n   */\n  setGravity(gravity) {\n    this.world.setGravity(new Ammo.btVector3(gravity.x, gravity.y, gravity.z))\n    this.gravity.copy(gravity)\n\n    return this\n  }\n\n  /**\n   * Creates MMDPhysicsHelper\n   *\n   * @return {MMDPhysicsHelper}\n   */\n  createHelper() {\n    return new MMDPhysicsHelper(this.mesh, this)\n  }\n\n  // private methods\n\n  _init(mesh, rigidBodyParams, constraintParams) {\n    const manager = this.manager\n\n    // rigid body/constraint parameters are for\n    // mesh's default world transform as position(0, 0, 0),\n    // quaternion(0, 0, 0, 1) and scale(0, 0, 0)\n\n    const parent = mesh.parent\n\n    if (parent !== null) mesh.parent = null\n\n    const currentPosition = manager.allocThreeVector3()\n    const currentQuaternion = manager.allocThreeQuaternion()\n    const currentScale = manager.allocThreeVector3()\n\n    currentPosition.copy(mesh.position)\n    currentQuaternion.copy(mesh.quaternion)\n    currentScale.copy(mesh.scale)\n\n    mesh.position.set(0, 0, 0)\n    mesh.quaternion.set(0, 0, 0, 1)\n    mesh.scale.set(1, 1, 1)\n\n    mesh.updateMatrixWorld(true)\n\n    if (this.world === null) {\n      this.world = this._createWorld()\n      this.setGravity(this.gravity)\n    }\n\n    this._initRigidBodies(rigidBodyParams)\n    this._initConstraints(constraintParams)\n\n    if (parent !== null) mesh.parent = parent\n\n    mesh.position.copy(currentPosition)\n    mesh.quaternion.copy(currentQuaternion)\n    mesh.scale.copy(currentScale)\n\n    mesh.updateMatrixWorld(true)\n\n    this.reset()\n\n    manager.freeThreeVector3(currentPosition)\n    manager.freeThreeQuaternion(currentQuaternion)\n    manager.freeThreeVector3(currentScale)\n  }\n\n  _createWorld() {\n    const config = new Ammo.btDefaultCollisionConfiguration()\n    const dispatcher = new Ammo.btCollisionDispatcher(config)\n    const cache = new Ammo.btDbvtBroadphase()\n    const solver = new Ammo.btSequentialImpulseConstraintSolver()\n    const world = new Ammo.btDiscreteDynamicsWorld(dispatcher, cache, solver, config)\n    return world\n  }\n\n  _initRigidBodies(rigidBodies) {\n    for (let i = 0, il = rigidBodies.length; i < il; i++) {\n      this.bodies.push(new RigidBody(this.mesh, this.world, rigidBodies[i], this.manager))\n    }\n  }\n\n  _initConstraints(constraints) {\n    for (let i = 0, il = constraints.length; i < il; i++) {\n      const params = constraints[i]\n      const bodyA = this.bodies[params.rigidBodyIndex1]\n      const bodyB = this.bodies[params.rigidBodyIndex2]\n      this.constraints.push(new Constraint(this.mesh, this.world, bodyA, bodyB, params, this.manager))\n    }\n  }\n\n  _stepSimulation(delta) {\n    const unitStep = this.unitStep\n    let stepTime = delta\n    let maxStepNum = ((delta / unitStep) | 0) + 1\n\n    if (stepTime < unitStep) {\n      stepTime = unitStep\n      maxStepNum = 1\n    }\n\n    if (maxStepNum > this.maxStepNum) {\n      maxStepNum = this.maxStepNum\n    }\n\n    this.world.stepSimulation(stepTime, maxStepNum, unitStep)\n  }\n\n  _updateRigidBodies() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateFromBone()\n    }\n  }\n\n  _updateBones() {\n    for (let i = 0, il = this.bodies.length; i < il; i++) {\n      this.bodies[i].updateBone()\n    }\n  }\n}\n\n/**\n * This manager's responsibilies are\n *\n * 1. manage Ammo.js and Three.js object resources and\n *    improve the performance and the memory consumption by\n *    reusing objects.\n *\n * 2. provide simple Ammo object operations.\n */\nclass ResourceManager {\n  constructor() {\n    // for Three.js\n    this.threeVector3s = []\n    this.threeMatrix4s = []\n    this.threeQuaternions = []\n    this.threeEulers = []\n\n    // for Ammo.js\n    this.transforms = []\n    this.quaternions = []\n    this.vector3s = []\n  }\n\n  allocThreeVector3() {\n    return this.threeVector3s.length > 0 ? this.threeVector3s.pop() : new Vector3()\n  }\n\n  freeThreeVector3(v) {\n    this.threeVector3s.push(v)\n  }\n\n  allocThreeMatrix4() {\n    return this.threeMatrix4s.length > 0 ? this.threeMatrix4s.pop() : new Matrix4()\n  }\n\n  freeThreeMatrix4(m) {\n    this.threeMatrix4s.push(m)\n  }\n\n  allocThreeQuaternion() {\n    return this.threeQuaternions.length > 0 ? this.threeQuaternions.pop() : new Quaternion()\n  }\n\n  freeThreeQuaternion(q) {\n    this.threeQuaternions.push(q)\n  }\n\n  allocThreeEuler() {\n    return this.threeEulers.length > 0 ? this.threeEulers.pop() : new Euler()\n  }\n\n  freeThreeEuler(e) {\n    this.threeEulers.push(e)\n  }\n\n  allocTransform() {\n    return this.transforms.length > 0 ? this.transforms.pop() : new Ammo.btTransform()\n  }\n\n  freeTransform(t) {\n    this.transforms.push(t)\n  }\n\n  allocQuaternion() {\n    return this.quaternions.length > 0 ? this.quaternions.pop() : new Ammo.btQuaternion()\n  }\n\n  freeQuaternion(q) {\n    this.quaternions.push(q)\n  }\n\n  allocVector3() {\n    return this.vector3s.length > 0 ? this.vector3s.pop() : new Ammo.btVector3()\n  }\n\n  freeVector3(v) {\n    this.vector3s.push(v)\n  }\n\n  setIdentity(t) {\n    t.setIdentity()\n  }\n\n  getBasis(t) {\n    var q = this.allocQuaternion()\n    t.getBasis().getRotation(q)\n    return q\n  }\n\n  getBasisAsMatrix3(t) {\n    var q = this.getBasis(t)\n    var m = this.quaternionToMatrix3(q)\n    this.freeQuaternion(q)\n    return m\n  }\n\n  getOrigin(t) {\n    return t.getOrigin()\n  }\n\n  setOrigin(t, v) {\n    t.getOrigin().setValue(v.x(), v.y(), v.z())\n  }\n\n  copyOrigin(t1, t2) {\n    var o = t2.getOrigin()\n    this.setOrigin(t1, o)\n  }\n\n  setBasis(t, q) {\n    t.setRotation(q)\n  }\n\n  setBasisFromMatrix3(t, m) {\n    var q = this.matrix3ToQuaternion(m)\n    this.setBasis(t, q)\n    this.freeQuaternion(q)\n  }\n\n  setOriginFromArray3(t, a) {\n    t.getOrigin().setValue(a[0], a[1], a[2])\n  }\n\n  setOriginFromThreeVector3(t, v) {\n    t.getOrigin().setValue(v.x, v.y, v.z)\n  }\n\n  setBasisFromArray3(t, a) {\n    var thQ = this.allocThreeQuaternion()\n    var thE = this.allocThreeEuler()\n    thE.set(a[0], a[1], a[2])\n    this.setBasisFromThreeQuaternion(t, thQ.setFromEuler(thE))\n\n    this.freeThreeEuler(thE)\n    this.freeThreeQuaternion(thQ)\n  }\n\n  setBasisFromThreeQuaternion(t, a) {\n    var q = this.allocQuaternion()\n\n    q.setX(a.x)\n    q.setY(a.y)\n    q.setZ(a.z)\n    q.setW(a.w)\n    this.setBasis(t, q)\n\n    this.freeQuaternion(q)\n  }\n\n  multiplyTransforms(t1, t2) {\n    var t = this.allocTransform()\n    this.setIdentity(t)\n\n    var m1 = this.getBasisAsMatrix3(t1)\n    var m2 = this.getBasisAsMatrix3(t2)\n\n    var o1 = this.getOrigin(t1)\n    var o2 = this.getOrigin(t2)\n\n    var v1 = this.multiplyMatrix3ByVector3(m1, o2)\n    var v2 = this.addVector3(v1, o1)\n    this.setOrigin(t, v2)\n\n    var m3 = this.multiplyMatrices3(m1, m2)\n    this.setBasisFromMatrix3(t, m3)\n\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return t\n  }\n\n  inverseTransform(t) {\n    var t2 = this.allocTransform()\n\n    var m1 = this.getBasisAsMatrix3(t)\n    var o = this.getOrigin(t)\n\n    var m2 = this.transposeMatrix3(m1)\n    var v1 = this.negativeVector3(o)\n    var v2 = this.multiplyMatrix3ByVector3(m2, v1)\n\n    this.setOrigin(t2, v2)\n    this.setBasisFromMatrix3(t2, m2)\n\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return t2\n  }\n\n  multiplyMatrices3(m1, m2) {\n    var m3 = []\n\n    var v10 = this.rowOfMatrix3(m1, 0)\n    var v11 = this.rowOfMatrix3(m1, 1)\n    var v12 = this.rowOfMatrix3(m1, 2)\n\n    var v20 = this.columnOfMatrix3(m2, 0)\n    var v21 = this.columnOfMatrix3(m2, 1)\n    var v22 = this.columnOfMatrix3(m2, 2)\n\n    m3[0] = this.dotVectors3(v10, v20)\n    m3[1] = this.dotVectors3(v10, v21)\n    m3[2] = this.dotVectors3(v10, v22)\n    m3[3] = this.dotVectors3(v11, v20)\n    m3[4] = this.dotVectors3(v11, v21)\n    m3[5] = this.dotVectors3(v11, v22)\n    m3[6] = this.dotVectors3(v12, v20)\n    m3[7] = this.dotVectors3(v12, v21)\n    m3[8] = this.dotVectors3(v12, v22)\n\n    this.freeVector3(v10)\n    this.freeVector3(v11)\n    this.freeVector3(v12)\n    this.freeVector3(v20)\n    this.freeVector3(v21)\n    this.freeVector3(v22)\n\n    return m3\n  }\n\n  addVector3(v1, v2) {\n    var v = this.allocVector3()\n    v.setValue(v1.x() + v2.x(), v1.y() + v2.y(), v1.z() + v2.z())\n    return v\n  }\n\n  dotVectors3(v1, v2) {\n    return v1.x() * v2.x() + v1.y() * v2.y() + v1.z() * v2.z()\n  }\n\n  rowOfMatrix3(m, i) {\n    var v = this.allocVector3()\n    v.setValue(m[i * 3 + 0], m[i * 3 + 1], m[i * 3 + 2])\n    return v\n  }\n\n  columnOfMatrix3(m, i) {\n    var v = this.allocVector3()\n    v.setValue(m[i + 0], m[i + 3], m[i + 6])\n    return v\n  }\n\n  negativeVector3(v) {\n    var v2 = this.allocVector3()\n    v2.setValue(-v.x(), -v.y(), -v.z())\n    return v2\n  }\n\n  multiplyMatrix3ByVector3(m, v) {\n    var v4 = this.allocVector3()\n\n    var v0 = this.rowOfMatrix3(m, 0)\n    var v1 = this.rowOfMatrix3(m, 1)\n    var v2 = this.rowOfMatrix3(m, 2)\n    var x = this.dotVectors3(v0, v)\n    var y = this.dotVectors3(v1, v)\n    var z = this.dotVectors3(v2, v)\n\n    v4.setValue(x, y, z)\n\n    this.freeVector3(v0)\n    this.freeVector3(v1)\n    this.freeVector3(v2)\n\n    return v4\n  }\n\n  transposeMatrix3(m) {\n    var m2 = []\n    m2[0] = m[0]\n    m2[1] = m[3]\n    m2[2] = m[6]\n    m2[3] = m[1]\n    m2[4] = m[4]\n    m2[5] = m[7]\n    m2[6] = m[2]\n    m2[7] = m[5]\n    m2[8] = m[8]\n    return m2\n  }\n\n  quaternionToMatrix3(q) {\n    var m = []\n\n    var x = q.x()\n    var y = q.y()\n    var z = q.z()\n    var w = q.w()\n\n    var xx = x * x\n    var yy = y * y\n    var zz = z * z\n\n    var xy = x * y\n    var yz = y * z\n    var zx = z * x\n\n    var xw = x * w\n    var yw = y * w\n    var zw = z * w\n\n    m[0] = 1 - 2 * (yy + zz)\n    m[1] = 2 * (xy - zw)\n    m[2] = 2 * (zx + yw)\n    m[3] = 2 * (xy + zw)\n    m[4] = 1 - 2 * (zz + xx)\n    m[5] = 2 * (yz - xw)\n    m[6] = 2 * (zx - yw)\n    m[7] = 2 * (yz + xw)\n    m[8] = 1 - 2 * (xx + yy)\n\n    return m\n  }\n\n  matrix3ToQuaternion(m) {\n    var t = m[0] + m[4] + m[8]\n    var s, x, y, z, w\n\n    if (t > 0) {\n      s = Math.sqrt(t + 1.0) * 2\n      w = 0.25 * s\n      x = (m[7] - m[5]) / s\n      y = (m[2] - m[6]) / s\n      z = (m[3] - m[1]) / s\n    } else if (m[0] > m[4] && m[0] > m[8]) {\n      s = Math.sqrt(1.0 + m[0] - m[4] - m[8]) * 2\n      w = (m[7] - m[5]) / s\n      x = 0.25 * s\n      y = (m[1] + m[3]) / s\n      z = (m[2] + m[6]) / s\n    } else if (m[4] > m[8]) {\n      s = Math.sqrt(1.0 + m[4] - m[0] - m[8]) * 2\n      w = (m[2] - m[6]) / s\n      x = (m[1] + m[3]) / s\n      y = 0.25 * s\n      z = (m[5] + m[7]) / s\n    } else {\n      s = Math.sqrt(1.0 + m[8] - m[0] - m[4]) * 2\n      w = (m[3] - m[1]) / s\n      x = (m[2] + m[6]) / s\n      y = (m[5] + m[7]) / s\n      z = 0.25 * s\n    }\n\n    var q = this.allocQuaternion()\n    q.setX(x)\n    q.setY(y)\n    q.setZ(z)\n    q.setW(w)\n    return q\n  }\n}\n\n/**\n * @param {THREE.SkinnedMesh} mesh\n * @param {Ammo.btDiscreteDynamicsWorld} world\n * @param {Object} params\n * @param {ResourceManager} manager\n */\nclass RigidBody {\n  constructor(mesh, world, params, manager) {\n    this.mesh = mesh\n    this.world = world\n    this.params = params\n    this.manager = manager\n\n    this.body = null\n    this.bone = null\n    this.boneOffsetForm = null\n    this.boneOffsetFormInverse = null\n\n    this._init()\n  }\n\n  /**\n   * Resets rigid body transform to the current bone's.\n   *\n   * @return {RigidBody}\n   */\n  reset() {\n    this._setTransformFromBone()\n    return this\n  }\n\n  /**\n   * Updates rigid body's transform from the current bone.\n   *\n   * @return {RidigBody}\n   */\n  updateFromBone() {\n    if (this.params.boneIndex !== -1 && this.params.type === 0) {\n      this._setTransformFromBone()\n    }\n\n    return this\n  }\n\n  /**\n   * Updates bone from the current ridid body's transform.\n   *\n   * @return {RidigBody}\n   */\n  updateBone() {\n    if (this.params.type === 0 || this.params.boneIndex === -1) {\n      return this\n    }\n\n    this._updateBoneRotation()\n\n    if (this.params.type === 1) {\n      this._updateBonePosition()\n    }\n\n    this.bone.updateMatrixWorld(true)\n\n    if (this.params.type === 2) {\n      this._setPositionFromBone()\n    }\n\n    return this\n  }\n\n  // private methods\n\n  _init() {\n    function generateShape(p) {\n      switch (p.shapeType) {\n        case 0:\n          return new Ammo.btSphereShape(p.width)\n\n        case 1:\n          return new Ammo.btBoxShape(new Ammo.btVector3(p.width, p.height, p.depth))\n\n        case 2:\n          return new Ammo.btCapsuleShape(p.width, p.height)\n\n        default:\n          throw new Error('unknown shape type ' + p.shapeType)\n      }\n    }\n\n    const manager = this.manager\n    const params = this.params\n    const bones = this.mesh.skeleton.bones\n    const bone = params.boneIndex === -1 ? new Bone() : bones[params.boneIndex]\n\n    const shape = generateShape(params)\n    const weight = params.type === 0 ? 0 : params.weight\n    const localInertia = manager.allocVector3()\n    localInertia.setValue(0, 0, 0)\n\n    if (weight !== 0) {\n      shape.calculateLocalInertia(weight, localInertia)\n    }\n\n    const boneOffsetForm = manager.allocTransform()\n    manager.setIdentity(boneOffsetForm)\n    manager.setOriginFromArray3(boneOffsetForm, params.position)\n    manager.setBasisFromArray3(boneOffsetForm, params.rotation)\n\n    const vector = manager.allocThreeVector3()\n    const boneForm = manager.allocTransform()\n    manager.setIdentity(boneForm)\n    manager.setOriginFromThreeVector3(boneForm, bone.getWorldPosition(vector))\n\n    const form = manager.multiplyTransforms(boneForm, boneOffsetForm)\n    const state = new Ammo.btDefaultMotionState(form)\n\n    const info = new Ammo.btRigidBodyConstructionInfo(weight, state, shape, localInertia)\n    info.set_m_friction(params.friction)\n    info.set_m_restitution(params.restitution)\n\n    const body = new Ammo.btRigidBody(info)\n\n    if (params.type === 0) {\n      body.setCollisionFlags(body.getCollisionFlags() | 2)\n\n      /*\n       * It'd be better to comment out this line though in general I should call this method\n       * because I'm not sure why but physics will be more like MMD's\n       * if I comment out.\n       */\n      body.setActivationState(4)\n    }\n\n    body.setDamping(params.positionDamping, params.rotationDamping)\n    body.setSleepingThresholds(0, 0)\n\n    this.world.addRigidBody(body, 1 << params.groupIndex, params.groupTarget)\n\n    this.body = body\n    this.bone = bone\n    this.boneOffsetForm = boneOffsetForm\n    this.boneOffsetFormInverse = manager.inverseTransform(boneOffsetForm)\n\n    manager.freeVector3(localInertia)\n    manager.freeTransform(form)\n    manager.freeTransform(boneForm)\n    manager.freeThreeVector3(vector)\n  }\n\n  _getBoneTransform() {\n    const manager = this.manager\n    const p = manager.allocThreeVector3()\n    const q = manager.allocThreeQuaternion()\n    const s = manager.allocThreeVector3()\n\n    this.bone.matrixWorld.decompose(p, q, s)\n\n    const tr = manager.allocTransform()\n    manager.setOriginFromThreeVector3(tr, p)\n    manager.setBasisFromThreeQuaternion(tr, q)\n\n    const form = manager.multiplyTransforms(tr, this.boneOffsetForm)\n\n    manager.freeTransform(tr)\n    manager.freeThreeVector3(s)\n    manager.freeThreeQuaternion(q)\n    manager.freeThreeVector3(p)\n\n    return form\n  }\n\n  _getWorldTransformForBone() {\n    const manager = this.manager\n    const tr = this.body.getCenterOfMassTransform()\n    return manager.multiplyTransforms(tr, this.boneOffsetFormInverse)\n  }\n\n  _setTransformFromBone() {\n    const manager = this.manager\n    const form = this._getBoneTransform()\n\n    // TODO: check the most appropriate way to set\n    //this.body.setWorldTransform( form );\n    this.body.setCenterOfMassTransform(form)\n    this.body.getMotionState().setWorldTransform(form)\n\n    manager.freeTransform(form)\n  }\n\n  _setPositionFromBone() {\n    const manager = this.manager\n    const form = this._getBoneTransform()\n\n    const tr = manager.allocTransform()\n    this.body.getMotionState().getWorldTransform(tr)\n    manager.copyOrigin(tr, form)\n\n    // TODO: check the most appropriate way to set\n    //this.body.setWorldTransform( tr );\n    this.body.setCenterOfMassTransform(tr)\n    this.body.getMotionState().setWorldTransform(tr)\n\n    manager.freeTransform(tr)\n    manager.freeTransform(form)\n  }\n\n  _updateBoneRotation() {\n    const manager = this.manager\n\n    const tr = this._getWorldTransformForBone()\n    const q = manager.getBasis(tr)\n\n    const thQ = manager.allocThreeQuaternion()\n    const thQ2 = manager.allocThreeQuaternion()\n    const thQ3 = manager.allocThreeQuaternion()\n\n    thQ.set(q.x(), q.y(), q.z(), q.w())\n    thQ2.setFromRotationMatrix(this.bone.matrixWorld)\n    thQ2.conjugate()\n    thQ2.multiply(thQ)\n\n    //this.bone.quaternion.multiply( thQ2 );\n\n    thQ3.setFromRotationMatrix(this.bone.matrix)\n\n    // Renormalizing quaternion here because repeatedly transforming\n    // quaternion continuously accumulates floating point error and\n    // can end up being overflow. See #15335\n    this.bone.quaternion.copy(thQ2.multiply(thQ3).normalize())\n\n    manager.freeThreeQuaternion(thQ)\n    manager.freeThreeQuaternion(thQ2)\n    manager.freeThreeQuaternion(thQ3)\n\n    manager.freeQuaternion(q)\n    manager.freeTransform(tr)\n  }\n\n  _updateBonePosition() {\n    const manager = this.manager\n\n    const tr = this._getWorldTransformForBone()\n\n    const thV = manager.allocThreeVector3()\n\n    const o = manager.getOrigin(tr)\n    thV.set(o.x(), o.y(), o.z())\n\n    if (this.bone.parent) {\n      this.bone.parent.worldToLocal(thV)\n    }\n\n    this.bone.position.copy(thV)\n\n    manager.freeThreeVector3(thV)\n\n    manager.freeTransform(tr)\n  }\n}\n\n//\n\nclass Constraint {\n  /**\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Ammo.btDiscreteDynamicsWorld} world\n   * @param {RigidBody} bodyA\n   * @param {RigidBody} bodyB\n   * @param {Object} params\n   * @param {ResourceManager} manager\n   */\n  constructor(mesh, world, bodyA, bodyB, params, manager) {\n    this.mesh = mesh\n    this.world = world\n    this.bodyA = bodyA\n    this.bodyB = bodyB\n    this.params = params\n    this.manager = manager\n\n    this.constraint = null\n\n    this._init()\n  }\n\n  // private method\n\n  _init() {\n    const manager = this.manager\n    const params = this.params\n    const bodyA = this.bodyA\n    const bodyB = this.bodyB\n\n    const form = manager.allocTransform()\n    manager.setIdentity(form)\n    manager.setOriginFromArray3(form, params.position)\n    manager.setBasisFromArray3(form, params.rotation)\n\n    const formA = manager.allocTransform()\n    const formB = manager.allocTransform()\n\n    bodyA.body.getMotionState().getWorldTransform(formA)\n    bodyB.body.getMotionState().getWorldTransform(formB)\n\n    const formInverseA = manager.inverseTransform(formA)\n    const formInverseB = manager.inverseTransform(formB)\n\n    const formA2 = manager.multiplyTransforms(formInverseA, form)\n    const formB2 = manager.multiplyTransforms(formInverseB, form)\n\n    const constraint = new Ammo.btGeneric6DofSpringConstraint(bodyA.body, bodyB.body, formA2, formB2, true)\n\n    const lll = manager.allocVector3()\n    const lul = manager.allocVector3()\n    const all = manager.allocVector3()\n    const aul = manager.allocVector3()\n\n    lll.setValue(params.translationLimitation1[0], params.translationLimitation1[1], params.translationLimitation1[2])\n    lul.setValue(params.translationLimitation2[0], params.translationLimitation2[1], params.translationLimitation2[2])\n    all.setValue(params.rotationLimitation1[0], params.rotationLimitation1[1], params.rotationLimitation1[2])\n    aul.setValue(params.rotationLimitation2[0], params.rotationLimitation2[1], params.rotationLimitation2[2])\n\n    constraint.setLinearLowerLimit(lll)\n    constraint.setLinearUpperLimit(lul)\n    constraint.setAngularLowerLimit(all)\n    constraint.setAngularUpperLimit(aul)\n\n    for (let i = 0; i < 3; i++) {\n      if (params.springPosition[i] !== 0) {\n        constraint.enableSpring(i, true)\n        constraint.setStiffness(i, params.springPosition[i])\n      }\n    }\n\n    for (let i = 0; i < 3; i++) {\n      if (params.springRotation[i] !== 0) {\n        constraint.enableSpring(i + 3, true)\n        constraint.setStiffness(i + 3, params.springRotation[i])\n      }\n    }\n\n    /*\n     * Currently(10/31/2016) official ammo.js doesn't support\n     * btGeneric6DofSpringConstraint.setParam method.\n     * You need custom ammo.js (add the method into idl) if you wanna use.\n     * By setting this parameter, physics will be more like MMD's\n     */\n    if (constraint.setParam !== undefined) {\n      for (let i = 0; i < 6; i++) {\n        constraint.setParam(2, 0.475, i)\n      }\n    }\n\n    this.world.addConstraint(constraint, true)\n    this.constraint = constraint\n\n    manager.freeTransform(form)\n    manager.freeTransform(formA)\n    manager.freeTransform(formB)\n    manager.freeTransform(formInverseA)\n    manager.freeTransform(formInverseB)\n    manager.freeTransform(formA2)\n    manager.freeTransform(formB2)\n    manager.freeVector3(lll)\n    manager.freeVector3(lul)\n    manager.freeVector3(all)\n    manager.freeVector3(aul)\n  }\n}\n\nconst _position = /* @__PURE__ */ new Vector3()\nconst _quaternion = /* @__PURE__ */ new Quaternion()\nconst _scale = /* @__PURE__ */ new Vector3()\nconst _matrixWorldInv = /* @__PURE__ */ new Matrix4()\n\nclass MMDPhysicsHelper extends Object3D {\n  /**\n   * Visualize Rigid bodies\n   *\n   * @param {THREE.SkinnedMesh} mesh\n   * @param {Physics} physics\n   */\n  constructor(mesh, physics) {\n    super()\n\n    this.root = mesh\n    this.physics = physics\n\n    this.matrix.copy(mesh.matrixWorld)\n    this.matrixAutoUpdate = false\n\n    this.materials = []\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0xff8888),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0x88ff88),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this.materials.push(\n      new MeshBasicMaterial({\n        color: new Color(0x8888ff),\n        wireframe: true,\n        depthTest: false,\n        depthWrite: false,\n        opacity: 0.25,\n        transparent: true,\n      }),\n    )\n\n    this._init()\n  }\n\n  /**\n   * Frees the GPU-related resources allocated by this instance. Call this method whenever this instance is no longer used in your app.\n   */\n  dispose() {\n    const materials = this.materials\n    const children = this.children\n\n    for (let i = 0; i < materials.length; i++) {\n      materials[i].dispose()\n    }\n\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i]\n\n      if (child.isMesh) child.geometry.dispose()\n    }\n  }\n\n  /**\n   * Updates Rigid Bodies visualization.\n   */\n  updateMatrixWorld(force) {\n    var mesh = this.root\n\n    if (this.visible) {\n      var bodies = this.physics.bodies\n\n      _matrixWorldInv\n        .copy(mesh.matrixWorld)\n        .decompose(_position, _quaternion, _scale)\n        .compose(_position, _quaternion, _scale.set(1, 1, 1))\n        .invert()\n\n      for (var i = 0, il = bodies.length; i < il; i++) {\n        var body = bodies[i].body\n        var child = this.children[i]\n\n        var tr = body.getCenterOfMassTransform()\n        var origin = tr.getOrigin()\n        var rotation = tr.getRotation()\n\n        child.position.set(origin.x(), origin.y(), origin.z()).applyMatrix4(_matrixWorldInv)\n\n        child.quaternion\n          .setFromRotationMatrix(_matrixWorldInv)\n          .multiply(_quaternion.set(rotation.x(), rotation.y(), rotation.z(), rotation.w()))\n      }\n    }\n\n    this.matrix\n      .copy(mesh.matrixWorld)\n      .decompose(_position, _quaternion, _scale)\n      .compose(_position, _quaternion, _scale.set(1, 1, 1))\n\n    super.updateMatrixWorld(force)\n  }\n\n  // private method\n\n  _init() {\n    var bodies = this.physics.bodies\n\n    function createGeometry(param) {\n      switch (param.shapeType) {\n        case 0:\n          return new SphereGeometry(param.width, 16, 8)\n\n        case 1:\n          return new BoxGeometry(param.width * 2, param.height * 2, param.depth * 2, 8, 8, 8)\n\n        case 2:\n          return new CapsuleGeometry(param.width, param.height, 8, 16)\n\n        default:\n          return null\n      }\n    }\n\n    for (var i = 0, il = bodies.length; i < il; i++) {\n      var param = bodies[i].params\n      this.add(new Mesh(createGeometry(param), this.materials[param.type]))\n    }\n  }\n}\n\nexport { MMDPhysics }\n"], "names": ["param"], "mappings": ";;AA4BA,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUf,YAAY,MAAM,iBAAiB,mBAAmB,CAAE,GAAE,SAAS,IAAI;AACrE,QAAI,OAAO,SAAS,aAAa;AAC/B,YAAM,IAAI,MAAM,qEAAqE;AAAA,IACtF;AAED,SAAK,UAAU,IAAI,gBAAiB;AAEpC,SAAK,OAAO;AAQZ,SAAK,WAAW,OAAO,aAAa,SAAY,OAAO,WAAW,IAAI;AACtE,SAAK,aAAa,OAAO,eAAe,SAAY,OAAO,aAAa;AACxE,SAAK,UAAU,IAAI,QAAQ,GAAG,OAAO,IAAI,CAAC;AAE1C,QAAI,OAAO,YAAY;AAAW,WAAK,QAAQ,KAAK,OAAO,OAAO;AAElE,SAAK,QAAQ,OAAO,UAAU,SAAY,OAAO,QAAQ;AAEzD,SAAK,SAAS,CAAE;AAChB,SAAK,cAAc,CAAE;AAErB,SAAK,MAAM,MAAM,iBAAiB,gBAAgB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,OAAO;AACZ,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK;AAMlB,QAAI,oBAAoB;AAExB,UAAM,WAAW,QAAQ,kBAAmB;AAC5C,UAAM,aAAa,QAAQ,qBAAsB;AACjD,UAAM,QAAQ,QAAQ,kBAAmB;AAEzC,SAAK,YAAY,UAAU,UAAU,YAAY,KAAK;AAEtD,QAAI,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,GAAG;AACnD,0BAAoB;AAAA,IACrB;AAED,QAAI;AAEJ,QAAI,mBAAmB;AACrB,eAAS,KAAK;AAEd,UAAI,WAAW;AAAM,aAAK,SAAS;AAEnC,YAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,WAAK,MAAM,IAAI,GAAG,GAAG,CAAC;AACtB,WAAK,kBAAkB,IAAI;AAAA,IAC5B;AAID,SAAK,mBAAoB;AACzB,SAAK,gBAAgB,KAAK;AAC1B,SAAK,aAAc;AAInB,QAAI,mBAAmB;AACrB,UAAI,WAAW;AAAM,aAAK,SAAS;AAEnC,WAAK,MAAM,KAAK,KAAK;AAAA,IACtB;AAED,YAAQ,iBAAiB,KAAK;AAC9B,YAAQ,oBAAoB,UAAU;AACtC,YAAQ,iBAAiB,QAAQ;AAEjC,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ;AACN,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AACpD,WAAK,OAAO,CAAC,EAAE,MAAO;AAAA,IACvB;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,QAAQ;AACb,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAK,OAAO,IAAI,EAAE;AAAA,IACnB;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,WAAW,SAAS;AAClB,SAAK,MAAM,WAAW,IAAI,KAAK,UAAU,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;AACzE,SAAK,QAAQ,KAAK,OAAO;AAEzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe;AACb,WAAO,IAAI,iBAAiB,KAAK,MAAM,IAAI;AAAA,EAC5C;AAAA;AAAA,EAID,MAAM,MAAM,iBAAiB,kBAAkB;AAC7C,UAAM,UAAU,KAAK;AAMrB,UAAM,SAAS,KAAK;AAEpB,QAAI,WAAW;AAAM,WAAK,SAAS;AAEnC,UAAM,kBAAkB,QAAQ,kBAAmB;AACnD,UAAM,oBAAoB,QAAQ,qBAAsB;AACxD,UAAM,eAAe,QAAQ,kBAAmB;AAEhD,oBAAgB,KAAK,KAAK,QAAQ;AAClC,sBAAkB,KAAK,KAAK,UAAU;AACtC,iBAAa,KAAK,KAAK,KAAK;AAE5B,SAAK,SAAS,IAAI,GAAG,GAAG,CAAC;AACzB,SAAK,WAAW,IAAI,GAAG,GAAG,GAAG,CAAC;AAC9B,SAAK,MAAM,IAAI,GAAG,GAAG,CAAC;AAEtB,SAAK,kBAAkB,IAAI;AAE3B,QAAI,KAAK,UAAU,MAAM;AACvB,WAAK,QAAQ,KAAK,aAAc;AAChC,WAAK,WAAW,KAAK,OAAO;AAAA,IAC7B;AAED,SAAK,iBAAiB,eAAe;AACrC,SAAK,iBAAiB,gBAAgB;AAEtC,QAAI,WAAW;AAAM,WAAK,SAAS;AAEnC,SAAK,SAAS,KAAK,eAAe;AAClC,SAAK,WAAW,KAAK,iBAAiB;AACtC,SAAK,MAAM,KAAK,YAAY;AAE5B,SAAK,kBAAkB,IAAI;AAE3B,SAAK,MAAO;AAEZ,YAAQ,iBAAiB,eAAe;AACxC,YAAQ,oBAAoB,iBAAiB;AAC7C,YAAQ,iBAAiB,YAAY;AAAA,EACtC;AAAA,EAED,eAAe;AACb,UAAM,SAAS,IAAI,KAAK,gCAAiC;AACzD,UAAM,aAAa,IAAI,KAAK,sBAAsB,MAAM;AACxD,UAAM,QAAQ,IAAI,KAAK,iBAAkB;AACzC,UAAM,SAAS,IAAI,KAAK,oCAAqC;AAC7D,UAAM,QAAQ,IAAI,KAAK,wBAAwB,YAAY,OAAO,QAAQ,MAAM;AAChF,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,aAAa;AAC5B,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,WAAK,OAAO,KAAK,IAAI,UAAU,KAAK,MAAM,KAAK,OAAO,YAAY,CAAC,GAAG,KAAK,OAAO,CAAC;AAAA,IACpF;AAAA,EACF;AAAA,EAED,iBAAiB,aAAa;AAC5B,aAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AACpD,YAAM,SAAS,YAAY,CAAC;AAC5B,YAAM,QAAQ,KAAK,OAAO,OAAO,eAAe;AAChD,YAAM,QAAQ,KAAK,OAAO,OAAO,eAAe;AAChD,WAAK,YAAY,KAAK,IAAI,WAAW,KAAK,MAAM,KAAK,OAAO,OAAO,OAAO,QAAQ,KAAK,OAAO,CAAC;AAAA,IAChG;AAAA,EACF;AAAA,EAED,gBAAgB,OAAO;AACrB,UAAM,WAAW,KAAK;AACtB,QAAI,WAAW;AACf,QAAI,cAAe,QAAQ,WAAY,KAAK;AAE5C,QAAI,WAAW,UAAU;AACvB,iBAAW;AACX,mBAAa;AAAA,IACd;AAED,QAAI,aAAa,KAAK,YAAY;AAChC,mBAAa,KAAK;AAAA,IACnB;AAED,SAAK,MAAM,eAAe,UAAU,YAAY,QAAQ;AAAA,EACzD;AAAA,EAED,qBAAqB;AACnB,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AACpD,WAAK,OAAO,CAAC,EAAE,eAAgB;AAAA,IAChC;AAAA,EACF;AAAA,EAED,eAAe;AACb,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AACpD,WAAK,OAAO,CAAC,EAAE,WAAY;AAAA,IAC5B;AAAA,EACF;AACH;AAWA,MAAM,gBAAgB;AAAA,EACpB,cAAc;AAEZ,SAAK,gBAAgB,CAAE;AACvB,SAAK,gBAAgB,CAAE;AACvB,SAAK,mBAAmB,CAAE;AAC1B,SAAK,cAAc,CAAE;AAGrB,SAAK,aAAa,CAAE;AACpB,SAAK,cAAc,CAAE;AACrB,SAAK,WAAW,CAAE;AAAA,EACnB;AAAA,EAED,oBAAoB;AAClB,WAAO,KAAK,cAAc,SAAS,IAAI,KAAK,cAAc,QAAQ,IAAI,QAAS;AAAA,EAChF;AAAA,EAED,iBAAiB,GAAG;AAClB,SAAK,cAAc,KAAK,CAAC;AAAA,EAC1B;AAAA,EAED,oBAAoB;AAClB,WAAO,KAAK,cAAc,SAAS,IAAI,KAAK,cAAc,QAAQ,IAAI,QAAS;AAAA,EAChF;AAAA,EAED,iBAAiB,GAAG;AAClB,SAAK,cAAc,KAAK,CAAC;AAAA,EAC1B;AAAA,EAED,uBAAuB;AACrB,WAAO,KAAK,iBAAiB,SAAS,IAAI,KAAK,iBAAiB,QAAQ,IAAI,WAAY;AAAA,EACzF;AAAA,EAED,oBAAoB,GAAG;AACrB,SAAK,iBAAiB,KAAK,CAAC;AAAA,EAC7B;AAAA,EAED,kBAAkB;AAChB,WAAO,KAAK,YAAY,SAAS,IAAI,KAAK,YAAY,QAAQ,IAAI,MAAO;AAAA,EAC1E;AAAA,EAED,eAAe,GAAG;AAChB,SAAK,YAAY,KAAK,CAAC;AAAA,EACxB;AAAA,EAED,iBAAiB;AACf,WAAO,KAAK,WAAW,SAAS,IAAI,KAAK,WAAW,IAAK,IAAG,IAAI,KAAK,YAAa;AAAA,EACnF;AAAA,EAED,cAAc,GAAG;AACf,SAAK,WAAW,KAAK,CAAC;AAAA,EACvB;AAAA,EAED,kBAAkB;AAChB,WAAO,KAAK,YAAY,SAAS,IAAI,KAAK,YAAY,IAAK,IAAG,IAAI,KAAK,aAAc;AAAA,EACtF;AAAA,EAED,eAAe,GAAG;AAChB,SAAK,YAAY,KAAK,CAAC;AAAA,EACxB;AAAA,EAED,eAAe;AACb,WAAO,KAAK,SAAS,SAAS,IAAI,KAAK,SAAS,IAAK,IAAG,IAAI,KAAK,UAAW;AAAA,EAC7E;AAAA,EAED,YAAY,GAAG;AACb,SAAK,SAAS,KAAK,CAAC;AAAA,EACrB;AAAA,EAED,YAAY,GAAG;AACb,MAAE,YAAa;AAAA,EAChB;AAAA,EAED,SAAS,GAAG;AACV,QAAI,IAAI,KAAK,gBAAiB;AAC9B,MAAE,SAAQ,EAAG,YAAY,CAAC;AAC1B,WAAO;AAAA,EACR;AAAA,EAED,kBAAkB,GAAG;AACnB,QAAI,IAAI,KAAK,SAAS,CAAC;AACvB,QAAI,IAAI,KAAK,oBAAoB,CAAC;AAClC,SAAK,eAAe,CAAC;AACrB,WAAO;AAAA,EACR;AAAA,EAED,UAAU,GAAG;AACX,WAAO,EAAE,UAAW;AAAA,EACrB;AAAA,EAED,UAAU,GAAG,GAAG;AACd,MAAE,UAAS,EAAG,SAAS,EAAE,KAAK,EAAE,EAAC,GAAI,EAAE,EAAC,CAAE;AAAA,EAC3C;AAAA,EAED,WAAW,IAAI,IAAI;AACjB,QAAI,IAAI,GAAG,UAAW;AACtB,SAAK,UAAU,IAAI,CAAC;AAAA,EACrB;AAAA,EAED,SAAS,GAAG,GAAG;AACb,MAAE,YAAY,CAAC;AAAA,EAChB;AAAA,EAED,oBAAoB,GAAG,GAAG;AACxB,QAAI,IAAI,KAAK,oBAAoB,CAAC;AAClC,SAAK,SAAS,GAAG,CAAC;AAClB,SAAK,eAAe,CAAC;AAAA,EACtB;AAAA,EAED,oBAAoB,GAAG,GAAG;AACxB,MAAE,UAAS,EAAG,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACxC;AAAA,EAED,0BAA0B,GAAG,GAAG;AAC9B,MAAE,YAAY,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAA,EACrC;AAAA,EAED,mBAAmB,GAAG,GAAG;AACvB,QAAI,MAAM,KAAK,qBAAsB;AACrC,QAAI,MAAM,KAAK,gBAAiB;AAChC,QAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,SAAK,4BAA4B,GAAG,IAAI,aAAa,GAAG,CAAC;AAEzD,SAAK,eAAe,GAAG;AACvB,SAAK,oBAAoB,GAAG;AAAA,EAC7B;AAAA,EAED,4BAA4B,GAAG,GAAG;AAChC,QAAI,IAAI,KAAK,gBAAiB;AAE9B,MAAE,KAAK,EAAE,CAAC;AACV,MAAE,KAAK,EAAE,CAAC;AACV,MAAE,KAAK,EAAE,CAAC;AACV,MAAE,KAAK,EAAE,CAAC;AACV,SAAK,SAAS,GAAG,CAAC;AAElB,SAAK,eAAe,CAAC;AAAA,EACtB;AAAA,EAED,mBAAmB,IAAI,IAAI;AACzB,QAAI,IAAI,KAAK,eAAgB;AAC7B,SAAK,YAAY,CAAC;AAElB,QAAI,KAAK,KAAK,kBAAkB,EAAE;AAClC,QAAI,KAAK,KAAK,kBAAkB,EAAE;AAElC,QAAI,KAAK,KAAK,UAAU,EAAE;AAC1B,QAAI,KAAK,KAAK,UAAU,EAAE;AAE1B,QAAI,KAAK,KAAK,yBAAyB,IAAI,EAAE;AAC7C,QAAI,KAAK,KAAK,WAAW,IAAI,EAAE;AAC/B,SAAK,UAAU,GAAG,EAAE;AAEpB,QAAI,KAAK,KAAK,kBAAkB,IAAI,EAAE;AACtC,SAAK,oBAAoB,GAAG,EAAE;AAE9B,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY,EAAE;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,GAAG;AAClB,QAAI,KAAK,KAAK,eAAgB;AAE9B,QAAI,KAAK,KAAK,kBAAkB,CAAC;AACjC,QAAI,IAAI,KAAK,UAAU,CAAC;AAExB,QAAI,KAAK,KAAK,iBAAiB,EAAE;AACjC,QAAI,KAAK,KAAK,gBAAgB,CAAC;AAC/B,QAAI,KAAK,KAAK,yBAAyB,IAAI,EAAE;AAE7C,SAAK,UAAU,IAAI,EAAE;AACrB,SAAK,oBAAoB,IAAI,EAAE;AAE/B,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY,EAAE;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,kBAAkB,IAAI,IAAI;AACxB,QAAI,KAAK,CAAE;AAEX,QAAI,MAAM,KAAK,aAAa,IAAI,CAAC;AACjC,QAAI,MAAM,KAAK,aAAa,IAAI,CAAC;AACjC,QAAI,MAAM,KAAK,aAAa,IAAI,CAAC;AAEjC,QAAI,MAAM,KAAK,gBAAgB,IAAI,CAAC;AACpC,QAAI,MAAM,KAAK,gBAAgB,IAAI,CAAC;AACpC,QAAI,MAAM,KAAK,gBAAgB,IAAI,CAAC;AAEpC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AACjC,OAAG,CAAC,IAAI,KAAK,YAAY,KAAK,GAAG;AAEjC,SAAK,YAAY,GAAG;AACpB,SAAK,YAAY,GAAG;AACpB,SAAK,YAAY,GAAG;AACpB,SAAK,YAAY,GAAG;AACpB,SAAK,YAAY,GAAG;AACpB,SAAK,YAAY,GAAG;AAEpB,WAAO;AAAA,EACR;AAAA,EAED,WAAW,IAAI,IAAI;AACjB,QAAI,IAAI,KAAK,aAAc;AAC3B,MAAE,SAAS,GAAG,EAAG,IAAG,GAAG,EAAC,GAAI,GAAG,EAAC,IAAK,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG;AAC5D,WAAO;AAAA,EACR;AAAA,EAED,YAAY,IAAI,IAAI;AAClB,WAAO,GAAG,EAAG,IAAG,GAAG,EAAC,IAAK,GAAG,EAAG,IAAG,GAAG,EAAC,IAAK,GAAG,EAAG,IAAG,GAAG,EAAG;AAAA,EAC3D;AAAA,EAED,aAAa,GAAG,GAAG;AACjB,QAAI,IAAI,KAAK,aAAc;AAC3B,MAAE,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;AACnD,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,GAAG,GAAG;AACpB,QAAI,IAAI,KAAK,aAAc;AAC3B,MAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,WAAO;AAAA,EACR;AAAA,EAED,gBAAgB,GAAG;AACjB,QAAI,KAAK,KAAK,aAAc;AAC5B,OAAG,SAAS,CAAC,EAAE,EAAG,GAAE,CAAC,EAAE,EAAG,GAAE,CAAC,EAAE,EAAC,CAAE;AAClC,WAAO;AAAA,EACR;AAAA,EAED,yBAAyB,GAAG,GAAG;AAC7B,QAAI,KAAK,KAAK,aAAc;AAE5B,QAAI,KAAK,KAAK,aAAa,GAAG,CAAC;AAC/B,QAAI,KAAK,KAAK,aAAa,GAAG,CAAC;AAC/B,QAAI,KAAK,KAAK,aAAa,GAAG,CAAC;AAC/B,QAAI,IAAI,KAAK,YAAY,IAAI,CAAC;AAC9B,QAAI,IAAI,KAAK,YAAY,IAAI,CAAC;AAC9B,QAAI,IAAI,KAAK,YAAY,IAAI,CAAC;AAE9B,OAAG,SAAS,GAAG,GAAG,CAAC;AAEnB,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY,EAAE;AACnB,SAAK,YAAY,EAAE;AAEnB,WAAO;AAAA,EACR;AAAA,EAED,iBAAiB,GAAG;AAClB,QAAI,KAAK,CAAE;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,OAAG,CAAC,IAAI,EAAE,CAAC;AACX,WAAO;AAAA,EACR;AAAA,EAED,oBAAoB,GAAG;AACrB,QAAI,IAAI,CAAE;AAEV,QAAI,IAAI,EAAE,EAAG;AACb,QAAI,IAAI,EAAE,EAAG;AACb,QAAI,IAAI,EAAE,EAAG;AACb,QAAI,IAAI,EAAE,EAAG;AAEb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AAEb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AAEb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AACb,QAAI,KAAK,IAAI;AAEb,MAAE,CAAC,IAAI,IAAI,KAAK,KAAK;AACrB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,IAAI,KAAK,KAAK;AACrB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,KAAK,KAAK;AACjB,MAAE,CAAC,IAAI,IAAI,KAAK,KAAK;AAErB,WAAO;AAAA,EACR;AAAA,EAED,oBAAoB,GAAG;AACrB,QAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACzB,QAAI,GAAG,GAAG,GAAG,GAAG;AAEhB,QAAI,IAAI,GAAG;AACT,UAAI,KAAK,KAAK,IAAI,CAAG,IAAI;AACzB,UAAI,OAAO;AACX,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAAA,IACrB,WAAU,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACrC,UAAI,KAAK,KAAK,IAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1C,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,UAAI,OAAO;AACX,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAAA,IACrB,WAAU,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG;AACtB,UAAI,KAAK,KAAK,IAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1C,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,UAAI,OAAO;AACX,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AAAA,IAC1B,OAAW;AACL,UAAI,KAAK,KAAK,IAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1C,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,WAAK,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACpB,UAAI,OAAO;AAAA,IACZ;AAED,QAAI,IAAI,KAAK,gBAAiB;AAC9B,MAAE,KAAK,CAAC;AACR,MAAE,KAAK,CAAC;AACR,MAAE,KAAK,CAAC;AACR,MAAE,KAAK,CAAC;AACR,WAAO;AAAA,EACR;AACH;AAQA,MAAM,UAAU;AAAA,EACd,YAAY,MAAM,OAAO,QAAQ,SAAS;AACxC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAEf,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,wBAAwB;AAE7B,SAAK,MAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,QAAQ;AACN,SAAK,sBAAuB;AAC5B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,iBAAiB;AACf,QAAI,KAAK,OAAO,cAAc,MAAM,KAAK,OAAO,SAAS,GAAG;AAC1D,WAAK,sBAAuB;AAAA,IAC7B;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,aAAa;AACX,QAAI,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,cAAc,IAAI;AAC1D,aAAO;AAAA,IACR;AAED,SAAK,oBAAqB;AAE1B,QAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,WAAK,oBAAqB;AAAA,IAC3B;AAED,SAAK,KAAK,kBAAkB,IAAI;AAEhC,QAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,WAAK,qBAAsB;AAAA,IAC5B;AAED,WAAO;AAAA,EACR;AAAA;AAAA,EAID,QAAQ;AACN,aAAS,cAAc,GAAG;AACxB,cAAQ,EAAE,WAAS;AAAA,QACjB,KAAK;AACH,iBAAO,IAAI,KAAK,cAAc,EAAE,KAAK;AAAA,QAEvC,KAAK;AACH,iBAAO,IAAI,KAAK,WAAW,IAAI,KAAK,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;AAAA,QAE3E,KAAK;AACH,iBAAO,IAAI,KAAK,eAAe,EAAE,OAAO,EAAE,MAAM;AAAA,QAElD;AACE,gBAAM,IAAI,MAAM,wBAAwB,EAAE,SAAS;AAAA,MACtD;AAAA,IACF;AAED,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,KAAK,KAAK,SAAS;AACjC,UAAM,OAAO,OAAO,cAAc,KAAK,IAAI,SAAS,MAAM,OAAO,SAAS;AAE1E,UAAM,QAAQ,cAAc,MAAM;AAClC,UAAM,SAAS,OAAO,SAAS,IAAI,IAAI,OAAO;AAC9C,UAAM,eAAe,QAAQ,aAAc;AAC3C,iBAAa,SAAS,GAAG,GAAG,CAAC;AAE7B,QAAI,WAAW,GAAG;AAChB,YAAM,sBAAsB,QAAQ,YAAY;AAAA,IACjD;AAED,UAAM,iBAAiB,QAAQ,eAAgB;AAC/C,YAAQ,YAAY,cAAc;AAClC,YAAQ,oBAAoB,gBAAgB,OAAO,QAAQ;AAC3D,YAAQ,mBAAmB,gBAAgB,OAAO,QAAQ;AAE1D,UAAM,SAAS,QAAQ,kBAAmB;AAC1C,UAAM,WAAW,QAAQ,eAAgB;AACzC,YAAQ,YAAY,QAAQ;AAC5B,YAAQ,0BAA0B,UAAU,KAAK,iBAAiB,MAAM,CAAC;AAEzE,UAAM,OAAO,QAAQ,mBAAmB,UAAU,cAAc;AAChE,UAAM,QAAQ,IAAI,KAAK,qBAAqB,IAAI;AAEhD,UAAM,OAAO,IAAI,KAAK,4BAA4B,QAAQ,OAAO,OAAO,YAAY;AACpF,SAAK,eAAe,OAAO,QAAQ;AACnC,SAAK,kBAAkB,OAAO,WAAW;AAEzC,UAAM,OAAO,IAAI,KAAK,YAAY,IAAI;AAEtC,QAAI,OAAO,SAAS,GAAG;AACrB,WAAK,kBAAkB,KAAK,kBAAiB,IAAK,CAAC;AAOnD,WAAK,mBAAmB,CAAC;AAAA,IAC1B;AAED,SAAK,WAAW,OAAO,iBAAiB,OAAO,eAAe;AAC9D,SAAK,sBAAsB,GAAG,CAAC;AAE/B,SAAK,MAAM,aAAa,MAAM,KAAK,OAAO,YAAY,OAAO,WAAW;AAExE,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,QAAQ,iBAAiB,cAAc;AAEpE,YAAQ,YAAY,YAAY;AAChC,YAAQ,cAAc,IAAI;AAC1B,YAAQ,cAAc,QAAQ;AAC9B,YAAQ,iBAAiB,MAAM;AAAA,EAChC;AAAA,EAED,oBAAoB;AAClB,UAAM,UAAU,KAAK;AACrB,UAAM,IAAI,QAAQ,kBAAmB;AACrC,UAAM,IAAI,QAAQ,qBAAsB;AACxC,UAAM,IAAI,QAAQ,kBAAmB;AAErC,SAAK,KAAK,YAAY,UAAU,GAAG,GAAG,CAAC;AAEvC,UAAM,KAAK,QAAQ,eAAgB;AACnC,YAAQ,0BAA0B,IAAI,CAAC;AACvC,YAAQ,4BAA4B,IAAI,CAAC;AAEzC,UAAM,OAAO,QAAQ,mBAAmB,IAAI,KAAK,cAAc;AAE/D,YAAQ,cAAc,EAAE;AACxB,YAAQ,iBAAiB,CAAC;AAC1B,YAAQ,oBAAoB,CAAC;AAC7B,YAAQ,iBAAiB,CAAC;AAE1B,WAAO;AAAA,EACR;AAAA,EAED,4BAA4B;AAC1B,UAAM,UAAU,KAAK;AACrB,UAAM,KAAK,KAAK,KAAK,yBAA0B;AAC/C,WAAO,QAAQ,mBAAmB,IAAI,KAAK,qBAAqB;AAAA,EACjE;AAAA,EAED,wBAAwB;AACtB,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK,kBAAmB;AAIrC,SAAK,KAAK,yBAAyB,IAAI;AACvC,SAAK,KAAK,iBAAiB,kBAAkB,IAAI;AAEjD,YAAQ,cAAc,IAAI;AAAA,EAC3B;AAAA,EAED,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,OAAO,KAAK,kBAAmB;AAErC,UAAM,KAAK,QAAQ,eAAgB;AACnC,SAAK,KAAK,iBAAiB,kBAAkB,EAAE;AAC/C,YAAQ,WAAW,IAAI,IAAI;AAI3B,SAAK,KAAK,yBAAyB,EAAE;AACrC,SAAK,KAAK,iBAAiB,kBAAkB,EAAE;AAE/C,YAAQ,cAAc,EAAE;AACxB,YAAQ,cAAc,IAAI;AAAA,EAC3B;AAAA,EAED,sBAAsB;AACpB,UAAM,UAAU,KAAK;AAErB,UAAM,KAAK,KAAK,0BAA2B;AAC3C,UAAM,IAAI,QAAQ,SAAS,EAAE;AAE7B,UAAM,MAAM,QAAQ,qBAAsB;AAC1C,UAAM,OAAO,QAAQ,qBAAsB;AAC3C,UAAM,OAAO,QAAQ,qBAAsB;AAE3C,QAAI,IAAI,EAAE,EAAG,GAAE,EAAE,EAAG,GAAE,EAAE,EAAC,GAAI,EAAE,EAAC,CAAE;AAClC,SAAK,sBAAsB,KAAK,KAAK,WAAW;AAChD,SAAK,UAAW;AAChB,SAAK,SAAS,GAAG;AAIjB,SAAK,sBAAsB,KAAK,KAAK,MAAM;AAK3C,SAAK,KAAK,WAAW,KAAK,KAAK,SAAS,IAAI,EAAE,WAAW;AAEzD,YAAQ,oBAAoB,GAAG;AAC/B,YAAQ,oBAAoB,IAAI;AAChC,YAAQ,oBAAoB,IAAI;AAEhC,YAAQ,eAAe,CAAC;AACxB,YAAQ,cAAc,EAAE;AAAA,EACzB;AAAA,EAED,sBAAsB;AACpB,UAAM,UAAU,KAAK;AAErB,UAAM,KAAK,KAAK,0BAA2B;AAE3C,UAAM,MAAM,QAAQ,kBAAmB;AAEvC,UAAM,IAAI,QAAQ,UAAU,EAAE;AAC9B,QAAI,IAAI,EAAE,EAAG,GAAE,EAAE,EAAG,GAAE,EAAE,GAAG;AAE3B,QAAI,KAAK,KAAK,QAAQ;AACpB,WAAK,KAAK,OAAO,aAAa,GAAG;AAAA,IAClC;AAED,SAAK,KAAK,SAAS,KAAK,GAAG;AAE3B,YAAQ,iBAAiB,GAAG;AAE5B,YAAQ,cAAc,EAAE;AAAA,EACzB;AACH;AAIA,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASf,YAAY,MAAM,OAAO,OAAO,OAAO,QAAQ,SAAS;AACtD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAEf,SAAK,aAAa;AAElB,SAAK,MAAO;AAAA,EACb;AAAA;AAAA,EAID,QAAQ;AACN,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,KAAK;AAEnB,UAAM,OAAO,QAAQ,eAAgB;AACrC,YAAQ,YAAY,IAAI;AACxB,YAAQ,oBAAoB,MAAM,OAAO,QAAQ;AACjD,YAAQ,mBAAmB,MAAM,OAAO,QAAQ;AAEhD,UAAM,QAAQ,QAAQ,eAAgB;AACtC,UAAM,QAAQ,QAAQ,eAAgB;AAEtC,UAAM,KAAK,iBAAiB,kBAAkB,KAAK;AACnD,UAAM,KAAK,iBAAiB,kBAAkB,KAAK;AAEnD,UAAM,eAAe,QAAQ,iBAAiB,KAAK;AACnD,UAAM,eAAe,QAAQ,iBAAiB,KAAK;AAEnD,UAAM,SAAS,QAAQ,mBAAmB,cAAc,IAAI;AAC5D,UAAM,SAAS,QAAQ,mBAAmB,cAAc,IAAI;AAE5D,UAAM,aAAa,IAAI,KAAK,8BAA8B,MAAM,MAAM,MAAM,MAAM,QAAQ,QAAQ,IAAI;AAEtG,UAAM,MAAM,QAAQ,aAAc;AAClC,UAAM,MAAM,QAAQ,aAAc;AAClC,UAAM,MAAM,QAAQ,aAAc;AAClC,UAAM,MAAM,QAAQ,aAAc;AAElC,QAAI,SAAS,OAAO,uBAAuB,CAAC,GAAG,OAAO,uBAAuB,CAAC,GAAG,OAAO,uBAAuB,CAAC,CAAC;AACjH,QAAI,SAAS,OAAO,uBAAuB,CAAC,GAAG,OAAO,uBAAuB,CAAC,GAAG,OAAO,uBAAuB,CAAC,CAAC;AACjH,QAAI,SAAS,OAAO,oBAAoB,CAAC,GAAG,OAAO,oBAAoB,CAAC,GAAG,OAAO,oBAAoB,CAAC,CAAC;AACxG,QAAI,SAAS,OAAO,oBAAoB,CAAC,GAAG,OAAO,oBAAoB,CAAC,GAAG,OAAO,oBAAoB,CAAC,CAAC;AAExG,eAAW,oBAAoB,GAAG;AAClC,eAAW,oBAAoB,GAAG;AAClC,eAAW,qBAAqB,GAAG;AACnC,eAAW,qBAAqB,GAAG;AAEnC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,OAAO,eAAe,CAAC,MAAM,GAAG;AAClC,mBAAW,aAAa,GAAG,IAAI;AAC/B,mBAAW,aAAa,GAAG,OAAO,eAAe,CAAC,CAAC;AAAA,MACpD;AAAA,IACF;AAED,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,OAAO,eAAe,CAAC,MAAM,GAAG;AAClC,mBAAW,aAAa,IAAI,GAAG,IAAI;AACnC,mBAAW,aAAa,IAAI,GAAG,OAAO,eAAe,CAAC,CAAC;AAAA,MACxD;AAAA,IACF;AAQD,QAAI,WAAW,aAAa,QAAW;AACrC,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,mBAAW,SAAS,GAAG,OAAO,CAAC;AAAA,MAChC;AAAA,IACF;AAED,SAAK,MAAM,cAAc,YAAY,IAAI;AACzC,SAAK,aAAa;AAElB,YAAQ,cAAc,IAAI;AAC1B,YAAQ,cAAc,KAAK;AAC3B,YAAQ,cAAc,KAAK;AAC3B,YAAQ,cAAc,YAAY;AAClC,YAAQ,cAAc,YAAY;AAClC,YAAQ,cAAc,MAAM;AAC5B,YAAQ,cAAc,MAAM;AAC5B,YAAQ,YAAY,GAAG;AACvB,YAAQ,YAAY,GAAG;AACvB,YAAQ,YAAY,GAAG;AACvB,YAAQ,YAAY,GAAG;AAAA,EACxB;AACH;AAEA,MAAM,YAA4B,oBAAI,QAAS;AAC/C,MAAM,cAA8B,oBAAI,WAAY;AACpD,MAAM,SAAyB,oBAAI,QAAS;AAC5C,MAAM,kBAAkC,oBAAI,QAAS;AAErD,MAAM,yBAAyB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtC,YAAY,MAAM,SAAS;AACzB,UAAO;AAEP,SAAK,OAAO;AACZ,SAAK,UAAU;AAEf,SAAK,OAAO,KAAK,KAAK,WAAW;AACjC,SAAK,mBAAmB;AAExB,SAAK,YAAY,CAAE;AAEnB,SAAK,UAAU;AAAA,MACb,IAAI,kBAAkB;AAAA,QACpB,OAAO,IAAI,MAAM,QAAQ;AAAA,QACzB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA,MACrB,CAAO;AAAA,IACF;AAED,SAAK,UAAU;AAAA,MACb,IAAI,kBAAkB;AAAA,QACpB,OAAO,IAAI,MAAM,OAAQ;AAAA,QACzB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA,MACrB,CAAO;AAAA,IACF;AAED,SAAK,UAAU;AAAA,MACb,IAAI,kBAAkB;AAAA,QACpB,OAAO,IAAI,MAAM,OAAQ;AAAA,QACzB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,aAAa;AAAA,MACrB,CAAO;AAAA,IACF;AAED,SAAK,MAAO;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACR,UAAM,YAAY,KAAK;AACvB,UAAM,WAAW,KAAK;AAEtB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAU,CAAC,EAAE,QAAS;AAAA,IACvB;AAED,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,QAAQ,SAAS,CAAC;AAExB,UAAI,MAAM;AAAQ,cAAM,SAAS,QAAS;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB,OAAO;AACvB,QAAI,OAAO,KAAK;AAEhB,QAAI,KAAK,SAAS;AAChB,UAAI,SAAS,KAAK,QAAQ;AAE1B,sBACG,KAAK,KAAK,WAAW,EACrB,UAAU,WAAW,aAAa,MAAM,EACxC,QAAQ,WAAW,aAAa,OAAO,IAAI,GAAG,GAAG,CAAC,CAAC,EACnD,OAAQ;AAEX,eAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,YAAI,OAAO,OAAO,CAAC,EAAE;AACrB,YAAI,QAAQ,KAAK,SAAS,CAAC;AAE3B,YAAI,KAAK,KAAK,yBAA0B;AACxC,YAAI,SAAS,GAAG,UAAW;AAC3B,YAAI,WAAW,GAAG,YAAa;AAE/B,cAAM,SAAS,IAAI,OAAO,EAAC,GAAI,OAAO,EAAC,GAAI,OAAO,EAAG,CAAA,EAAE,aAAa,eAAe;AAEnF,cAAM,WACH,sBAAsB,eAAe,EACrC,SAAS,YAAY,IAAI,SAAS,EAAG,GAAE,SAAS,EAAG,GAAE,SAAS,EAAC,GAAI,SAAS,EAAG,CAAA,CAAC;AAAA,MACpF;AAAA,IACF;AAED,SAAK,OACF,KAAK,KAAK,WAAW,EACrB,UAAU,WAAW,aAAa,MAAM,EACxC,QAAQ,WAAW,aAAa,OAAO,IAAI,GAAG,GAAG,CAAC,CAAC;AAEtD,UAAM,kBAAkB,KAAK;AAAA,EAC9B;AAAA;AAAA,EAID,QAAQ;AACN,QAAI,SAAS,KAAK,QAAQ;AAE1B,aAAS,eAAeA,QAAO;AAC7B,cAAQA,OAAM,WAAS;AAAA,QACrB,KAAK;AACH,iBAAO,IAAI,eAAeA,OAAM,OAAO,IAAI,CAAC;AAAA,QAE9C,KAAK;AACH,iBAAO,IAAI,YAAYA,OAAM,QAAQ,GAAGA,OAAM,SAAS,GAAGA,OAAM,QAAQ,GAAG,GAAG,GAAG,CAAC;AAAA,QAEpF,KAAK;AACH,iBAAO,IAAI,gBAAgBA,OAAM,OAAOA,OAAM,QAAQ,GAAG,EAAE;AAAA,QAE7D;AACE,iBAAO;AAAA,MACV;AAAA,IACF;AAED,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAK;AAC/C,UAAI,QAAQ,OAAO,CAAC,EAAE;AACtB,WAAK,IAAI,IAAI,KAAK,eAAe,KAAK,GAAG,KAAK,UAAU,MAAM,IAAI,CAAC,CAAC;AAAA,IACrE;AAAA,EACF;AACH;"}