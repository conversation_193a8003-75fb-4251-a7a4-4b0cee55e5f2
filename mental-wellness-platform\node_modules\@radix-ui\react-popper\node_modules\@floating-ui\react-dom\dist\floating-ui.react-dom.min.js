!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@floating-ui/dom"),require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/dom","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReactDOM={},e.FloatingUIDOM,e.React,e.ReactDOM)}(this,(function(e,t,r,n){"use strict";function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var o=u(r),c=u(n),f="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function l(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let r,n,u;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(r=e.length,r!=t.length)return!1;for(n=r;0!=n--;)if(!l(e[n],t[n]))return!1;return!0}if(u=Object.keys(e),r=u.length,r!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!Object.prototype.hasOwnProperty.call(t,u[n]))return!1;for(n=r;0!=n--;){const r=u[n];if(("_owner"!==r||!e.$$typeof)&&!l(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}e.arrow=e=>{const{element:r,padding:n}=e;return{name:"arrow",options:e,fn(e){return u=r,Object.prototype.hasOwnProperty.call(u,"current")?null!=r.current?t.arrow({element:r.current,padding:n}).fn(e):{}:r?t.arrow({element:r,padding:n}).fn(e):{};var u}}},e.useFloating=function(e){let{middleware:r,placement:n="bottom",strategy:u="absolute",whileElementsMounted:a}=void 0===e?{}:e;const i=o.useRef(null),s=o.useRef(null),d=function(e){const t=o.useRef(e);return f((()=>{t.current=e})),t}(a),p=o.useRef(null),[y,m]=o.useState({x:null,y:null,strategy:u,placement:n,middlewareData:{}}),[g,b]=o.useState(r);l(null==g?void 0:g.map((e=>{let{options:t}=e;return t})),null==r?void 0:r.map((e=>{let{options:t}=e;return t})))||b(r);const O=o.useCallback((()=>{i.current&&s.current&&t.computePosition(i.current,s.current,{middleware:g,placement:n,strategy:u}).then((e=>{h.current&&c.flushSync((()=>{m(e)}))}))}),[g,n,u]);f((()=>{h.current&&O()}),[O]);const h=o.useRef(!1);f((()=>(h.current=!0,()=>{h.current=!1})),[]);const j=o.useCallback((()=>{if("function"==typeof p.current&&(p.current(),p.current=null),i.current&&s.current)if(d.current){const e=d.current(i.current,s.current,O);p.current=e}else O()}),[O,d]),w=o.useCallback((e=>{i.current=e,j()}),[j]),k=o.useCallback((e=>{s.current=e,j()}),[j]),v=o.useMemo((()=>({reference:i,floating:s})),[]);return o.useMemo((()=>({...y,update:O,refs:v,reference:w,floating:k})),[y,O,v,w,k])},Object.keys(t).forEach((function(r){"default"===r||e.hasOwnProperty(r)||Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[r]}})})),Object.defineProperty(e,"__esModule",{value:!0})}));
