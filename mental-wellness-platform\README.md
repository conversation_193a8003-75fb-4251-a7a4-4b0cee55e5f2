# Mental Wellness Platform

An AI-powered mental wellness platform designed specifically for women and children, providing emotional support, therapeutic games, safety monitoring, and educational resources.

## 🌟 Features

- **AI-Powered Chat Support**: 24/7 emotional support with safety monitoring
- **Therapeutic Games**: Interactive games for stress relief and coping skills
- **Safety Monitoring**: AI-based detection of distress patterns and concerning behavior
- **Educational Content**: Videos, stories, and resources for emotional intelligence
- **Crisis Resources**: Quick access to helplines and emergency services
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- OpenAI API key (optional, for AI chat functionality)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mental-wellness-platform
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` and add your configuration:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js app directory
│   ├── api/            # API routes
│   ├── chat/           # Chat support page
│   ├── games/          # Games section
│   ├── resources/      # Crisis resources
│   └── content/        # Educational content
├── components/         # React components
│   ├── ui/            # Reusable UI components
│   ├── layout/        # Layout components
│   ├── chatbot/       # Chat-related components
│   ├── games/         # Game components
│   ├── safety/        # Safety monitoring components
│   └── resources/     # Resource components
├── lib/               # Utility functions and configurations
├── types/             # TypeScript type definitions
├── services/          # External service integrations
├── hooks/             # Custom React hooks
└── contexts/          # React context providers
```

## 🔧 Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key for AI chat functionality
- `DATABASE_URL`: Database connection string (when implementing persistence)
- `NEXTAUTH_SECRET`: Secret for authentication (when implementing user accounts)

### Safety Monitoring

The platform includes built-in safety monitoring that detects:
- Emergency situations
- Self-harm indicators
- Abuse patterns
- General distress signals

Risk levels are automatically assessed and appropriate resources are provided.

## 🛡️ Safety Features

- **Crisis Detection**: Automatic detection of crisis keywords and phrases
- **Emergency Resources**: Quick access to crisis hotlines and emergency services
- **Risk Assessment**: Multi-level risk assessment (low, medium, high, critical)
- **Professional Referrals**: Guidance to seek professional help when appropriate

## 🎮 Therapeutic Games

The platform will include various therapeutic games:
- Stress relief activities
- Mindfulness exercises
- Emotional regulation tools
- Coping skill builders

## 📚 Educational Content

- Emotional intelligence resources
- Safety and protection information
- Coping strategies and techniques
- Age-appropriate wellness content

## 🆘 Emergency Contacts

- **Emergency Services**: 911
- **Crisis Hotline**: 988 (Suicide & Crisis Lifeline)
- **Crisis Text Line**: Text HOME to 741741
- **Child Abuse Hotline**: 1-800-4-A-CHILD (**************)
- **Domestic Violence Hotline**: **************

## 🤝 Contributing

This project aims to provide crucial mental health support. Contributions should prioritize:
- User safety and privacy
- Accessibility and inclusivity
- Evidence-based approaches
- Professional standards compliance

## 📄 License

This project is designed for mental health support and should be used responsibly with appropriate professional oversight.

## ⚠️ Disclaimer

This platform provides support and resources but is not a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of qualified health providers with any questions regarding mental health conditions.
