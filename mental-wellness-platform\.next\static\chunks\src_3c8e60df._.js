(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": ()=>cn,
    "debounce": ()=>debounce,
    "formatDate": ()=>formatDate,
    "formatTime": ()=>formatTime,
    "generateId": ()=>generateId,
    "sanitizeInput": ()=>sanitizeInput
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}
function formatTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function debounce(func, wait) {
    let timeout;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function sanitizeInput(input) {
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '').replace(/[<>]/g, '').trim();
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/chatbot/VoiceChat.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>VoiceChat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MicrophoneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MicrophoneIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/MicrophoneIcon.js [app-client] (ecmascript) <export default as MicrophoneIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$SpeakerWaveIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SpeakerWaveIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js [app-client] (ecmascript) <export default as SpeakerWaveIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$StopIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__StopIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/StopIcon.js [app-client] (ecmascript) <export default as StopIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$LanguageIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LanguageIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js [app-client] (ecmascript) <export default as LanguageIcon>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function VoiceChat(param) {
    let { onTranscript, onSpeakResponse, isListening, setIsListening, language, setLanguage } = param;
    _s();
    const [isSupported, setIsSupported] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isSpeaking, setIsSpeaking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [transcript, setTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const recognitionRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const synthRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "VoiceChat.useEffect": ()=>{
            // Check for speech recognition support
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const speechSynthesis = window.speechSynthesis;
            if (SpeechRecognition && speechSynthesis) {
                setIsSupported(true);
                synthRef.current = speechSynthesis;
                // Initialize speech recognition
                const recognition = new SpeechRecognition();
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = language === 'ta' ? 'ta-IN' : 'en-US';
                recognition.onstart = ({
                    "VoiceChat.useEffect": ()=>{
                        console.log('Voice recognition started');
                    }
                })["VoiceChat.useEffect"];
                recognition.onresult = ({
                    "VoiceChat.useEffect": (event)=>{
                        let finalTranscript = '';
                        let interimTranscript = '';
                        for(let i = event.resultIndex; i < event.results.length; i++){
                            const transcript = event.results[i][0].transcript;
                            if (event.results[i].isFinal) {
                                finalTranscript += transcript;
                            } else {
                                interimTranscript += transcript;
                            }
                        }
                        setTranscript(interimTranscript);
                        if (finalTranscript) {
                            onTranscript(finalTranscript);
                            setTranscript('');
                            setIsListening(false);
                        }
                    }
                })["VoiceChat.useEffect"];
                recognition.onerror = ({
                    "VoiceChat.useEffect": (event)=>{
                        console.error('Speech recognition error:', event.error);
                        setIsListening(false);
                    }
                })["VoiceChat.useEffect"];
                recognition.onend = ({
                    "VoiceChat.useEffect": ()=>{
                        setIsListening(false);
                    }
                })["VoiceChat.useEffect"];
                recognitionRef.current = recognition;
            }
            return ({
                "VoiceChat.useEffect": ()=>{
                    if (recognitionRef.current) {
                        recognitionRef.current.stop();
                    }
                }
            })["VoiceChat.useEffect"];
        }
    }["VoiceChat.useEffect"], [
        language,
        onTranscript,
        setIsListening
    ]);
    const startListening = ()=>{
        if (recognitionRef.current && !isListening) {
            recognitionRef.current.lang = language === 'ta' ? 'ta-IN' : 'en-US';
            recognitionRef.current.start();
            setIsListening(true);
        }
    };
    const stopListening = ()=>{
        if (recognitionRef.current && isListening) {
            recognitionRef.current.stop();
            setIsListening(false);
        }
    };
    const speakText = function(text) {
        let lang = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : language;
        if (synthRef.current && text) {
            // Stop any current speech
            synthRef.current.cancel();
            const utterance = new SpeechSynthesisUtterance(text);
            // Set language and voice
            utterance.lang = lang === 'ta' ? 'ta-IN' : 'en-US';
            utterance.rate = 0.9;
            utterance.pitch = 1;
            utterance.volume = 1;
            // Try to find a suitable voice
            const voices = synthRef.current.getVoices();
            const preferredVoice = voices.find((voice)=>lang === 'ta' ? voice.lang.includes('ta') || voice.lang.includes('Tamil') : voice.lang.includes('en') && voice.lang.includes('US'));
            if (preferredVoice) {
                utterance.voice = preferredVoice;
            }
            utterance.onstart = ()=>setIsSpeaking(true);
            utterance.onend = ()=>setIsSpeaking(false);
            utterance.onerror = ()=>setIsSpeaking(false);
            synthRef.current.speak(utterance);
        }
    };
    const stopSpeaking = ()=>{
        if (synthRef.current) {
            synthRef.current.cancel();
            setIsSpeaking(false);
        }
    };
    // Expose speakText function to parent
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "VoiceChat.useEffect": ()=>{
            onSpeakResponse(speakText, language);
        }
    }["VoiceChat.useEffect"], [
        language,
        onSpeakResponse
    ]);
    if (!isSupported) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-yellow-800",
                children: "Voice features are not supported in your browser. Please use Chrome or Edge for voice interaction."
            }, void 0, false, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
            lineNumber: 152,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$LanguageIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LanguageIcon$3e$__["LanguageIcon"], {
                        className: "h-5 w-5 text-gray-500"
                    }, void 0, false, {
                        fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        value: language,
                        onChange: (e)=>setLanguage(e.target.value),
                        className: "text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "en",
                                children: "English"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "ta",
                                children: "தமிழ் (Tamil)"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 163,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2",
                children: !isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: startListening,
                    className: "flex items-center space-x-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MicrophoneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MicrophoneIcon$3e$__["MicrophoneIcon"], {
                            className: "h-5 w-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 182,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm",
                            children: language === 'ta' ? 'பேசுங்கள்' : 'Speak'
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 183,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                    lineNumber: 178,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: stopListening,
                    className: "flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$StopIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__StopIcon$3e$__["StopIcon"], {
                            className: "h-5 w-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 192,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm",
                            children: language === 'ta' ? 'நிறுத்து' : 'Stop'
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 193,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                    lineNumber: 188,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 176,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2",
                children: !isSpeaking ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>speakText(language === 'ta' ? 'வணக்கம்! நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்.' : 'Hello! I am here to help you.'),
                    className: "flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$SpeakerWaveIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__SpeakerWaveIcon$3e$__["SpeakerWaveIcon"], {
                            className: "h-5 w-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 210,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm",
                            children: language === 'ta' ? 'சோதனை' : 'Test Voice'
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 211,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                    lineNumber: 203,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: stopSpeaking,
                    className: "flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$StopIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__StopIcon$3e$__["StopIcon"], {
                            className: "h-5 w-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 220,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-sm",
                            children: language === 'ta' ? 'அமைதி' : 'Stop Voice'
                        }, void 0, false, {
                            fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                            lineNumber: 221,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                    lineNumber: 216,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this),
            transcript && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 text-sm text-gray-600 italic",
                children: [
                    language === 'ta' ? 'கேட்கிறது: ' : 'Listening: ',
                    transcript
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 230,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-2",
                children: [
                    isListening && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-1 text-red-600",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-2 h-2 bg-red-600 rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 239,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs",
                                children: language === 'ta' ? 'கேட்கிறது' : 'Listening'
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 240,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                        lineNumber: 238,
                        columnNumber: 11
                    }, this),
                    isSpeaking && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-1 text-blue-600",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-2 h-2 bg-blue-600 rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 247,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-xs",
                                children: language === 'ta' ? 'பேசுகிறது' : 'Speaking'
                            }, void 0, false, {
                                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                                lineNumber: 248,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
                lineNumber: 236,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/chatbot/VoiceChat.tsx",
        lineNumber: 161,
        columnNumber: 5
    }, this);
}
_s(VoiceChat, "FfEV2Iqjw7hhVpuysK1Awdxebtc=");
_c = VoiceChat;
var _c;
__turbopack_context__.k.register(_c, "VoiceChat");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/avatar/HumanAvatar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>HumanAvatar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$cf57b220$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__D__as__useFrame$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-cf57b220.esm.js [app-client] (ecmascript) <export D as useFrame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/OrbitControls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/shapes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three/build/three.core.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
// Advanced Realistic Human Head Component
function RealisticHumanHead(param) {
    let { isListening, isSpeaking, emotion, lipSyncData, facialData } = param;
    _s();
    // Head and facial feature refs
    const headRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const faceRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const leftEyeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const rightEyeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const leftEyeballRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const rightEyeballRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const leftEyelidRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const rightEyelidRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mouthRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const upperLipRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const lowerLipRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const teethRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const tongueRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const leftEyebrowRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const rightEyebrowRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const noseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const leftCheekRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const rightCheekRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const jawRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Animation states
    const [blinkTimer, setBlinkTimer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [mouthAnimation, setMouthAnimation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [eyeMovement, setEyeMovement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0
    });
    const [headMovement, setHeadMovement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0,
        z: 0
    });
    const [breathingPhase, setBreathingPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Advanced Animation Loop
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$cf57b220$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__D__as__useFrame$3e$__["useFrame"])({
        "RealisticHumanHead.useFrame": (state, delta)=>{
            if (!headRef.current) return;
            const time = state.clock.elapsedTime;
            // Natural breathing animation
            setBreathingPhase({
                "RealisticHumanHead.useFrame": (prev)=>prev + delta * 0.3
            }["RealisticHumanHead.useFrame"]);
            const breathingIntensity = 1 + Math.sin(breathingPhase) * 0.015;
            if (headRef.current) {
                headRef.current.scale.setScalar(breathingIntensity);
            }
            // Realistic blinking system
            setBlinkTimer({
                "RealisticHumanHead.useFrame": (prev)=>prev + delta
            }["RealisticHumanHead.useFrame"]);
            if (blinkTimer > 2 + Math.random() * 3) {
                setBlinkTimer(0);
                // Smooth eyelid animation
                if (leftEyelidRef.current && rightEyelidRef.current) {
                    const blinkDuration = 0.15;
                    const blinkProgress = Math.min(time % blinkDuration / blinkDuration, 1);
                    const blinkValue = Math.sin(blinkProgress * Math.PI);
                    leftEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9);
                    rightEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9);
                }
            }
            // Advanced lip-sync animation
            if (isSpeaking && lipSyncData) {
                // Upper and lower lip movement based on phonemes
                if (upperLipRef.current && lowerLipRef.current) {
                    const mouthOpen = lipSyncData.mouthOpenness * 0.8;
                    const mouthWidth = lipSyncData.mouthWidth * 0.6;
                    upperLipRef.current.position.y = 0.1 + mouthOpen * 0.1;
                    lowerLipRef.current.position.y = -0.1 - mouthOpen * 0.15;
                    upperLipRef.current.scale.x = 1 + mouthWidth * 0.3;
                    lowerLipRef.current.scale.x = 1 + mouthWidth * 0.3;
                    // Jaw movement
                    if (jawRef.current) {
                        jawRef.current.position.y = -mouthOpen * 0.1;
                        jawRef.current.rotation.x = mouthOpen * 0.2;
                    }
                    // Tongue visibility for certain phonemes
                    if (tongueRef.current) {
                        const showTongue = [
                            'L',
                            'TH',
                            'R'
                        ].includes(lipSyncData.phoneme);
                        tongueRef.current.visible = showTongue;
                        if (showTongue) {
                            tongueRef.current.position.z = 0.05 + mouthOpen * 0.1;
                        }
                    }
                }
            } else {
                // Return to neutral position
                if (upperLipRef.current && lowerLipRef.current && jawRef.current) {
                    upperLipRef.current.position.y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(upperLipRef.current.position.y, 0.1, delta * 8);
                    lowerLipRef.current.position.y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(lowerLipRef.current.position.y, -0.1, delta * 8);
                    jawRef.current.position.y = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(jawRef.current.position.y, 0, delta * 8);
                    jawRef.current.rotation.x = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(jawRef.current.rotation.x, 0, delta * 8);
                    if (tongueRef.current) {
                        tongueRef.current.visible = false;
                    }
                }
            }
            // Facial expression animation based on facialData
            if (facialData) {
                // Eyebrow movement
                if (leftEyebrowRef.current && rightEyebrowRef.current) {
                    leftEyebrowRef.current.position.y = 0.4 + facialData.eyebrowLeft * 0.1;
                    rightEyebrowRef.current.position.y = 0.4 + facialData.eyebrowRight * 0.1;
                    leftEyebrowRef.current.rotation.z = facialData.eyebrowLeft * 0.2;
                    rightEyebrowRef.current.rotation.z = -facialData.eyebrowRight * 0.2;
                }
                // Cheek movement for smiling/expressions
                if (leftCheekRef.current && rightCheekRef.current) {
                    const cheekRaise = facialData.cheekPuff;
                    leftCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2);
                    rightCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2);
                    leftCheekRef.current.position.y = -0.1 + cheekRaise * 0.05;
                    rightCheekRef.current.position.y = -0.1 + cheekRaise * 0.05;
                }
            }
            // Natural eye movement and gaze
            setEyeMovement({
                "RealisticHumanHead.useFrame": (prev)=>({
                        x: prev.x + (Math.random() - 0.5) * 0.001,
                        y: prev.y + (Math.random() - 0.5) * 0.001
                    })
            }["RealisticHumanHead.useFrame"]);
            if (leftEyeballRef.current && rightEyeballRef.current) {
                const gazeX = Math.sin(time * 0.3) * 0.1 + eyeMovement.x;
                const gazeY = Math.cos(time * 0.4) * 0.05 + eyeMovement.y;
                leftEyeballRef.current.position.x = gazeX;
                leftEyeballRef.current.position.y = gazeY;
                rightEyeballRef.current.position.x = gazeX;
                rightEyeballRef.current.position.y = gazeY;
            }
            // Listening animation - subtle head movement
            if (isListening) {
                setHeadMovement({
                    "RealisticHumanHead.useFrame": (prev)=>({
                            x: Math.sin(time * 1.2) * 0.05,
                            y: Math.cos(time * 0.8) * 0.03,
                            z: Math.sin(time * 1.5) * 0.08
                        })
                }["RealisticHumanHead.useFrame"]);
            } else {
                setHeadMovement({
                    "RealisticHumanHead.useFrame": (prev)=>({
                            x: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(prev.x, Math.sin(time * 0.5) * 0.02, delta * 2),
                            y: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(prev.y, Math.cos(time * 0.3) * 0.01, delta * 2),
                            z: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MathUtils"].lerp(prev.z, 0, delta * 3)
                        })
                }["RealisticHumanHead.useFrame"]);
            }
            if (headRef.current) {
                headRef.current.rotation.x = headMovement.x;
                headRef.current.rotation.y = headMovement.y;
                headRef.current.rotation.z = headMovement.z;
            }
        }
    }["RealisticHumanHead.useFrame"]);
    // Emotion-based expressions
    const getEmotionColors = ()=>{
        switch(emotion){
            case 'happy':
                return {
                    skin: '#FFE4C4',
                    cheek: '#FFB6C1'
                };
            case 'sad':
                return {
                    skin: '#F5DEB3',
                    cheek: '#D3D3D3'
                };
            case 'concerned':
                return {
                    skin: '#FAEBD7',
                    cheek: '#DDA0DD'
                };
            case 'empathetic':
                return {
                    skin: '#FFF8DC',
                    cheek: '#F0E68C'
                };
            default:
                return {
                    skin: '#FDBCB4',
                    cheek: '#FFB6C1'
                };
        }
    };
    const colors = getEmotionColors();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
        ref: headRef,
        position: [
            0,
            0,
            0
        ],
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                ref: faceRef,
                args: [
                    1.2,
                    64,
                    64
                ],
                position: [
                    0,
                    0,
                    0
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: colors.skin,
                    roughness: 0.8,
                    metalness: 0.1
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 214,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 213,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                ref: leftEyeRef,
                position: [
                    -0.35,
                    0.25,
                    0.85
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.18,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            -0.05
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 224,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: leftEyeballRef,
                        args: [
                            0.15,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#FFFFFF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 228,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.08,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0.12
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#4A90E2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 233,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.04,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0.13
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#000000"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 237,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 236,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: leftEyelidRef,
                        args: [
                            0.16,
                            32,
                            16
                        ],
                        position: [
                            0,
                            0.05,
                            0.1
                        ],
                        scale: [
                            1,
                            1,
                            0.3
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 241,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 222,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                ref: rightEyeRef,
                position: [
                    0.35,
                    0.25,
                    0.85
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.18,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            -0.05
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 248,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 247,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: rightEyeballRef,
                        args: [
                            0.15,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#FFFFFF"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 252,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 251,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.08,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0.12
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#4A90E2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 256,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 255,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.04,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0.13
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#000000"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 260,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 259,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: rightEyelidRef,
                        args: [
                            0.16,
                            32,
                            16
                        ],
                        position: [
                            0,
                            0.05,
                            0.1
                        ],
                        scale: [
                            1,
                            1,
                            0.3
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 264,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 263,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 245,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                ref: leftEyebrowRef,
                args: [
                    0.35,
                    0.08,
                    0.12
                ],
                position: [
                    -0.35,
                    0.45,
                    0.8
                ],
                rotation: [
                    0,
                    0,
                    emotion === 'concerned' ? 0.4 : emotion === 'happy' ? -0.1 : 0
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: "#654321",
                    roughness: 0.9
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 275,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 269,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                ref: rightEyebrowRef,
                args: [
                    0.35,
                    0.08,
                    0.12
                ],
                position: [
                    0.35,
                    0.45,
                    0.8
                ],
                rotation: [
                    0,
                    0,
                    emotion === 'concerned' ? -0.4 : emotion === 'happy' ? 0.1 : 0
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: "#654321",
                    roughness: 0.9
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 283,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                ref: noseRef,
                position: [
                    0,
                    0.05,
                    0.95
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                        args: [
                            0.08,
                            0.3,
                            0.15
                        ],
                        position: [
                            0,
                            0.1,
                            0
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 290,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 289,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.06,
                            16,
                            16
                        ],
                        position: [
                            0,
                            -0.05,
                            0.05
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: colors.skin
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 293,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.02,
                            8,
                            8
                        ],
                        position: [
                            -0.03,
                            -0.08,
                            0.02
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#8B4513"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 298,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 297,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            0.02,
                            8,
                            8
                        ],
                        position: [
                            0.03,
                            -0.08,
                            0.02
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#8B4513"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 301,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 300,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                ref: mouthRef,
                position: [
                    0,
                    -0.25,
                    0.85
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: upperLipRef,
                        args: [
                            0.25,
                            32,
                            16
                        ],
                        position: [
                            0,
                            0.1,
                            0
                        ],
                        scale: [
                            1,
                            0.4,
                            0.8
                        ],
                        rotation: [
                            0,
                            0,
                            emotion === 'happy' ? 0.2 : emotion === 'sad' ? -0.2 : 0
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#CD5C5C"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 315,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 308,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: lowerLipRef,
                        args: [
                            0.28,
                            32,
                            16
                        ],
                        position: [
                            0,
                            -0.1,
                            0
                        ],
                        scale: [
                            1,
                            0.5,
                            0.8
                        ],
                        rotation: [
                            0,
                            0,
                            emotion === 'happy' ? 0.15 : emotion === 'sad' ? -0.15 : 0
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#B22222"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 326,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 319,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                        ref: teethRef,
                        args: [
                            0.3,
                            0.08,
                            0.05
                        ],
                        position: [
                            0,
                            0,
                            0.05
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#FFFACD"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 331,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 330,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        ref: tongueRef,
                        args: [
                            0.15,
                            16,
                            16
                        ],
                        position: [
                            0,
                            -0.05,
                            0
                        ],
                        scale: [
                            1,
                            0.6,
                            1.5
                        ],
                        visible: false,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#FF69B4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 342,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 335,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 306,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                ref: jawRef,
                args: [
                    0.8,
                    32,
                    32
                ],
                position: [
                    0,
                    -0.6,
                    0.3
                ],
                scale: [
                    1.2,
                    0.8,
                    1
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: colors.skin
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 348,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 347,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                ref: leftCheekRef,
                args: [
                    0.15,
                    32,
                    32
                ],
                position: [
                    -0.7,
                    -0.1,
                    0.6
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin,
                    transparent: true,
                    opacity: emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 353,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 352,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                ref: rightCheekRef,
                args: [
                    0.15,
                    32,
                    32
                ],
                position: [
                    0.7,
                    -0.1,
                    0.6
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin,
                    transparent: true,
                    opacity: emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 360,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 359,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                position: [
                    0,
                    0.4,
                    -0.1
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                        args: [
                            1.35,
                            32,
                            32
                        ],
                        position: [
                            0,
                            0,
                            0
                        ],
                        scale: [
                            1,
                            1.2,
                            0.8
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#654321",
                            roughness: 0.8
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 370,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 369,
                        columnNumber: 9
                    }, this),
                    Array.from({
                        length: 20
                    }, (_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cylinder"], {
                            args: [
                                0.01,
                                0.01,
                                0.3
                            ],
                            position: [
                                (Math.random() - 0.5) * 2,
                                Math.random() * 0.5,
                                (Math.random() - 0.5) * 1.5
                            ],
                            rotation: [
                                Math.random() * 0.5,
                                Math.random() * Math.PI * 2,
                                Math.random() * 0.5
                            ],
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                                color: "#543A2F"
                            }, void 0, false, {
                                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                                lineNumber: 388,
                                columnNumber: 13
                            }, this)
                        }, i, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 374,
                            columnNumber: 11
                        }, this))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 368,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cylinder"], {
                args: [
                    0.25,
                    0.3,
                    0.8
                ],
                position: [
                    0,
                    -1.2,
                    0
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: colors.skin
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 395,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 394,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("group", {
                position: [
                    0,
                    -1.8,
                    0
                ],
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                        args: [
                            2.2,
                            0.4,
                            0.8
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#4A90E2"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 401,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 400,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Box"], {
                        args: [
                            0.6,
                            0.1,
                            0.1
                        ],
                        position: [
                            0,
                            0.2,
                            0.4
                        ],
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                            color: "#2C5282"
                        }, void 0, false, {
                            fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                            lineNumber: 405,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 404,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 399,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
        lineNumber: 211,
        columnNumber: 5
    }, this);
}
_s(RealisticHumanHead, "C0EAdqBZt1UZyksZ8sl6cTK9k/U=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$cf57b220$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__D__as__useFrame$3e$__["useFrame"]
    ];
});
_c = RealisticHumanHead;
// Advanced Lighting Environment for Realistic Rendering
function Environment() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ambientLight", {
                intensity: 0.4,
                color: "#ffffff"
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 417,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("directionalLight", {
                position: [
                    5,
                    8,
                    5
                ],
                intensity: 1.2,
                color: "#ffffff",
                castShadow: true,
                "shadow-mapSize-width": 2048,
                "shadow-mapSize-height": 2048,
                "shadow-camera-far": 50,
                "shadow-camera-left": -10,
                "shadow-camera-right": 10,
                "shadow-camera-top": 10,
                "shadow-camera-bottom": -10
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 420,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("directionalLight", {
                position: [
                    -3,
                    4,
                    2
                ],
                intensity: 0.6,
                color: "#E6F3FF"
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 435,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                position: [
                    0,
                    2,
                    -3
                ],
                intensity: 0.8,
                color: "#FFE4B5"
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 442,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("spotLight", {
                position: [
                    0,
                    1,
                    4
                ],
                angle: 0.3,
                penumbra: 0.5,
                intensity: 0.5,
                color: "#FFFACD",
                "target-position": [
                    0,
                    0,
                    0
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 449,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
                args: [
                    50
                ],
                position: [
                    0,
                    0,
                    -25
                ],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshBasicMaterial", {
                    color: "#E8F4FD",
                    side: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BackSide"]
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 460,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 459,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("mesh", {
                rotation: [
                    -Math.PI / 2,
                    0,
                    0
                ],
                position: [
                    0,
                    -3,
                    0
                ],
                receiveShadow: true,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("planeGeometry", {
                        args: [
                            20,
                            20
                        ]
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 468,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                        color: "#F0F8FF",
                        transparent: true,
                        opacity: 0.3,
                        roughness: 0.1,
                        metalness: 0.1
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 469,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 467,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_c1 = Environment;
function HumanAvatar(param) {
    let { isListening, isSpeaking, emotion, message, lipSyncData, facialData } = param;
    _s1();
    const [cameraPosition, setCameraPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        0,
        0,
        4.5
    ]);
    const [currentLipSync, setCurrentLipSync] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        mouthOpenness: 0,
        mouthWidth: 0,
        phoneme: 'silence'
    });
    const [currentFacialData, setCurrentFacialData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        eyeBlinkLeft: 1,
        eyeBlinkRight: 1,
        eyebrowLeft: 0,
        eyebrowRight: 0,
        cheekPuff: 0,
        jawOpen: 0
    });
    // Update lip sync data when speaking
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HumanAvatar.useEffect": ()=>{
            if (isSpeaking && message) {
                // Simple phoneme extraction from message
                const words = message.split(' ');
                let phonemeIndex = 0;
                const animateLipSync = {
                    "HumanAvatar.useEffect.animateLipSync": ()=>{
                        if (phonemeIndex < message.length && isSpeaking) {
                            const char = message[phonemeIndex].toLowerCase();
                            let phoneme = 'silence';
                            let mouthOpenness = 0.2;
                            let mouthWidth = 0.5;
                            // Enhanced phoneme mapping
                            if ('aeiou'.includes(char)) {
                                phoneme = char.toUpperCase();
                                mouthOpenness = char === 'a' ? 0.8 : char === 'o' ? 0.9 : char === 'i' ? 0.3 : 0.6;
                                mouthWidth = char === 'i' ? 0.9 : char === 'o' ? 0.4 : 0.6;
                            } else if ('mbp'.includes(char)) {
                                phoneme = char.toUpperCase();
                                mouthOpenness = 0.1;
                                mouthWidth = 0.5;
                            } else if ('fv'.includes(char)) {
                                phoneme = char.toUpperCase();
                                mouthOpenness = 0.3;
                                mouthWidth = 0.7;
                            } else if ('sz'.includes(char)) {
                                phoneme = 'S';
                                mouthOpenness = 0.2;
                                mouthWidth = 0.6;
                            } else if ('rl'.includes(char)) {
                                phoneme = char.toUpperCase();
                                mouthOpenness = 0.4;
                                mouthWidth = 0.6;
                            }
                            setCurrentLipSync({
                                mouthOpenness,
                                mouthWidth,
                                phoneme
                            });
                            phonemeIndex++;
                            setTimeout(animateLipSync, 80 + Math.random() * 40); // Variable speed
                        } else {
                            setCurrentLipSync({
                                mouthOpenness: 0.1,
                                mouthWidth: 0.5,
                                phoneme: 'silence'
                            });
                        }
                    }
                }["HumanAvatar.useEffect.animateLipSync"];
                animateLipSync();
            } else {
                setCurrentLipSync({
                    mouthOpenness: 0.1,
                    mouthWidth: 0.5,
                    phoneme: 'silence'
                });
            }
        }
    }["HumanAvatar.useEffect"], [
        isSpeaking,
        message
    ]);
    // Update facial expressions based on emotion
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HumanAvatar.useEffect": ()=>{
            const emotionMappings = {
                happy: {
                    eyebrowLeft: 0.2,
                    eyebrowRight: 0.2,
                    cheekPuff: 0.4
                },
                sad: {
                    eyebrowLeft: -0.3,
                    eyebrowRight: -0.3,
                    cheekPuff: 0
                },
                concerned: {
                    eyebrowLeft: -0.2,
                    eyebrowRight: 0.1,
                    cheekPuff: 0
                },
                empathetic: {
                    eyebrowLeft: 0.1,
                    eyebrowRight: 0.1,
                    cheekPuff: 0.2
                },
                neutral: {
                    eyebrowLeft: 0,
                    eyebrowRight: 0,
                    cheekPuff: 0
                }
            };
            const mapping = emotionMappings[emotion];
            setCurrentFacialData({
                "HumanAvatar.useEffect": (prev)=>({
                        ...prev,
                        ...mapping
                    })
            }["HumanAvatar.useEffect"]);
        }
    }["HumanAvatar.useEffect"], [
        emotion
    ]);
    // Dynamic camera positioning
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HumanAvatar.useEffect": ()=>{
            if (isListening) {
                setCameraPosition([
                    0.2,
                    0.1,
                    3.8
                ]); // Slight angle when listening
            } else if (isSpeaking) {
                setCameraPosition([
                    0,
                    0,
                    4.2
                ]); // Centered when speaking
            } else {
                setCameraPosition([
                    0,
                    0,
                    4.5
                ]); // Default position
            }
        }
    }["HumanAvatar.useEffect"], [
        isListening,
        isSpeaking
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-96 relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Canvas"], {
                camera: {
                    position: cameraPosition,
                    fov: 45,
                    near: 0.1,
                    far: 1000
                },
                shadows: true,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Environment, {}, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 596,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(RealisticHumanHead, {
                        isListening: isListening,
                        isSpeaking: isSpeaking,
                        emotion: emotion,
                        lipSyncData: lipSyncData || currentLipSync,
                        facialData: facialData || currentFacialData
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 597,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OrbitControls"], {
                        enableZoom: true,
                        enablePan: false,
                        maxDistance: 8,
                        minDistance: 3,
                        maxPolarAngle: Math.PI / 1.8,
                        minPolarAngle: Math.PI / 4,
                        enableDamping: true,
                        dampingFactor: 0.05
                    }, void 0, false, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 604,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 587,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-4 left-4 space-y-2",
                children: [
                    isListening && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-2 h-2 bg-white rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                                lineNumber: 620,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Listening..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                                lineNumber: 621,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 619,
                        columnNumber: 11
                    }, this),
                    isSpeaking && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2 bg-blue-500 text-white px-3 py-1 rounded-full text-sm",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-2 h-2 bg-white rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                                lineNumber: 626,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Speaking..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                                lineNumber: 627,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                        lineNumber: 625,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 617,
                columnNumber: 7
            }, this),
            message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-4 left-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-gray-800",
                    children: message
                }, void 0, false, {
                    fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                    lineNumber: 635,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
                lineNumber: 634,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/avatar/HumanAvatar.tsx",
        lineNumber: 586,
        columnNumber: 5
    }, this);
}
_s1(HumanAvatar, "W595nhCBIhNnGWO0WomqaLbSCP8=");
_c2 = HumanAvatar;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "RealisticHumanHead");
__turbopack_context__.k.register(_c1, "Environment");
__turbopack_context__.k.register(_c2, "HumanAvatar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/avatar/FacialAnimationEngine.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>FacialAnimationEngine,
    "detectEmotionFromText": ()=>detectEmotionFromText,
    "textToPhonemes": ()=>textToPhonemes
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
// Advanced Phoneme to mouth shape mapping for realistic lip-sync
const PHONEME_MOUTH_SHAPES = {
    'A': {
        openness: 0.85,
        width: 0.65,
        lipRounding: 0.2,
        tonguePosition: 0.1
    },
    'E': {
        openness: 0.55,
        width: 0.85,
        lipRounding: 0.1,
        tonguePosition: 0.3
    },
    'I': {
        openness: 0.25,
        width: 0.95,
        lipRounding: 0.0,
        tonguePosition: 0.8
    },
    'O': {
        openness: 0.9,
        width: 0.35,
        lipRounding: 0.9,
        tonguePosition: 0.2
    },
    'U': {
        openness: 0.65,
        width: 0.25,
        lipRounding: 0.95,
        tonguePosition: 0.1
    },
    'M': {
        openness: 0.05,
        width: 0.5,
        lipRounding: 0.3,
        tonguePosition: 0.0
    },
    'B': {
        openness: 0.08,
        width: 0.5,
        lipRounding: 0.2,
        tonguePosition: 0.0
    },
    'P': {
        openness: 0.02,
        width: 0.45,
        lipRounding: 0.4,
        tonguePosition: 0.0
    },
    'F': {
        openness: 0.35,
        width: 0.7,
        lipRounding: 0.1,
        tonguePosition: 0.2
    },
    'V': {
        openness: 0.32,
        width: 0.72,
        lipRounding: 0.1,
        tonguePosition: 0.2
    },
    'TH': {
        openness: 0.25,
        width: 0.8,
        lipRounding: 0.0,
        tonguePosition: 0.9
    },
    'S': {
        openness: 0.18,
        width: 0.65,
        lipRounding: 0.0,
        tonguePosition: 0.7
    },
    'SH': {
        openness: 0.4,
        width: 0.5,
        lipRounding: 0.6,
        tonguePosition: 0.5
    },
    'R': {
        openness: 0.45,
        width: 0.6,
        lipRounding: 0.3,
        tonguePosition: 0.6
    },
    'L': {
        openness: 0.35,
        width: 0.75,
        lipRounding: 0.1,
        tonguePosition: 0.8
    },
    'T': {
        openness: 0.2,
        width: 0.6,
        lipRounding: 0.0,
        tonguePosition: 0.9
    },
    'D': {
        openness: 0.25,
        width: 0.65,
        lipRounding: 0.0,
        tonguePosition: 0.85
    },
    'N': {
        openness: 0.15,
        width: 0.6,
        lipRounding: 0.0,
        tonguePosition: 0.8
    },
    'K': {
        openness: 0.3,
        width: 0.5,
        lipRounding: 0.0,
        tonguePosition: 0.3
    },
    'G': {
        openness: 0.35,
        width: 0.55,
        lipRounding: 0.0,
        tonguePosition: 0.3
    },
    'silence': {
        openness: 0.08,
        width: 0.5,
        lipRounding: 0.2,
        tonguePosition: 0.0
    }
};
// Advanced emotion-based facial expressions with micro-expressions
const EMOTION_EXPRESSIONS = {
    neutral: {
        eyebrowHeight: 0,
        eyebrowAngle: 0,
        mouthCurve: 0,
        mouthCorners: 0,
        eyeOpenness: 1,
        eyeSquint: 0,
        cheekRaise: 0,
        nostrilFlare: 0,
        jawTension: 0,
        foreheadWrinkle: 0,
        lipTension: 0
    },
    happy: {
        eyebrowHeight: 0.15,
        eyebrowAngle: 0.1,
        mouthCurve: 0.7,
        mouthCorners: 0.8,
        eyeOpenness: 0.85,
        eyeSquint: 0.3,
        cheekRaise: 0.6,
        nostrilFlare: 0.1,
        jawTension: 0,
        foreheadWrinkle: 0,
        lipTension: 0.2
    },
    sad: {
        eyebrowHeight: -0.4,
        eyebrowAngle: -0.3,
        mouthCurve: -0.5,
        mouthCorners: -0.6,
        eyeOpenness: 0.7,
        eyeSquint: 0.1,
        cheekRaise: -0.2,
        nostrilFlare: 0,
        jawTension: 0.2,
        foreheadWrinkle: 0.4,
        lipTension: 0.3
    },
    concerned: {
        eyebrowHeight: -0.25,
        eyebrowAngle: -0.4,
        mouthCurve: -0.15,
        mouthCorners: -0.2,
        eyeOpenness: 1.1,
        eyeSquint: 0,
        cheekRaise: 0,
        nostrilFlare: 0.2,
        jawTension: 0.3,
        foreheadWrinkle: 0.6,
        lipTension: 0.4
    },
    empathetic: {
        eyebrowHeight: 0.1,
        eyebrowAngle: 0.2,
        mouthCurve: 0.3,
        mouthCorners: 0.4,
        eyeOpenness: 0.95,
        eyeSquint: 0.1,
        cheekRaise: 0.3,
        nostrilFlare: 0,
        jawTension: 0,
        foreheadWrinkle: 0.1,
        lipTension: 0.1
    }
};
function FacialAnimationEngine(param) {
    let { audioData, text, emotion, isActive, onAnimationUpdate } = param;
    _s();
    const [currentPhoneme, setCurrentPhoneme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('silence');
    const [blinkTimer, setBlinkTimer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [emotionIntensity, setEmotionIntensity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const animationFrameRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const analyserRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    // Advanced phoneme detection from text with context awareness
    const detectPhonemeFromText = (text, position)=>{
        if (!text || position >= text.length) return 'silence';
        const char = text[position].toLowerCase();
        const nextChar = position + 1 < text.length ? text[position + 1].toLowerCase() : '';
        const prevChar = position > 0 ? text[position - 1].toLowerCase() : '';
        // Advanced phoneme mapping with digraphs and context
        const phonemeMap = {
            // Vowels
            'a': 'A',
            'e': 'E',
            'i': 'I',
            'o': 'O',
            'u': 'U',
            // Consonants - Bilabials
            'm': 'M',
            'b': 'B',
            'p': 'P',
            // Labiodentals
            'f': 'F',
            'v': 'V',
            // Alveolars
            't': 'T',
            'd': 'D',
            'n': 'N',
            's': 'S',
            'z': 'S',
            'l': 'L',
            'r': 'R',
            // Velars
            'k': 'K',
            'g': 'G',
            // Special cases
            'c': nextChar === 'h' ? 'SH' : 'K',
            'j': 'SH',
            'w': 'U',
            'y': 'I',
            'q': 'K',
            'x': 'S',
            // Silence
            ' ': 'silence',
            '.': 'silence',
            ',': 'silence',
            '!': 'silence',
            '?': 'silence'
        };
        // Handle digraphs
        if (char === 't' && nextChar === 'h') return 'TH';
        if (char === 's' && nextChar === 'h') return 'SH';
        if (char === 'c' && nextChar === 'h') return 'SH';
        if (char === 'p' && nextChar === 'h') return 'F';
        if (char === 'g' && nextChar === 'h') return 'G';
        // Handle silent letters
        if (char === 'h' && prevChar !== '') return 'silence';
        if (char === 'w' && prevChar === 's') return 'silence';
        if (char === 'b' && prevChar === 'm') return 'silence';
        if (char === 'l' && prevChar === 'a' && nextChar === 'k') return 'silence';
        return phonemeMap[char] || 'silence';
    };
    // Audio analysis for lip-sync
    const analyzeAudio = (audioData)=>{
        if (!audioData) return 0;
        let sum = 0;
        for(let i = 0; i < audioData.length; i++){
            sum += Math.abs(audioData[i]);
        }
        return sum / audioData.length;
    };
    // Generate realistic blinking
    const generateBlink = (deltaTime)=>{
        setBlinkTimer((prev)=>prev + deltaTime);
        // Random blinking every 2-4 seconds
        const blinkInterval = 2000 + Math.random() * 2000;
        if (blinkTimer > blinkInterval) {
            setBlinkTimer(0);
            // Quick blink animation
            const blinkPhase = Date.now() % 200 / 200;
            const blinkValue = Math.sin(blinkPhase * Math.PI);
            return {
                left: blinkValue,
                right: blinkValue
            };
        }
        return {
            left: 1,
            right: 1
        };
    };
    // Main animation loop
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FacialAnimationEngine.useEffect": ()=>{
            if (!isActive) return;
            let textPosition = 0;
            let lastTime = Date.now();
            const animate = {
                "FacialAnimationEngine.useEffect.animate": ()=>{
                    const currentTime = Date.now();
                    const deltaTime = currentTime - lastTime;
                    lastTime = currentTime;
                    // Text-based lip-sync animation
                    if (text && textPosition < text.length) {
                        const phoneme = detectPhonemeFromText(text, Math.floor(textPosition));
                        setCurrentPhoneme(phoneme);
                        // Advance through text at speaking pace (adjust speed as needed)
                        textPosition += deltaTime * 0.01; // Adjust this multiplier for speaking speed
                    } else {
                        setCurrentPhoneme('silence');
                    }
                    // Audio-based animation (if available)
                    let audioIntensity = 0;
                    if (audioData) {
                        audioIntensity = analyzeAudio(audioData);
                    }
                    // Generate advanced facial animation data
                    const mouthShape = PHONEME_MOUTH_SHAPES[currentPhoneme] || PHONEME_MOUTH_SHAPES.silence;
                    const expression = EMOTION_EXPRESSIONS[emotion];
                    const blink = generateBlink(deltaTime);
                    // Calculate micro-expressions and natural variations
                    const naturalVariation = {
                        eyebrow: Math.sin(currentTime * 0.002) * 0.02,
                        mouth: Math.cos(currentTime * 0.003) * 0.01,
                        eye: Math.sin(currentTime * 0.004) * 0.01
                    };
                    const animationData = {
                        mouthOpenness: Math.max(0, Math.min(1, mouthShape.openness + audioIntensity * 0.4 + expression.mouthCurve * 0.2 + naturalVariation.mouth)),
                        eyeBlinkLeft: blink.left * (1 + expression.eyeSquint * 0.3),
                        eyeBlinkRight: blink.right * (1 + expression.eyeSquint * 0.3),
                        eyebrowPosition: expression.eyebrowHeight + naturalVariation.eyebrow,
                        headRotation: {
                            x: Math.sin(currentTime * 0.0008) * 0.04 + expression.jawTension * 0.02,
                            y: Math.sin(currentTime * 0.0006) * 0.025 + audioIntensity * 0.05,
                            z: Math.sin(currentTime * 0.001) * 0.015 + expression.foreheadWrinkle * 0.01
                        },
                        emotion,
                        intensity: emotionIntensity * (1 + Math.sin(currentTime * 0.005) * 0.1)
                    };
                    // Add breathing influence on facial features
                    const breathingInfluence = Math.sin(currentTime * 0.0015) * 0.008;
                    animationData.mouthOpenness += breathingInfluence;
                    animationData.eyebrowPosition += breathingInfluence * 0.5;
                    onAnimationUpdate(animationData);
                    if (isActive) {
                        animationFrameRef.current = requestAnimationFrame(animate);
                    }
                }
            }["FacialAnimationEngine.useEffect.animate"];
            animate();
            return ({
                "FacialAnimationEngine.useEffect": ()=>{
                    if (animationFrameRef.current) {
                        cancelAnimationFrame(animationFrameRef.current);
                    }
                }
            })["FacialAnimationEngine.useEffect"];
        }
    }["FacialAnimationEngine.useEffect"], [
        isActive,
        text,
        emotion,
        audioData,
        onAnimationUpdate
    ]);
    // Setup audio analysis
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FacialAnimationEngine.useEffect": ()=>{
            if ("object" !== 'undefined' && window.AudioContext) {
                audioContextRef.current = new AudioContext();
                analyserRef.current = audioContextRef.current.createAnalyser();
                analyserRef.current.fftSize = 256;
            }
            return ({
                "FacialAnimationEngine.useEffect": ()=>{
                    if (audioContextRef.current) {
                        audioContextRef.current.close();
                    }
                }
            })["FacialAnimationEngine.useEffect"];
        }
    }["FacialAnimationEngine.useEffect"], []);
    return null // This is a logic-only component
    ;
}
_s(FacialAnimationEngine, "HZcOOZ6r+042zEExeQZOTaQtFOQ=");
_c = FacialAnimationEngine;
function textToPhonemes(text) {
    const words = text.split(' ');
    const phonemes = [];
    words.forEach((word, wordIndex)=>{
        for(let i = 0; i < word.length; i++){
            const char = word[i].toLowerCase();
            let phoneme = 'silence';
            // Simple character to phoneme mapping
            if ('aeiou'.includes(char)) {
                phoneme = char.toUpperCase();
            } else if ('mbp'.includes(char)) {
                phoneme = char.toUpperCase();
            } else if ('fv'.includes(char)) {
                phoneme = char.toUpperCase();
            } else if ('sz'.includes(char)) {
                phoneme = 'S';
            } else if ('rl'.includes(char)) {
                phoneme = char.toUpperCase();
            }
            phonemes.push({
                phoneme,
                duration: 100 + Math.random() * 100 // Random duration between 100-200ms
            });
        }
        // Add pause between words
        if (wordIndex < words.length - 1) {
            phonemes.push({
                phoneme: 'silence',
                duration: 200
            });
        }
    });
    return phonemes;
}
function detectEmotionFromText(text) {
    const lowerText = text.toLowerCase();
    // Happy indicators
    if (lowerText.includes('happy') || lowerText.includes('joy') || lowerText.includes('great') || lowerText.includes('wonderful') || lowerText.includes('excited') || lowerText.includes('😊') || lowerText.includes('good') || lowerText.includes('amazing')) {
        return 'happy';
    }
    // Sad indicators
    if (lowerText.includes('sad') || lowerText.includes('depressed') || lowerText.includes('cry') || lowerText.includes('hurt') || lowerText.includes('pain') || lowerText.includes('😢') || lowerText.includes('terrible') || lowerText.includes('awful')) {
        return 'sad';
    }
    // Concerned indicators
    if (lowerText.includes('worried') || lowerText.includes('anxious') || lowerText.includes('scared') || lowerText.includes('afraid') || lowerText.includes('nervous') || lowerText.includes('stress') || lowerText.includes('concerned') || lowerText.includes('help')) {
        return 'concerned';
    }
    // Empathetic indicators
    if (lowerText.includes('understand') || lowerText.includes('support') || lowerText.includes('care') || lowerText.includes('here for you') || lowerText.includes('listen') || lowerText.includes('comfort')) {
        return 'empathetic';
    }
    return 'neutral';
}
var _c;
__turbopack_context__.k.register(_c, "FacialAnimationEngine");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/avatar/AudioAnalyzer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "audioToPhoneme": ()=>audioToPhoneme,
    "default": ()=>AudioAnalyzer,
    "generateLipSyncFromAudio": ()=>generateLipSyncFromAudio
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function AudioAnalyzer(param) {
    let { isActive, onAudioData } = param;
    _s();
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const analyserRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const microphoneRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const streamRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationFrameRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize audio context and microphone
    const initializeAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AudioAnalyzer.useCallback[initializeAudio]": async ()=>{
            try {
                if (!audioContextRef.current) {
                    audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
                }
                if (!analyserRef.current) {
                    analyserRef.current = audioContextRef.current.createAnalyser();
                    analyserRef.current.fftSize = 2048;
                    analyserRef.current.smoothingTimeConstant = 0.8;
                }
                // Get microphone access
                if (!streamRef.current) {
                    streamRef.current = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: 44100
                        }
                    });
                    microphoneRef.current = audioContextRef.current.createMediaStreamSource(streamRef.current);
                    microphoneRef.current.connect(analyserRef.current);
                }
                setIsInitialized(true);
            } catch (error) {
                console.error('Error initializing audio:', error);
            }
        }
    }["AudioAnalyzer.useCallback[initializeAudio]"], []);
    // Advanced audio analysis
    const analyzeAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AudioAnalyzer.useCallback[analyzeAudio]": ()=>{
            if (!analyserRef.current || !isActive) return;
            const bufferLength = analyserRef.current.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            const frequencyData = new Float32Array(bufferLength);
            analyserRef.current.getByteFrequencyData(dataArray);
            analyserRef.current.getFloatFrequencyData(frequencyData);
            // Calculate volume (RMS)
            let sum = 0;
            for(let i = 0; i < bufferLength; i++){
                sum += dataArray[i] * dataArray[i];
            }
            const volume = Math.sqrt(sum / bufferLength) / 255;
            // Find dominant frequency
            let maxIndex = 0;
            let maxValue = 0;
            for(let i = 0; i < bufferLength; i++){
                if (dataArray[i] > maxValue) {
                    maxValue = dataArray[i];
                    maxIndex = i;
                }
            }
            const frequency = maxIndex * audioContextRef.current.sampleRate / (2 * bufferLength);
            // Estimate formants (simplified)
            const formants = [];
            const formantRanges = [
                [
                    200,
                    1000
                ],
                [
                    800,
                    2500
                ],
                [
                    1500,
                    4000
                ] // F3
            ];
            for (const [low, high] of formantRanges){
                const startBin = Math.floor(low * bufferLength * 2 / audioContextRef.current.sampleRate);
                const endBin = Math.floor(high * bufferLength * 2 / audioContextRef.current.sampleRate);
                let maxFormantValue = 0;
                let maxFormantIndex = startBin;
                for(let i = startBin; i < Math.min(endBin, bufferLength); i++){
                    if (dataArray[i] > maxFormantValue) {
                        maxFormantValue = dataArray[i];
                        maxFormantIndex = i;
                    }
                }
                const formantFreq = maxFormantIndex * audioContextRef.current.sampleRate / (2 * bufferLength);
                formants.push(formantFreq);
            }
            // Simple pitch detection using autocorrelation
            const pitch = estimatePitch(dataArray, audioContextRef.current.sampleRate);
            // Voice activity detection
            const voiceActivity = volume > 0.01 && frequency > 80 && frequency < 1000;
            onAudioData({
                volume,
                frequency,
                formants,
                pitch,
                voiceActivity
            });
            if (isActive) {
                animationFrameRef.current = requestAnimationFrame(analyzeAudio);
            }
        }
    }["AudioAnalyzer.useCallback[analyzeAudio]"], [
        isActive,
        onAudioData
    ]);
    // Simple pitch estimation using autocorrelation
    const estimatePitch = (buffer, sampleRate)=>{
        const SIZE = buffer.length;
        const MAX_SAMPLES = Math.floor(SIZE / 2);
        let bestOffset = -1;
        let bestCorrelation = 0;
        let rms = 0;
        let foundGoodCorrelation = false;
        const correlations = new Array(MAX_SAMPLES);
        for(let i = 0; i < SIZE; i++){
            const val = buffer[i] - 128;
            rms += val * val;
        }
        rms = Math.sqrt(rms / SIZE);
        if (rms < 0.01) return -1;
        let lastCorrelation = 1;
        for(let offset = 1; offset < MAX_SAMPLES; offset++){
            let correlation = 0;
            for(let i = 0; i < MAX_SAMPLES; i++){
                correlation += Math.abs(buffer[i] - 128 - (buffer[i + offset] - 128));
            }
            correlation = 1 - correlation / MAX_SAMPLES;
            correlations[offset] = correlation;
            if (correlation > 0.9 && correlation > lastCorrelation) {
                foundGoodCorrelation = true;
                if (correlation > bestCorrelation) {
                    bestCorrelation = correlation;
                    bestOffset = offset;
                }
            } else if (foundGoodCorrelation) {
                const shift = (correlations[bestOffset + 1] - correlations[bestOffset - 1]) / correlations[bestOffset];
                return sampleRate / (bestOffset + 8 * shift);
            }
            lastCorrelation = correlation;
        }
        if (bestCorrelation > 0.01) {
            return sampleRate / bestOffset;
        }
        return -1;
    };
    // Start/stop audio analysis
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AudioAnalyzer.useEffect": ()=>{
            if (isActive && !isInitialized) {
                initializeAudio();
            }
            if (isActive && isInitialized) {
                analyzeAudio();
            }
            return ({
                "AudioAnalyzer.useEffect": ()=>{
                    if (animationFrameRef.current) {
                        cancelAnimationFrame(animationFrameRef.current);
                    }
                }
            })["AudioAnalyzer.useEffect"];
        }
    }["AudioAnalyzer.useEffect"], [
        isActive,
        isInitialized,
        initializeAudio,
        analyzeAudio
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AudioAnalyzer.useEffect": ()=>{
            return ({
                "AudioAnalyzer.useEffect": ()=>{
                    if (streamRef.current) {
                        streamRef.current.getTracks().forEach({
                            "AudioAnalyzer.useEffect": (track)=>track.stop()
                        }["AudioAnalyzer.useEffect"]);
                    }
                    if (audioContextRef.current) {
                        audioContextRef.current.close();
                    }
                }
            })["AudioAnalyzer.useEffect"];
        }
    }["AudioAnalyzer.useEffect"], []);
    return null // This is a logic-only component
    ;
}
_s(AudioAnalyzer, "+NylifJuY3N8fIryGkymqHoQSFc=");
_c = AudioAnalyzer;
function audioToPhoneme(audioData) {
    if (!audioData.voiceActivity || audioData.volume < 0.01) {
        return 'silence';
    }
    const [f1, f2, f3] = audioData.formants;
    // Simplified vowel classification based on formants
    if (f1 < 400 && f2 > 2000) return 'I';
    if (f1 < 500 && f2 < 1500) return 'U';
    if (f1 > 700 && f2 > 1500) return 'A';
    if (f1 > 500 && f2 > 1800) return 'E';
    if (f1 > 500 && f2 < 1200) return 'O';
    // Consonant classification based on frequency and volume patterns
    if (audioData.frequency > 2000 && audioData.volume > 0.3) return 'S';
    if (audioData.frequency < 200 && audioData.volume > 0.5) return 'M';
    if (audioData.frequency > 1000 && audioData.volume > 0.4) return 'F';
    return 'silence';
}
function generateLipSyncFromAudio(audioData) {
    const phoneme = audioToPhoneme(audioData);
    const baseShape = {
        mouthOpenness: 0.1,
        mouthWidth: 0.5,
        phoneme: 'silence'
    };
    if (!audioData.voiceActivity) {
        return baseShape;
    }
    // Map volume to mouth openness
    const volumeInfluence = Math.min(audioData.volume * 2, 1);
    // Map formants to mouth shape
    const [f1, f2] = audioData.formants;
    const mouthOpenness = Math.min(0.9, 0.2 + f1 / 1000 * 0.6 + volumeInfluence * 0.3);
    const mouthWidth = Math.min(0.9, 0.3 + f2 / 2500 * 0.6);
    return {
        mouthOpenness,
        mouthWidth,
        phoneme
    };
}
var _c;
__turbopack_context__.k.register(_c, "AudioAnalyzer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/avatar-chat/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>AvatarChatPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ExclamationTriangleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExclamationTriangleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js [app-client] (ecmascript) <export default as ExclamationTriangleIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chatbot$2f$VoiceChat$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/chatbot/VoiceChat.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$HumanAvatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/avatar/HumanAvatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$FacialAnimationEngine$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/avatar/FacialAnimationEngine.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$AudioAnalyzer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/avatar/AudioAnalyzer.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function AvatarChatPage() {
    _s();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [inputMessage, setInputMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isListening, setIsListening] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('en');
    const [speakResponse, setSpeakResponse] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Avatar states
    const [avatarEmotion, setAvatarEmotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('neutral');
    const [avatarIsSpeaking, setAvatarIsSpeaking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [avatarIsListening, setAvatarIsListening] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [currentSpeechText, setCurrentSpeechText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [facialAnimationData, setFacialAnimationData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [realTimeLipSync, setRealTimeLipSync] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        mouthOpenness: 0,
        mouthWidth: 0,
        phoneme: 'silence'
    });
    const [realTimeFacialData, setRealTimeFacialData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        eyeBlinkLeft: 1,
        eyeBlinkRight: 1,
        eyebrowLeft: 0,
        eyebrowRight: 0,
        cheekPuff: 0,
        jawOpen: 0
    });
    const [audioAnalysisActive, setAudioAnalysisActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const typingTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const speechTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    // Initialize with welcome message
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AvatarChatPage.useEffect": ()=>{
            const welcomeMessage = {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                role: 'assistant',
                content: language === 'ta' ? 'வணக்கம்! நான் உங்கள் AI மனநல ஆலோசகர். நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்?' : 'Hello! I\'m your AI mental health counselor. I\'m here to provide emotional support and guidance. How are you feeling today?',
                timestamp: new Date().toISOString(),
                language,
                emotion: 'empathetic'
            };
            setMessages([
                welcomeMessage
            ]);
            setAvatarEmotion('empathetic');
            // Auto-speak welcome message
            setTimeout({
                "AvatarChatPage.useEffect": ()=>{
                    if (speakResponse) {
                        speakResponse(welcomeMessage.content, language);
                        setAvatarIsSpeaking(true);
                        setCurrentSpeechText(welcomeMessage.content);
                        // Stop speaking after estimated duration
                        const estimatedDuration = welcomeMessage.content.length * 80 // ~80ms per character
                        ;
                        speechTimeoutRef.current = setTimeout({
                            "AvatarChatPage.useEffect": ()=>{
                                setAvatarIsSpeaking(false);
                                setCurrentSpeechText('');
                            }
                        }["AvatarChatPage.useEffect"], estimatedDuration);
                    }
                }
            }["AvatarChatPage.useEffect"], 1000);
        }
    }["AvatarChatPage.useEffect"], [
        language,
        speakResponse
    ]);
    const scrollToBottom = ()=>{
        var _messagesEndRef_current;
        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({
            behavior: 'smooth'
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AvatarChatPage.useEffect": ()=>{
            scrollToBottom();
        }
    }["AvatarChatPage.useEffect"], [
        messages
    ]);
    // Handle facial animation updates
    const handleFacialAnimationUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AvatarChatPage.useCallback[handleFacialAnimationUpdate]": (animationData)=>{
            setFacialAnimationData(animationData);
        }
    }["AvatarChatPage.useCallback[handleFacialAnimationUpdate]"], []);
    // Handle real-time audio analysis
    const handleAudioData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AvatarChatPage.useCallback[handleAudioData]": (audioData)=>{
            // Generate lip-sync data from audio
            const lipSyncData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$AudioAnalyzer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateLipSyncFromAudio"])(audioData);
            setRealTimeLipSync(lipSyncData);
            // Update facial expressions based on voice activity
            if (audioData.voiceActivity && audioData.volume > 0.02) {
                setRealTimeFacialData({
                    "AvatarChatPage.useCallback[handleAudioData]": (prev)=>({
                            ...prev,
                            jawOpen: Math.min(0.8, audioData.volume * 1.5),
                            cheekPuff: audioData.frequency > 1000 ? 0.2 : 0
                        })
                }["AvatarChatPage.useCallback[handleAudioData]"]);
            } else {
                setRealTimeFacialData({
                    "AvatarChatPage.useCallback[handleAudioData]": (prev)=>({
                            ...prev,
                            jawOpen: 0,
                            cheekPuff: 0
                        })
                }["AvatarChatPage.useCallback[handleAudioData]"]);
            }
        }
    }["AvatarChatPage.useCallback[handleAudioData]"], []);
    // Typing effect for assistant messages
    const typeMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AvatarChatPage.useCallback[typeMessage]": (message)=>{
            const words = message.content.split(' ');
            let currentIndex = 0;
            const typeNextWord = {
                "AvatarChatPage.useCallback[typeMessage].typeNextWord": ()=>{
                    if (currentIndex < words.length) {
                        const typedContent = words.slice(0, currentIndex + 1).join(' ');
                        setMessages({
                            "AvatarChatPage.useCallback[typeMessage].typeNextWord": (prev)=>prev.map({
                                    "AvatarChatPage.useCallback[typeMessage].typeNextWord": (msg)=>msg.id === message.id ? {
                                            ...msg,
                                            typedContent,
                                            isTyping: true
                                        } : msg
                                }["AvatarChatPage.useCallback[typeMessage].typeNextWord"])
                        }["AvatarChatPage.useCallback[typeMessage].typeNextWord"]);
                        currentIndex++;
                        typingTimeoutRef.current = setTimeout(typeNextWord, 150 + Math.random() * 100);
                    } else {
                        setMessages({
                            "AvatarChatPage.useCallback[typeMessage].typeNextWord": (prev)=>prev.map({
                                    "AvatarChatPage.useCallback[typeMessage].typeNextWord": (msg)=>msg.id === message.id ? {
                                            ...msg,
                                            isTyping: false,
                                            typedContent: message.content
                                        } : msg
                                }["AvatarChatPage.useCallback[typeMessage].typeNextWord"])
                        }["AvatarChatPage.useCallback[typeMessage].typeNextWord"]);
                        // Start avatar speaking
                        if (message.role === 'assistant') {
                            setAvatarIsSpeaking(true);
                            setCurrentSpeechText(message.content);
                            setAvatarEmotion(message.emotion || 'neutral');
                            // Auto-speak the response
                            if (speakResponse) {
                                setTimeout({
                                    "AvatarChatPage.useCallback[typeMessage].typeNextWord": ()=>{
                                        speakResponse(message.content, message.language || language);
                                    }
                                }["AvatarChatPage.useCallback[typeMessage].typeNextWord"], 500);
                            }
                            // Stop speaking after estimated duration
                            const estimatedDuration = message.content.length * 80;
                            speechTimeoutRef.current = setTimeout({
                                "AvatarChatPage.useCallback[typeMessage].typeNextWord": ()=>{
                                    setAvatarIsSpeaking(false);
                                    setCurrentSpeechText('');
                                    setAvatarEmotion('neutral');
                                }
                            }["AvatarChatPage.useCallback[typeMessage].typeNextWord"], estimatedDuration);
                        }
                    }
                }
            }["AvatarChatPage.useCallback[typeMessage].typeNextWord"];
            typeNextWord();
        }
    }["AvatarChatPage.useCallback[typeMessage]"], [
        speakResponse,
        language
    ]);
    // Handle voice transcript
    const handleVoiceTranscript = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AvatarChatPage.useCallback[handleVoiceTranscript]": (transcript)=>{
            setInputMessage(transcript);
            setAvatarIsListening(false);
            // Auto-send voice messages
            setTimeout({
                "AvatarChatPage.useCallback[handleVoiceTranscript]": ()=>{
                    sendMessage(transcript);
                }
            }["AvatarChatPage.useCallback[handleVoiceTranscript]"], 500);
        }
    }["AvatarChatPage.useCallback[handleVoiceTranscript]"], []);
    // Handle voice response setup
    const handleSpeakResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AvatarChatPage.useCallback[handleSpeakResponse]": (speakFn)=>{
            setSpeakResponse({
                "AvatarChatPage.useCallback[handleSpeakResponse]": ()=>speakFn
            }["AvatarChatPage.useCallback[handleSpeakResponse]"]);
        }
    }["AvatarChatPage.useCallback[handleSpeakResponse]"], []);
    // Update avatar listening state and audio analysis
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AvatarChatPage.useEffect": ()=>{
            setAvatarIsListening(isListening);
            setAudioAnalysisActive(isListening); // Activate audio analysis when listening
        }
    }["AvatarChatPage.useEffect"], [
        isListening
    ]);
    const sendMessage = async (messageText)=>{
        const textToSend = messageText || inputMessage.trim();
        if (!textToSend || isLoading) return;
        const userMessage = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
            role: 'user',
            content: textToSend,
            timestamp: new Date().toISOString(),
            language
        };
        setMessages((prev)=>[
                ...prev,
                userMessage
            ]);
        setInputMessage('');
        setIsLoading(true);
        // Show typing indicator
        const typingMessage = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
            role: 'assistant',
            content: language === 'ta' ? 'சிந்திக்கிறேன்...' : 'Thinking...',
            timestamp: new Date().toISOString(),
            isTyping: true,
            language,
            emotion: 'neutral'
        };
        setMessages((prev)=>[
                ...prev,
                typingMessage
            ]);
        setAvatarEmotion('concerned'); // Show thinking expression
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: textToSend,
                    sessionId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                    userId: 'anonymous',
                    language
                })
            });
            const data = await response.json();
            // Remove typing indicator
            setMessages((prev)=>prev.filter((msg)=>msg.id !== typingMessage.id));
            if (data.success) {
                let responseContent = data.data.content;
                // Detect emotion from response
                const detectedEmotion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$FacialAnimationEngine$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["detectEmotionFromText"])(responseContent);
                // Basic Tamil translation for common responses
                if (language === 'ta' && !responseContent.includes('தமிழ்')) {
                    const translations = {
                        'I understand': 'நான் புரிந்துகொள்கிறேன்',
                        'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',
                        'I\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',
                        'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',
                        'That sounds difficult': 'அது கடினமாக இருக்கும்',
                        'You\'re not alone': 'நீங்கள் தனியாக இல்லை',
                        'I\'m sorry to hear': 'கேட்டு வருந்துகிறேன்',
                        'That\'s wonderful': 'அது அருமை',
                        'How can I help': 'நான் எப்படி உதவ முடியும்'
                    };
                    Object.entries(translations).forEach((param)=>{
                        let [en, ta] = param;
                        responseContent = responseContent.replace(new RegExp(en, 'gi'), ta);
                    });
                }
                const assistantMessage = {
                    id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                    role: 'assistant',
                    content: responseContent,
                    timestamp: data.data.timestamp,
                    safetyAlert: data.data.safetyAlert,
                    language,
                    emotion: detectedEmotion,
                    isTyping: true,
                    typedContent: ''
                };
                setMessages((prev)=>[
                        ...prev,
                        assistantMessage
                    ]);
                // Start typing effect
                setTimeout(()=>{
                    typeMessage(assistantMessage);
                }, 500);
            } else {
                throw new Error(data.error || 'Failed to get response');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            // Remove typing indicator
            setMessages((prev)=>prev.filter((msg)=>msg.id !== typingMessage.id));
            const errorMessage = {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                role: 'assistant',
                content: language === 'ta' ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும்.' : 'I apologize, but I\'m having trouble responding right now. Please try again.',
                timestamp: new Date().toISOString(),
                language,
                emotion: 'concerned'
            };
            setMessages((prev)=>[
                    ...prev,
                    errorMessage
                ]);
            typeMessage(errorMessage);
        } finally{
            setIsLoading(false);
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-7xl mx-auto p-4 h-[calc(100vh-100px)]",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-1 lg:grid-cols-2 gap-6 h-full",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-lg overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-semibold",
                                    children: language === 'ta' ? 'AI ஆலோசகர்' : 'AI Counselor'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/avatar-chat/page.tsx",
                                    lineNumber: 338,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-purple-100 text-sm",
                                    children: language === 'ta' ? 'உங்கள் மனநல ஆதரவு நண்பர்' : 'Your Mental Health Support Companion'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/avatar-chat/page.tsx",
                                    lineNumber: 341,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 337,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$HumanAvatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            isListening: avatarIsListening,
                            isSpeaking: avatarIsSpeaking,
                            emotion: avatarEmotion,
                            message: avatarIsSpeaking ? currentSpeechText : undefined,
                            lipSyncData: avatarIsListening ? realTimeLipSync : undefined,
                            facialData: avatarIsListening ? realTimeFacialData : undefined
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 349,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$AudioAnalyzer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            isActive: audioAnalysisActive,
                            onAudioData: handleAudioData
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 359,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$avatar$2f$FacialAnimationEngine$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            text: avatarIsSpeaking ? currentSpeechText : undefined,
                            emotion: avatarEmotion,
                            isActive: avatarIsSpeaking || avatarIsListening,
                            onAnimationUpdate: handleFacialAnimationUpdate
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 365,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/avatar-chat/page.tsx",
                    lineNumber: 336,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg shadow-lg flex flex-col",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-xl font-semibold",
                                                children: language === 'ta' ? 'உரையாடல்' : 'Conversation'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 379,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-pink-100 text-sm",
                                                children: language === 'ta' ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு' : 'Safe, confidential emotional support'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 382,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 378,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-3 h-3 bg-green-400 rounded-full animate-pulse"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 390,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm",
                                                children: language === 'ta' ? 'ஆன்லைன்' : 'Online'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 391,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 389,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                lineNumber: 377,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 376,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$chatbot$2f$VoiceChat$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            onTranscript: handleVoiceTranscript,
                            onSpeakResponse: handleSpeakResponse,
                            isListening: isListening,
                            setIsListening: setIsListening,
                            language: language,
                            setLanguage: setLanguage
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 399,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 border-l-4 border-red-400 p-3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ExclamationTriangleIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExclamationTriangleIcon$3e$__["ExclamationTriangleIcon"], {
                                        className: "h-5 w-5 text-red-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 411,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "ml-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-red-700",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                    children: language === 'ta' ? 'அவசரநிலை:' : 'Emergency:'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                    lineNumber: 414,
                                                    columnNumber: 19
                                                }, this),
                                                ' ',
                                                language === 'ta' ? 'உடனடி ஆபத்தில் இருந்தால், 911 அழைக்கவும். நெருக்கடி ஆதரவுக்கு 988 அழைக்கவும்.' : 'If in immediate danger, call 911. For crisis support, call 988.'
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                                            lineNumber: 413,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 412,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                lineNumber: 410,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 409,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 p-4 bg-gray-50",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center text-gray-600",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-lg font-semibold mb-2",
                                        children: language === 'ta' ? '3D அவதார் சாட்' : '3D Avatar Chat'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 427,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm",
                                        children: language === 'ta' ? 'இடதுபுறத்தில் உள்ள 3D அவதாருடன் பேசுங்கள்!' : 'Interact with the 3D avatar on the left!'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 430,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 space-y-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>sendMessage(language === 'ta' ? 'வணக்கம்!' : 'Hello!'),
                                                className: "block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",
                                                children: language === 'ta' ? '👋 வணக்கம் சொல்லுங்கள்' : '👋 Say Hello'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 437,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious'),
                                                className: "block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",
                                                children: language === 'ta' ? '😰 கவலையை பகிர்ந்து கொள்ளுங்கள்' : '😰 Share Anxiety'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 443,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help'),
                                                className: "block w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors",
                                                children: language === 'ta' ? '🆘 உதவி கேளுங்கள்' : '🆘 Ask for Help'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 449,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 436,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-6 p-3 bg-gray-100 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-sm font-semibold text-gray-700 mb-2",
                                                children: language === 'ta' ? '🚀 மேம்பட்ட அம்சங்கள்' : '🚀 Advanced Features'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 459,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1 text-xs text-gray-600",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: language === 'ta' ? '🎭 முக வெளிப்பாடுகள்' : '🎭 Facial Expressions'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 464,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-green-600",
                                                                children: "✓ Active"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 465,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                        lineNumber: 463,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: language === 'ta' ? '👄 உதடு ஒத்திசைவு' : '👄 Lip Sync'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 468,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: avatarIsListening ? 'text-green-600' : 'text-gray-400',
                                                                children: avatarIsListening ? '✓ Real-time' : '○ Standby'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 469,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                        lineNumber: 467,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: language === 'ta' ? '🎤 ஆடியோ பகுப்பாய்வு' : '🎤 Audio Analysis'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 474,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: audioAnalysisActive ? 'text-green-600' : 'text-gray-400',
                                                                children: audioAnalysisActive ? '✓ Analyzing' : '○ Inactive'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 475,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                        lineNumber: 473,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: language === 'ta' ? '🧠 உணர்ச்சி கண்டறிதல்' : '🧠 Emotion Detection'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 480,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-green-600",
                                                                children: "✓ Active"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                                lineNumber: 481,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                        lineNumber: 479,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                                lineNumber: 462,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/avatar-chat/page.tsx",
                                        lineNumber: 458,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/avatar-chat/page.tsx",
                                lineNumber: 426,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/avatar-chat/page.tsx",
                            lineNumber: 425,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/avatar-chat/page.tsx",
                    lineNumber: 374,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/avatar-chat/page.tsx",
            lineNumber: 333,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/avatar-chat/page.tsx",
        lineNumber: 332,
        columnNumber: 5
    }, this);
}
_s(AvatarChatPage, "nu8PTtYQShC7XLNQ5Ys6SwxVjf4=");
_c = AvatarChatPage;
var _c;
__turbopack_context__.k.register(_c, "AvatarChatPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_3c8e60df._.js.map