!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandContext={},e.React)}(this,(function(e,t){"use strict";e.default=function(){var e=t.createContext(void 0);return{Provider:function(r){var n=r.initialStore,o=r.createStore,i=r.children,u=t.useRef();return u.current||(n&&(console.warn("Provider initialStore is deprecated and will be removed in the next version."),o||(o=function(){return n})),u.current=o()),t.createElement(e.Provider,{value:u.current},i)},useStore:function(r,n){void 0===n&&(n=Object.is);var o=t.useContext(e);if(!o)throw new Error("Seems like you have not used zustand provider as an ancestor.");return o(r,n)},useStoreApi:function(){var r=t.useContext(e);if(!r)throw new Error("Seems like you have not used zustand provider as an ancestor.");return t.useMemo((function(){return{getState:r.getState,setState:r.setState,subscribe:r.subscribe,destroy:r.destroy}}),[r])}}},Object.defineProperty(e,"__esModule",{value:!0})}));
