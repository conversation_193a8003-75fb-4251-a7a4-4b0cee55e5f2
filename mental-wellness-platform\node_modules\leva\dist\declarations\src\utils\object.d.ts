export declare function pick<K extends string, T extends {
    [k in K]: unknown;
}>(object: T, keys: K[]): { [k in K]: T[k]; };
export declare function omit<K extends string, T extends {
    [k in K]: unknown;
}>(object: T, keys: K[]): T;
export declare function mapArrayToKeys<U extends any, K extends string>(value: U[], keys: K[]): Record<K, U>;
export declare function isObject(variable: any): boolean;
export declare const isEmptyObject: (obj: Object) => boolean;
