import { FullTheme, LevaCustomTheme } from './stitches.config';
export declare function mergeTheme(newTheme?: LevaCustomTheme): {
    theme: FullTheme;
    className: string;
};
export declare function useTh<C extends keyof FullTheme>(category: C, key: keyof FullTheme[C]): "" | {
    colors: {
        elevation1: string;
        elevation2: string;
        elevation3: string;
        accent1: string;
        accent2: string;
        accent3: string;
        highlight1: string;
        highlight2: string;
        highlight3: string;
        vivid1: string;
        folderWidgetColor: string;
        folderTextColor: string;
        toolTipBackground: string;
        toolTipText: string;
    };
    radii: {
        xs: string;
        sm: string;
        lg: string;
    };
    space: {
        xs: string;
        sm: string;
        md: string;
        rowGap: string;
        colGap: string;
    };
    fonts: {
        mono: string;
        sans: string;
    };
    fontSizes: {
        root: string;
        toolTip: string;
    };
    sizes: {
        rootWidth: string;
        controlWidth: string;
        numberInputMinWidth: string;
        scrubberWidth: string;
        scrubberHeight: string;
        rowHeight: string;
        folderTitleHeight: string;
        checkboxSize: string;
        joystickWidth: string;
        joystickHeight: string;
        colorPickerWidth: string;
        colorPickerHeight: string;
        imagePreviewWidth: string;
        imagePreviewHeight: string;
        monitorHeight: string;
        titleBarHeight: string;
    };
    shadows: {
        level1: string;
        level2: string;
    };
    borderWidths: {
        root: string;
        input: string;
        focus: string;
        hover: string;
        active: string;
        folder: string;
    };
    fontWeights: {
        label: string;
        folder: string;
        button: string;
    };
}[C][keyof {
    colors: {
        elevation1: string;
        elevation2: string;
        elevation3: string;
        accent1: string;
        accent2: string;
        accent3: string;
        highlight1: string;
        highlight2: string;
        highlight3: string;
        vivid1: string;
        folderWidgetColor: string;
        folderTextColor: string;
        toolTipBackground: string;
        toolTipText: string;
    };
    radii: {
        xs: string;
        sm: string;
        lg: string;
    };
    space: {
        xs: string;
        sm: string;
        md: string;
        rowGap: string;
        colGap: string;
    };
    fonts: {
        mono: string;
        sans: string;
    };
    fontSizes: {
        root: string;
        toolTip: string;
    };
    sizes: {
        rootWidth: string;
        controlWidth: string;
        numberInputMinWidth: string;
        scrubberWidth: string;
        scrubberHeight: string;
        rowHeight: string;
        folderTitleHeight: string;
        checkboxSize: string;
        joystickWidth: string;
        joystickHeight: string;
        colorPickerWidth: string;
        colorPickerHeight: string;
        imagePreviewWidth: string;
        imagePreviewHeight: string;
        monitorHeight: string;
        titleBarHeight: string;
    };
    shadows: {
        level1: string;
        level2: string;
    };
    borderWidths: {
        root: string;
        input: string;
        focus: string;
        hover: string;
        active: string;
        folder: string;
    };
    fontWeights: {
        label: string;
        folder: string;
        button: string;
    };
}[C]];
export * from './stitches.config';
