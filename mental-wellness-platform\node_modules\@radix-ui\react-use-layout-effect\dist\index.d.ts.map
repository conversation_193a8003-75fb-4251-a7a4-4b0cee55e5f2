{"mappings": ";AAEA;;;;;;GAMG;AACH,OAAA,MAAM,6CAAkF,CAAC", "sources": ["packages/react/use-layout-effect/src/packages/react/use-layout-effect/src/useLayoutEffect.tsx", "packages/react/use-layout-effect/src/packages/react/use-layout-effect/src/index.ts", "packages/react/use-layout-effect/src/index.ts"], "sourcesContent": [null, null, "export { useLayoutEffect } from './useLayoutEffect';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}