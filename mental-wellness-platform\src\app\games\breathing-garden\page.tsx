'use client'

import { useState, useEffect, useRef } from 'react'
import { PlayIcon, PauseIcon, ArrowLeftIcon, VolumeUpIcon, VolumeXIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

interface Flower {
  id: number
  x: number
  y: number
  size: number
  color: string
  bloomed: boolean
  bloomProgress: number
}

export default function BreathingGardenGame() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [breathPhase, setBreathPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause'>('inhale')
  const [cycleCount, setCycleCount] = useState(0)
  const [flowers, setFlowers] = useState<Flower[]>([])
  const [score, setScore] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(300) // 5 minutes
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [breathProgress, setBreathProgress] = useState(0)
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const phaseTimerRef = useRef<number>()
  const gameTimerRef = useRef<number>()

  // Breathing pattern: 4-4-4-4 (inhale-hold-exhale-pause)
  const breathingPattern = {
    inhale: 4000,
    hold: 4000,
    exhale: 4000,
    pause: 4000
  }

  // Initialize flowers
  useEffect(() => {
    const initialFlowers: Flower[] = []
    for (let i = 0; i < 12; i++) {
      initialFlowers.push({
        id: i,
        x: Math.random() * 600 + 50,
        y: Math.random() * 400 + 100,
        size: Math.random() * 30 + 20,
        color: ['#FF6B9D', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'][Math.floor(Math.random() * 6)],
        bloomed: false,
        bloomProgress: 0
      })
    }
    setFlowers(initialFlowers)
  }, [])

  // Game timer
  useEffect(() => {
    if (isPlaying && timeRemaining > 0) {
      gameTimerRef.current = window.setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsPlaying(false)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } else {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current)
      }
    }

    return () => {
      if (gameTimerRef.current) {
        clearInterval(gameTimerRef.current)
      }
    }
  }, [isPlaying, timeRemaining])

  // Breathing cycle management
  useEffect(() => {
    if (isPlaying) {
      const duration = breathingPattern[breathPhase]
      let startTime = Date.now()

      const updateProgress = () => {
        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)
        setBreathProgress(progress)

        if (progress < 1) {
          animationRef.current = requestAnimationFrame(updateProgress)
        }
      }

      updateProgress()

      phaseTimerRef.current = window.setTimeout(() => {
        const phases: Array<'inhale' | 'hold' | 'exhale' | 'pause'> = ['inhale', 'hold', 'exhale', 'pause']
        const currentIndex = phases.indexOf(breathPhase)
        const nextPhase = phases[(currentIndex + 1) % phases.length]
        
        setBreathPhase(nextPhase)
        setBreathProgress(0)

        if (nextPhase === 'inhale') {
          setCycleCount(prev => prev + 1)
          // Bloom a flower every 2 cycles
          if ((cycleCount + 1) % 2 === 0) {
            bloomRandomFlower()
          }
        }
      }, duration)
    }

    return () => {
      if (phaseTimerRef.current) {
        clearTimeout(phaseTimerRef.current)
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, breathPhase, cycleCount])

  const bloomRandomFlower = () => {
    setFlowers(prev => {
      const unbloomedFlowers = prev.filter(f => !f.bloomed)
      if (unbloomedFlowers.length === 0) return prev

      const randomFlower = unbloomedFlowers[Math.floor(Math.random() * unbloomedFlowers.length)]
      setScore(prevScore => prevScore + 10)

      return prev.map(flower => 
        flower.id === randomFlower.id 
          ? { ...flower, bloomed: true, bloomProgress: 1 }
          : flower
      )
    })
  }

  // Canvas drawing
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw background gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
    gradient.addColorStop(0, '#87CEEB')
    gradient.addColorStop(1, '#98FB98')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw flowers
    flowers.forEach(flower => {
      const { x, y, size, color, bloomed, bloomProgress } = flower
      
      // Draw stem
      ctx.strokeStyle = '#228B22'
      ctx.lineWidth = 3
      ctx.beginPath()
      ctx.moveTo(x, y + size)
      ctx.lineTo(x, y + size + 30)
      ctx.stroke()

      if (bloomed || bloomProgress > 0) {
        const currentSize = size * (bloomed ? 1 : bloomProgress)
        
        // Draw petals
        ctx.fillStyle = color
        for (let i = 0; i < 6; i++) {
          const angle = (i * Math.PI * 2) / 6
          const petalX = x + Math.cos(angle) * (currentSize * 0.6)
          const petalY = y + Math.sin(angle) * (currentSize * 0.6)
          
          ctx.beginPath()
          ctx.ellipse(petalX, petalY, currentSize * 0.3, currentSize * 0.5, angle, 0, Math.PI * 2)
          ctx.fill()
        }

        // Draw center
        ctx.fillStyle = '#FFD700'
        ctx.beginPath()
        ctx.arc(x, y, currentSize * 0.2, 0, Math.PI * 2)
        ctx.fill()
      } else {
        // Draw bud
        ctx.fillStyle = '#90EE90'
        ctx.beginPath()
        ctx.arc(x, y, size * 0.3, 0, Math.PI * 2)
        ctx.fill()
      }
    })

    // Draw breathing circle
    const centerX = canvas.width / 2
    const centerY = 100
    const baseRadius = 40
    
    let currentRadius = baseRadius
    if (breathPhase === 'inhale') {
      currentRadius = baseRadius + (breathProgress * 20)
    } else if (breathPhase === 'exhale') {
      currentRadius = baseRadius + 20 - (breathProgress * 20)
    } else {
      currentRadius = baseRadius + 20
    }

    // Breathing circle
    ctx.fillStyle = `rgba(100, 149, 237, ${0.3 + breathProgress * 0.4})`
    ctx.beginPath()
    ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2)
    ctx.fill()

    ctx.strokeStyle = '#4169E1'
    ctx.lineWidth = 3
    ctx.beginPath()
    ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2)
    ctx.stroke()

  }, [flowers, breathPhase, breathProgress])

  const toggleGame = () => {
    setIsPlaying(!isPlaying)
  }

  const resetGame = () => {
    setIsPlaying(false)
    setCycleCount(0)
    setScore(0)
    setTimeRemaining(300)
    setBreathPhase('inhale')
    setBreathProgress(0)
    setFlowers(prev => prev.map(f => ({ ...f, bloomed: false, bloomProgress: 0 })))
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getBreathInstruction = () => {
    switch (breathPhase) {
      case 'inhale': return 'Breathe In Slowly'
      case 'hold': return 'Hold Your Breath'
      case 'exhale': return 'Breathe Out Slowly'
      case 'pause': return 'Pause and Relax'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Link href="/games" className="flex items-center text-green-600 hover:text-green-700">
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Games
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">🌸 Breathing Garden</h1>
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="p-2 text-gray-600 hover:text-gray-800"
          >
            {soundEnabled ? <VolumeUpIcon className="h-6 w-6" /> : <VolumeXIcon className="h-6 w-6" />}
          </button>
        </div>

        {/* Game Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 text-center shadow-md">
            <div className="text-2xl font-bold text-green-600">{score}</div>
            <div className="text-sm text-gray-600">Score</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-md">
            <div className="text-2xl font-bold text-blue-600">{cycleCount}</div>
            <div className="text-sm text-gray-600">Breath Cycles</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-md">
            <div className="text-2xl font-bold text-purple-600">{flowers.filter(f => f.bloomed).length}</div>
            <div className="text-sm text-gray-600">Flowers Bloomed</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-md">
            <div className="text-2xl font-bold text-orange-600">{formatTime(timeRemaining)}</div>
            <div className="text-sm text-gray-600">Time Left</div>
          </div>
        </div>

        {/* Breathing Instruction */}
        <div className="bg-white rounded-lg p-6 mb-6 text-center shadow-md">
          <div className="text-2xl font-semibold text-gray-800 mb-2">
            {getBreathInstruction()}
          </div>
          <div className="text-lg text-gray-600">
            Follow the circle and breathe deeply to help flowers bloom
          </div>
          <div className="mt-4 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${breathProgress * 100}%` }}
            />
          </div>
        </div>

        {/* Game Canvas */}
        <div className="bg-white rounded-lg p-4 mb-6 shadow-md">
          <canvas
            ref={canvasRef}
            width={700}
            height={500}
            className="w-full h-auto border border-gray-200 rounded"
          />
        </div>

        {/* Game Controls */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={toggleGame}
            className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            {isPlaying ? <PauseIcon className="h-5 w-5 mr-2" /> : <PlayIcon className="h-5 w-5 mr-2" />}
            {isPlaying ? 'Pause' : 'Start'}
          </button>
          <button
            onClick={resetGame}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Reset Game
          </button>
        </div>

        {/* Game Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">How to Play:</h3>
          <ul className="text-blue-800 space-y-2">
            <li>• Follow the breathing circle and instructions</li>
            <li>• Complete breathing cycles to make flowers bloom</li>
            <li>• Each flower that blooms gives you 10 points</li>
            <li>• Practice for 5 minutes to complete the session</li>
            <li>• Focus on slow, deep breathing for maximum relaxation</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
