{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/resources/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { PhoneIcon, EnvelopeIcon, GlobeAltIcon, MapPinIcon, ExclamationTriangleIcon, ClockIcon } from '@heroicons/react/24/outline'\nimport { EMERGENCY_CONTACTS } from '@/lib/constants'\n\ninterface Resource {\n  id: string\n  name: string\n  type: 'helpline' | 'ngo' | 'emergency' | 'educational' | 'therapy'\n  category: 'mental_health' | 'abuse' | 'emergency' | 'education' | 'women' | 'children'\n  contact: {\n    phone?: string\n    email?: string\n    website?: string\n    address?: string\n  }\n  availability: string\n  location: string\n  description: string\n  isEmergency: boolean\n  languages?: string[]\n  ageGroup?: string\n}\n\nconst resources: Resource[] = [\n  {\n    id: '1',\n    name: 'National Suicide Prevention Lifeline',\n    type: 'emergency',\n    category: 'emergency',\n    contact: {\n      phone: '988',\n      website: 'https://suicidepreventionlifeline.org'\n    },\n    availability: '24/7',\n    location: 'National (USA)',\n    description: 'Free and confidential emotional support for people in suicidal crisis or emotional distress.',\n    isEmergency: true,\n    languages: ['English', 'Spanish']\n  },\n  {\n    id: '2',\n    name: 'Crisis Text Line',\n    type: 'emergency',\n    category: 'emergency',\n    contact: {\n      phone: 'Text HOME to 741741',\n      website: 'https://crisistextline.org'\n    },\n    availability: '24/7',\n    location: 'National (USA)',\n    description: 'Free, 24/7 support for those in crisis. Text with a trained crisis counselor.',\n    isEmergency: true,\n    languages: ['English', 'Spanish']\n  },\n  {\n    id: '3',\n    name: 'National Child Abuse Hotline',\n    type: 'helpline',\n    category: 'children',\n    contact: {\n      phone: '1-800-4-A-CHILD (**************)',\n      website: 'https://childhelp.org'\n    },\n    availability: '24/7',\n    location: 'National (USA)',\n    description: 'Professional crisis counselors provide intervention, information and referrals to thousands of callers each day.',\n    isEmergency: true,\n    languages: ['English', 'Spanish'],\n    ageGroup: 'Children & Teens'\n  },\n  {\n    id: '4',\n    name: 'National Domestic Violence Hotline',\n    type: 'helpline',\n    category: 'women',\n    contact: {\n      phone: '**************',\n      website: 'https://thehotline.org'\n    },\n    availability: '24/7',\n    location: 'National (USA)',\n    description: 'Confidential support for women experiencing domestic violence, available 24/7 in over 200 languages.',\n    isEmergency: true,\n    languages: ['200+ languages available']\n  },\n  {\n    id: '5',\n    name: 'SAMHSA National Helpline',\n    type: 'helpline',\n    category: 'mental_health',\n    contact: {\n      phone: '**************',\n      website: 'https://samhsa.gov'\n    },\n    availability: '24/7',\n    location: 'National (USA)',\n    description: 'Treatment referral and information service for individuals and families facing mental health and/or substance use disorders.',\n    isEmergency: false,\n    languages: ['English', 'Spanish']\n  },\n  {\n    id: '6',\n    name: 'National Alliance on Mental Illness (NAMI)',\n    type: 'ngo',\n    category: 'mental_health',\n    contact: {\n      phone: '**************',\n      email: '<EMAIL>',\n      website: 'https://nami.org'\n    },\n    availability: 'Mon-Fri 10am-10pm ET',\n    location: 'National (USA)',\n    description: 'Support, education and advocacy for individuals and families affected by mental illness.',\n    isEmergency: false,\n    languages: ['English', 'Spanish']\n  },\n  {\n    id: '7',\n    name: 'Girls on the Run',\n    type: 'ngo',\n    category: 'children',\n    contact: {\n      website: 'https://girlsontherun.org',\n      email: '<EMAIL>'\n    },\n    availability: 'Program schedules vary',\n    location: 'Multiple locations',\n    description: 'Physical activity-based positive youth development program for girls in 3rd-8th grade.',\n    isEmergency: false,\n    ageGroup: 'Girls 3rd-8th grade'\n  },\n  {\n    id: '8',\n    name: 'National Women\\'s Health Network',\n    type: 'educational',\n    category: 'women',\n    contact: {\n      phone: '************',\n      email: '<EMAIL>',\n      website: 'https://nwhn.org'\n    },\n    availability: 'Business hours',\n    location: 'National (USA)',\n    description: 'Advocacy organization dedicated to women\\'s health and rights, providing educational resources.',\n    isEmergency: false\n  }\n]\n\nexport default function ResourcesPage() {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [selectedType, setSelectedType] = useState<string>('all')\n\n  const filteredResources = resources.filter(resource => {\n    const categoryMatch = selectedCategory === 'all' || resource.category === selectedCategory\n    const typeMatch = selectedType === 'all' || resource.type === selectedType\n    return categoryMatch && typeMatch\n  })\n\n  const emergencyResources = resources.filter(resource => resource.isEmergency)\n  const nonEmergencyResources = filteredResources.filter(resource => !resource.isEmergency)\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'emergency': return 'bg-red-100 text-red-800'\n      case 'helpline': return 'bg-blue-100 text-blue-800'\n      case 'ngo': return 'bg-green-100 text-green-800'\n      case 'educational': return 'bg-purple-100 text-purple-800'\n      case 'therapy': return 'bg-pink-100 text-pink-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            Crisis Resources & Support\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Immediate access to helplines, emergency services, and support organizations \n            for women and children in need of assistance.\n          </p>\n        </div>\n\n        {/* Emergency Alert */}\n        <div className=\"bg-red-50 border-l-4 border-red-400 p-6 mb-8\">\n          <div className=\"flex\">\n            <ExclamationTriangleIcon className=\"h-6 w-6 text-red-400\" />\n            <div className=\"ml-3\">\n              <h3 className=\"text-lg font-medium text-red-800\">\n                In Immediate Danger?\n              </h3>\n              <div className=\"mt-2 text-sm text-red-700\">\n                <p className=\"mb-2\">If you are in immediate physical danger, call <strong>911</strong> right away.</p>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                  {EMERGENCY_CONTACTS.slice(0, 2).map((contact, index) => (\n                    <div key={index} className=\"bg-red-100 p-3 rounded\">\n                      <p className=\"font-semibold text-red-900\">{contact.name}</p>\n                      <p className=\"text-red-800 font-mono\">{contact.phone}</p>\n                      <p className=\"text-xs text-red-600\">{contact.description}</p>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-md p-6 mb-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category\n              </label>\n              <select\n                value={selectedCategory}\n                onChange={(e) => setSelectedCategory(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">All Categories</option>\n                <option value=\"emergency\">Emergency</option>\n                <option value=\"mental_health\">Mental Health</option>\n                <option value=\"abuse\">Abuse Support</option>\n                <option value=\"women\">Women's Resources</option>\n                <option value=\"children\">Children's Resources</option>\n                <option value=\"education\">Educational</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Type\n              </label>\n              <select\n                value={selectedType}\n                onChange={(e) => setSelectedType(e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"emergency\">Emergency Services</option>\n                <option value=\"helpline\">Helplines</option>\n                <option value=\"ngo\">Organizations</option>\n                <option value=\"educational\">Educational Resources</option>\n                <option value=\"therapy\">Therapy Services</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Emergency Resources */}\n        {selectedCategory === 'all' || selectedCategory === 'emergency' ? (\n          <div className=\"mb-12\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6 flex items-center\">\n              <ExclamationTriangleIcon className=\"h-6 w-6 text-red-500 mr-2\" />\n              Emergency Resources\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {emergencyResources.map((resource) => (\n                <div key={resource.id} className=\"bg-white border-l-4 border-red-500 rounded-lg shadow-md p-6\">\n                  <div className=\"flex justify-between items-start mb-4\">\n                    <h3 className=\"text-xl font-semibold text-gray-900\">{resource.name}</h3>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(resource.type)}`}>\n                      {resource.type}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-gray-600 mb-4\">{resource.description}</p>\n                  \n                  <div className=\"space-y-2 mb-4\">\n                    {resource.contact.phone && (\n                      <div className=\"flex items-center\">\n                        <PhoneIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                        <a href={`tel:${resource.contact.phone}`} className=\"text-blue-600 hover:text-blue-800 font-mono\">\n                          {resource.contact.phone}\n                        </a>\n                      </div>\n                    )}\n                    {resource.contact.website && (\n                      <div className=\"flex items-center\">\n                        <GlobeAltIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                        <a href={resource.contact.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 hover:text-blue-800\">\n                          Visit Website\n                        </a>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <div className=\"flex items-center text-sm text-gray-500\">\n                    <ClockIcon className=\"h-4 w-4 mr-1\" />\n                    {resource.availability}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        ) : null}\n\n        {/* Other Resources */}\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n            Support Resources\n          </h2>\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {nonEmergencyResources.map((resource) => (\n              <div key={resource.id} className=\"bg-white rounded-lg shadow-md p-6\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <h3 className=\"text-xl font-semibold text-gray-900\">{resource.name}</h3>\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(resource.type)}`}>\n                    {resource.type}\n                  </span>\n                </div>\n                \n                <p className=\"text-gray-600 mb-4\">{resource.description}</p>\n                \n                <div className=\"space-y-2 mb-4\">\n                  {resource.contact.phone && (\n                    <div className=\"flex items-center\">\n                      <PhoneIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                      <a href={`tel:${resource.contact.phone}`} className=\"text-blue-600 hover:text-blue-800\">\n                        {resource.contact.phone}\n                      </a>\n                    </div>\n                  )}\n                  {resource.contact.email && (\n                    <div className=\"flex items-center\">\n                      <EnvelopeIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                      <a href={`mailto:${resource.contact.email}`} className=\"text-blue-600 hover:text-blue-800\">\n                        {resource.contact.email}\n                      </a>\n                    </div>\n                  )}\n                  {resource.contact.website && (\n                    <div className=\"flex items-center\">\n                      <GlobeAltIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                      <a href={resource.contact.website} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-600 hover:text-blue-800\">\n                        Visit Website\n                      </a>\n                    </div>\n                  )}\n                  {resource.contact.address && (\n                    <div className=\"flex items-center\">\n                      <MapPinIcon className=\"h-4 w-4 text-gray-400 mr-2\" />\n                      <span className=\"text-gray-600\">{resource.contact.address}</span>\n                    </div>\n                  )}\n                </div>\n                \n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                  <div className=\"flex items-center\">\n                    <ClockIcon className=\"h-4 w-4 mr-1\" />\n                    {resource.availability}\n                  </div>\n                  <span>{resource.location}</span>\n                </div>\n                \n                {resource.languages && (\n                  <div className=\"mt-2\">\n                    <span className=\"text-xs text-gray-500\">Languages: {resource.languages.join(', ')}</span>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {filteredResources.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500 text-lg\">No resources found for the selected filters.</p>\n          </div>\n        )}\n\n        {/* Safety Tips */}\n        <div className=\"mt-16 bg-blue-50 rounded-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Safety Tips</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">🔒 Online Safety</h3>\n              <ul className=\"text-sm text-gray-600 space-y-1\">\n                <li>• Use private browsing mode when seeking help</li>\n                <li>• Clear your browser history after visiting support sites</li>\n                <li>• Use a safe computer or phone if possible</li>\n                <li>• Have a safety plan for online activities</li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900 mb-2\">📞 When Calling for Help</h3>\n              <ul className=\"text-sm text-gray-600 space-y-1\">\n                <li>• Find a safe, private place to make the call</li>\n                <li>• Have important information ready</li>\n                <li>• Know that calls to hotlines are confidential</li>\n                <li>• It's okay to hang up if you don't feel safe</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAyBA,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;YAAW;SAAU;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;YAAW;SAAU;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;SAA2B;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;YAAW;SAAU;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,WAAW;YAAC;YAAW;SAAU;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,SAAS;YACT,OAAO;QACT;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;QACd,UAAU;QACV,aAAa;QACb,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAC1E,MAAM,YAAY,iBAAiB,SAAS,SAAS,IAAI,KAAK;QAC9D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,WAAY,SAAS,WAAW;IAC5E,MAAM,wBAAwB,kBAAkB,MAAM,CAAC,CAAA,WAAY,CAAC,SAAS,WAAW;IAExF,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gPAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;oDAAO;kEAA8C,6LAAC;kEAAO;;;;;;oDAAY;;;;;;;0DACtF,6LAAC;gDAAI,WAAU;0DACZ,0HAAA,CAAA,qBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC5C,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAE,WAAU;0EAA8B,QAAQ,IAAI;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAA0B,QAAQ,KAAK;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAwB,QAAQ,WAAW;;;;;;;uDAHhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAatB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAgB;;;;;;0DAC9B,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;0CAG9B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,6LAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAO/B,qBAAqB,SAAS,qBAAqB,4BAClD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,gPAAA,CAAA,0BAAuB;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAGnE,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;oCAAsB,WAAU;;sDAC/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC,SAAS,IAAI;;;;;;8DAClE,6LAAC;oDAAK,WAAW,AAAC,8CAAyE,OAA5B,aAAa,SAAS,IAAI;8DACtF,SAAS,IAAI;;;;;;;;;;;;sDAIlB,6LAAC;4CAAE,WAAU;sDAAsB,SAAS,WAAW;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,OAAO,CAAC,KAAK,kBACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAE,MAAM,AAAC,OAA6B,OAAvB,SAAS,OAAO,CAAC,KAAK;4DAAI,WAAU;sEACjD,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;gDAI5B,SAAS,OAAO,CAAC,OAAO,kBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAE,MAAM,SAAS,OAAO,CAAC,OAAO;4DAAE,QAAO;4DAAS,KAAI;4DAAsB,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;sDAOjI,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDACpB,SAAS,YAAY;;;;;;;;mCA/BhB,SAAS,EAAE;;;;;;;;;;;;;;;2BAqCzB;8BAGJ,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,yBAC1B,6LAAC;oCAAsB,WAAU;;sDAC/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC,SAAS,IAAI;;;;;;8DAClE,6LAAC;oDAAK,WAAW,AAAC,8CAAyE,OAA5B,aAAa,SAAS,IAAI;8DACtF,SAAS,IAAI;;;;;;;;;;;;sDAIlB,6LAAC;4CAAE,WAAU;sDAAsB,SAAS,WAAW;;;;;;sDAEvD,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,OAAO,CAAC,KAAK,kBACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAE,MAAM,AAAC,OAA6B,OAAvB,SAAS,OAAO,CAAC,KAAK;4DAAI,WAAU;sEACjD,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;gDAI5B,SAAS,OAAO,CAAC,KAAK,kBACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAE,MAAM,AAAC,UAAgC,OAAvB,SAAS,OAAO,CAAC,KAAK;4DAAI,WAAU;sEACpD,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;gDAI5B,SAAS,OAAO,CAAC,OAAO,kBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAE,MAAM,SAAS,OAAO,CAAC,OAAO;4DAAE,QAAO;4DAAS,KAAI;4DAAsB,WAAU;sEAAoC;;;;;;;;;;;;gDAK9H,SAAS,OAAO,CAAC,OAAO,kBACvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAiB,SAAS,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAK/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDACpB,SAAS,YAAY;;;;;;;8DAExB,6LAAC;8DAAM,SAAS,QAAQ;;;;;;;;;;;;wCAGzB,SAAS,SAAS,kBACjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAwB;oDAAY,SAAS,SAAS,CAAC,IAAI,CAAC;;;;;;;;;;;;;mCArDxE,SAAS,EAAE;;;;;;;;;;;;;;;;gBA6D1B,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAKzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GA5PwB;KAAA", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/PhoneIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/EnvelopeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,KAIrB,EAAE,MAAM;QAJa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJqB;IAKpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/GlobeAltIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction GlobeAltIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(GlobeAltIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,KAIrB,EAAE,MAAM;QAJa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJqB;IAKpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,KAInB,EAAE,MAAM;QAJW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJmB;IAKlB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,wBAAwB,KAIhC,EAAE,MAAM;QAJwB,EAC/B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJgC;IAK/B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}