!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("prop-types")):"function"==typeof define&&define.amd?define(["exports","react","prop-types"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).reactDropzone={},e.<PERSON>,e.PropTypes)}(this,(function(e,t,n){"use strict";function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=r(t),i=r(n);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(u)throw o}}return i}(e,t)||p(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */()}function s(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||p(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){if(e){if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function u(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,u)}c((r=r.apply(e,t||[])).next())}))}function g(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}function m(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}var y=new Map([["aac","audio/aac"],["abw","application/x-abiword"],["arc","application/x-freearc"],["avif","image/avif"],["avi","video/x-msvideo"],["azw","application/vnd.amazon.ebook"],["bin","application/octet-stream"],["bmp","image/bmp"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["cda","application/x-cdf"],["csh","application/x-csh"],["css","text/css"],["csv","text/csv"],["doc","application/msword"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["eot","application/vnd.ms-fontobject"],["epub","application/epub+zip"],["gz","application/gzip"],["gif","image/gif"],["heic","image/heic"],["heif","image/heif"],["htm","text/html"],["html","text/html"],["ico","image/vnd.microsoft.icon"],["ics","text/calendar"],["jar","application/java-archive"],["jpeg","image/jpeg"],["jpg","image/jpeg"],["js","text/javascript"],["json","application/json"],["jsonld","application/ld+json"],["mid","audio/midi"],["midi","audio/midi"],["mjs","text/javascript"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mpeg","video/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["opus","audio/opus"],["otf","font/otf"],["png","image/png"],["pdf","application/pdf"],["php","application/x-httpd-php"],["ppt","application/vnd.ms-powerpoint"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["rar","application/vnd.rar"],["rtf","application/rtf"],["sh","application/x-sh"],["svg","image/svg+xml"],["swf","application/x-shockwave-flash"],["tar","application/x-tar"],["tif","image/tiff"],["tiff","image/tiff"],["ts","video/mp2t"],["ttf","font/ttf"],["txt","text/plain"],["vsd","application/vnd.visio"],["wav","audio/wav"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["woff","font/woff"],["woff2","font/woff2"],["xhtml","application/xhtml+xml"],["xls","application/vnd.ms-excel"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xml","application/xml"],["xul","application/vnd.mozilla.xul+xml"],["zip","application/zip"],["7z","application/x-7z-compressed"],["mkv","video/x-matroska"],["mov","video/quicktime"],["msg","application/vnd.ms-outlook"]]);function b(e,t){var n=function(e){var t=e.name;if(t&&-1!==t.lastIndexOf(".")&&!e.type){var n=t.split(".").pop().toLowerCase(),r=y.get(n);r&&Object.defineProperty(e,"type",{value:r,writable:!1,configurable:!1,enumerable:!0})}return e}(e);if("string"!=typeof n.path){var r=e.webkitRelativePath;Object.defineProperty(n,"path",{value:"string"==typeof t?t:"string"==typeof r&&r.length>0?r:e.name,writable:!1,configurable:!1,enumerable:!0})}return n}var h=[".DS_Store","Thumbs.db"];function w(e){return"object"==typeof e&&null!==e}function D(e){return j(e.target.files).map((function(e){return b(e)}))}function x(e){return v(this,void 0,void 0,(function(){return g(this,(function(t){switch(t.label){case 0:return[4,Promise.all(e.map((function(e){return e.getFile()})))];case 1:return[2,t.sent().map((function(e){return b(e)}))]}}))}))}function F(e,t){return v(this,void 0,void 0,(function(){var n;return g(this,(function(r){switch(r.label){case 0:return null===e?[2,[]]:e.items?(n=j(e.items).filter((function(e){return"file"===e.kind})),"drop"!==t?[2,n]:[4,Promise.all(n.map(A))]):[3,2];case 1:return[2,O(k(r.sent()))];case 2:return[2,O(j(e.files).map((function(e){return b(e)})))]}}))}))}function O(e){return e.filter((function(e){return-1===h.indexOf(e.name)}))}function j(e){if(null===e)return[];for(var t=[],n=0;n<e.length;n++){var r=e[n];t.push(r)}return t}function A(e){if("function"!=typeof e.webkitGetAsEntry)return E(e);var t=e.webkitGetAsEntry();return t&&t.isDirectory?C(t):E(e)}function k(e){return e.reduce((function(e,t){return function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(m(arguments[t]));return e}(e,Array.isArray(t)?k(t):[t])}),[])}function E(e){var t=e.getAsFile();if(!t)return Promise.reject(e+" is not a File");var n=b(t);return Promise.resolve(n)}function P(e){return v(this,void 0,void 0,(function(){return g(this,(function(t){return[2,e.isDirectory?C(e):S(e)]}))}))}function C(e){var t=e.createReader();return new Promise((function(e,n){var r=[];!function o(){var i=this;t.readEntries((function(t){return v(i,void 0,void 0,(function(){var i,a,u;return g(this,(function(c){switch(c.label){case 0:if(t.length)return[3,5];c.label=1;case 1:return c.trys.push([1,3,,4]),[4,Promise.all(r)];case 2:return i=c.sent(),e(i),[3,4];case 3:return a=c.sent(),n(a),[3,4];case 4:return[3,6];case 5:u=Promise.all(t.map(P)),r.push(u),o(),c.label=6;case 6:return[2]}}))}))}),(function(e){n(e)}))}()}))}function S(e){return v(this,void 0,void 0,(function(){return g(this,(function(t){return[2,new Promise((function(t,n){e.file((function(n){var r=b(n,e.fullPath);t(r)}),(function(e){n(e)}))}))]}))}))}var z="file-invalid-type",R="file-too-large",T="file-too-small",L="too-many-files",I={FileInvalidType:z,FileTooLarge:R,FileTooSmall:T,TooManyFiles:L},M=function(e){e=Array.isArray(e)&&1===e.length?e[0]:e;var t=Array.isArray(e)?"one of ".concat(e.join(", ")):e;return{code:z,message:"File type must be ".concat(t)}},K=function(e){return{code:R,message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},B=function(e){return{code:T,message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},_={code:L,message:"Too many files"};function q(e,t){var n="application/x-moz-file"===e.type||function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),r=e.name||"",o=(e.type||"").toLowerCase(),i=o.replace(/\/.*$/,"");return n.some((function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?r.toLowerCase().endsWith(t):t.endsWith("/*")?i===t.replace(/\/.*$/,""):o===t}))}return!0}(e,t);return[n,n?null:M(t)]}function G(e,t,n){if($(e.size))if($(t)&&$(n)){if(e.size>n)return[!1,K(n)];if(e.size<t)return[!1,B(t)]}else{if($(t)&&e.size<t)return[!1,B(t)];if($(n)&&e.size>n)return[!1,K(n)]}return[!0,null]}function $(e){return null!=e}function N(e){var t=e.files,n=e.accept,r=e.minSize,o=e.maxSize,i=e.multiple,a=e.maxFiles;return!(!i&&t.length>1||i&&a>=1&&t.length>a)&&t.every((function(e){var t=f(q(e,n),1)[0],i=f(G(e,r,o),1)[0];return t&&i}))}function U(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function W(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,(function(e){return"Files"===e||"application/x-moz-file"===e})):!!e.target&&!!e.target.files}function H(e){e.preventDefault()}function Y(e){return-1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")}function J(e){return-1!==e.indexOf("Edge/")}function Q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return Y(e)||J(e)}function V(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some((function(t){return!U(e)&&t&&t.apply(void 0,[e].concat(r)),U(e)}))}}function X(){return"showOpenFilePicker"in window}function Z(e){return e="string"==typeof e?e.split(","):e,[{description:"everything",accept:Array.isArray(e)?e.filter((function(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||/\w+\/[-+.\w]+/g.test(e)})).reduce((function(e,t){return u(u({},e),{},c({},t,[]))}),{}):{}}]}function ee(e){return e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)}function te(e){return e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)}var ne=["children"],re=["open"],oe=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],ie=["refKey","onChange","onClick"],ae=t.forwardRef((function(e,n){var r=e.children,i=le(l(e,ne)),a=i.open,c=l(i,re);return t.useImperativeHandle(n,(function(){return{open:a}}),[a]),o.default.createElement(t.Fragment,null,r(u(u({},c),{},{open:a})))}));ae.displayName="Dropzone";var ue={disabled:!1,getFilesFromEvent:function(e){return v(this,void 0,void 0,(function(){return g(this,(function(t){return w(e)&&w(e.dataTransfer)?[2,F(e.dataTransfer,e.type)]:function(e){return w(e)&&w(e.target)}(e)?[2,D(e)]:Array.isArray(e)&&e.every((function(e){return"getFile"in e&&"function"==typeof e.getFile}))?[2,x(e)]:[2,[]]}))}))},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!0};ae.defaultProps=ue,ae.propTypes={children:i.default.func,accept:i.default.oneOfType([i.default.string,i.default.arrayOf(i.default.string)]),multiple:i.default.bool,preventDropOnDocument:i.default.bool,noClick:i.default.bool,noKeyboard:i.default.bool,noDrag:i.default.bool,noDragEventsBubbling:i.default.bool,minSize:i.default.number,maxSize:i.default.number,maxFiles:i.default.number,disabled:i.default.bool,getFilesFromEvent:i.default.func,onFileDialogCancel:i.default.func,onFileDialogOpen:i.default.func,useFsAccessApi:i.default.bool,onDragEnter:i.default.func,onDragLeave:i.default.func,onDragOver:i.default.func,onDrop:i.default.func,onDropAccepted:i.default.func,onDropRejected:i.default.func,validator:i.default.func};var ce={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,draggedFiles:[],acceptedFiles:[],fileRejections:[]};function le(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=u(u({},ue),e),r=n.accept,o=n.disabled,i=n.getFilesFromEvent,a=n.maxSize,p=n.minSize,d=n.multiple,v=n.maxFiles,g=n.onDragEnter,m=n.onDragLeave,y=n.onDragOver,b=n.onDrop,h=n.onDropAccepted,w=n.onDropRejected,D=n.onFileDialogCancel,x=n.onFileDialogOpen,F=n.useFsAccessApi,O=n.preventDropOnDocument,j=n.noClick,A=n.noKeyboard,k=n.noDrag,E=n.noDragEventsBubbling,P=n.validator,C=t.useMemo((function(){return"function"==typeof x?x:se}),[x]),S=t.useMemo((function(){return"function"==typeof D?D:se}),[D]),z=t.useRef(null),R=t.useRef(null),T=t.useReducer(fe,ce),L=f(T,2),I=L[0],M=L[1],K=I.isFocused,B=I.isFileDialogActive,$=I.draggedFiles,Y=t.useRef("undefined"!=typeof window&&window.isSecureContext&&F&&X()),J=function(){!Y.current&&B&&setTimeout((function(){R.current&&(R.current.files.length||(M({type:"closeDialog"}),S()))}),300)};t.useEffect((function(){return window.addEventListener("focus",J,!1),function(){window.removeEventListener("focus",J,!1)}}),[R,B,S,Y]);var ne=t.useRef([]),re=function(e){z.current&&z.current.contains(e.target)||(e.preventDefault(),ne.current=[])};t.useEffect((function(){return O&&(document.addEventListener("dragover",H,!1),document.addEventListener("drop",re,!1)),function(){O&&(document.removeEventListener("dragover",H),document.removeEventListener("drop",re))}}),[z,O]);var ae=t.useCallback((function(e){e.preventDefault(),e.persist(),Fe(e),ne.current=[].concat(s(ne.current),[e.target]),W(e)&&Promise.resolve(i(e)).then((function(t){U(e)&&!E||(M({draggedFiles:t,isDragActive:!0,type:"setDraggedFiles"}),g&&g(e))}))}),[i,g,E]),le=t.useCallback((function(e){e.preventDefault(),e.persist(),Fe(e);var t=W(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&y&&y(e),!1}),[y,E]),pe=t.useCallback((function(e){e.preventDefault(),e.persist(),Fe(e);var t=ne.current.filter((function(e){return z.current&&z.current.contains(e)})),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),ne.current=t,t.length>0||(M({isDragActive:!1,type:"setDraggedFiles",draggedFiles:[]}),W(e)&&m&&m(e))}),[z,m,E]),de=t.useCallback((function(e,t){var n=[],o=[];e.forEach((function(e){var t=f(q(e,r),2),i=t[0],u=t[1],c=f(G(e,p,a),2),l=c[0],s=c[1],d=P?P(e):null;if(i&&l&&!d)n.push(e);else{var v=[u,s];d&&(v=v.concat(d)),o.push({file:e,errors:v.filter((function(e){return e}))})}})),(!d&&n.length>1||d&&v>=1&&n.length>v)&&(n.forEach((function(e){o.push({file:e,errors:[_]})})),n.splice(0)),M({acceptedFiles:n,fileRejections:o,type:"setFiles"}),b&&b(n,o,t),o.length>0&&w&&w(o,t),n.length>0&&h&&h(n,t)}),[M,d,r,p,a,v,b,h,w,P]),ve=t.useCallback((function(e){e.preventDefault(),e.persist(),Fe(e),ne.current=[],W(e)&&Promise.resolve(i(e)).then((function(t){U(e)&&!E||de(t,e)})),M({type:"reset"})}),[i,de,E]),ge=t.useCallback((function(){if(Y.current){M({type:"openDialog"}),C();var e={multiple:d,types:Z(r)};window.showOpenFilePicker(e).then((function(e){return i(e)})).then((function(e){de(e,null),M({type:"closeDialog"})})).catch((function(e){ee(e)?(S(e),M({type:"closeDialog"})):te(e)&&(Y.current=!1,R.current&&(R.current.value=null,R.current.click()))}))}else R.current&&(M({type:"openDialog"}),C(),R.current.value=null,R.current.click())}),[M,C,S,F,de,r,d]),me=t.useCallback((function(e){z.current&&z.current.isEqualNode(e.target)&&(" "!==e.key&&"Enter"!==e.key&&32!==e.keyCode&&13!==e.keyCode||(e.preventDefault(),ge()))}),[z,ge]),ye=t.useCallback((function(){M({type:"focus"})}),[]),be=t.useCallback((function(){M({type:"blur"})}),[]),he=t.useCallback((function(){j||(Q()?setTimeout(ge,0):ge())}),[j,ge]),we=function(e){return o?null:e},De=function(e){return A?null:we(e)},xe=function(e){return k?null:we(e)},Fe=function(e){E&&e.stopPropagation()},Oe=t.useMemo((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,r=e.role,i=e.onKeyDown,a=e.onFocus,f=e.onBlur,s=e.onClick,p=e.onDragEnter,d=e.onDragOver,v=e.onDragLeave,g=e.onDrop,m=l(e,oe);return u(u(c({onKeyDown:De(V(i,me)),onFocus:De(V(a,ye)),onBlur:De(V(f,be)),onClick:we(V(s,he)),onDragEnter:xe(V(p,ae)),onDragOver:xe(V(d,le)),onDragLeave:xe(V(v,pe)),onDrop:xe(V(g,ve)),role:"string"==typeof r&&""!==r?r:"button"},n,z),o||A?{}:{tabIndex:0}),m)}}),[z,me,ye,be,he,ae,le,pe,ve,A,k,o]),je=t.useCallback((function(e){e.stopPropagation()}),[]),Ae=t.useMemo((function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=void 0===t?"ref":t,o=e.onChange,i=e.onClick,a=l(e,ie),f=c({accept:r,multiple:d,type:"file",style:{display:"none"},onChange:we(V(o,ve)),onClick:we(V(i,je)),tabIndex:-1},n,R);return u(u({},f),a)}}),[R,r,d,ve,o]),ke=$.length,Ee=ke>0&&N({files:$,accept:r,minSize:p,maxSize:a,multiple:d,maxFiles:v}),Pe=ke>0&&!Ee;return u(u({},I),{},{isDragAccept:Ee,isDragReject:Pe,isFocused:K&&!o,getRootProps:Oe,getInputProps:Ae,rootRef:z,inputRef:R,open:we(ge)})}function fe(e,t){switch(t.type){case"focus":return u(u({},e),{},{isFocused:!0});case"blur":return u(u({},e),{},{isFocused:!1});case"openDialog":return u(u({},ce),{},{isFileDialogActive:!0});case"closeDialog":return u(u({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":var n=t.isDragActive,r=t.draggedFiles;return u(u({},e),{},{draggedFiles:r,isDragActive:n});case"setFiles":return u(u({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections});case"reset":return u({},ce);default:return e}}function se(){}e.ErrorCode=I,e.default=ae,e.useDropzone=le,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
