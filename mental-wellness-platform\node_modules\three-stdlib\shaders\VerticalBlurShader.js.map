{"version": 3, "file": "VerticalBlurShader.js", "sources": ["../../src/shaders/VerticalBlurShader.ts"], "sourcesContent": ["/**\n * Two pass Gaussian blur filter (horizontal and vertical blur shaders)\n * - described in http://www.gamerendering.com/2008/10/11/gaussian-blur-filter-shader/\n *   and used in http://www.cake23.de/traveling-wavefronts-lit-up.html\n *\n * - 9 samples per pass\n * - standard deviation 2.7\n * - \"h\" and \"v\" parameters should be set to \"1 / width\" and \"1 / height\"\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type VerticalBlurShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n  v: IUniform<number>\n}\n\nexport interface IVerticalBlurShader extends IShader<VerticalBlurShaderUniforms> {}\n\nexport const VerticalBlurShader: IVerticalBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    v: { value: 1.0 / 512.0 },\n  },\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n  fragmentShader: /* glsl */ `\n\n  uniform sampler2D tDiffuse;\n  uniform float v;\n\n  varying vec2 vUv;\n\n  void main() {\n\n    vec4 sum = vec4( 0.0 );\n\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 4.0 * v ) ) * 0.051;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 3.0 * v ) ) * 0.0918;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 2.0 * v ) ) * 0.12245;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 1.0 * v ) ) * 0.1531;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 1.0 * v ) ) * 0.1531;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 2.0 * v ) ) * 0.12245;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 3.0 * v ) ) * 0.0918;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 4.0 * v ) ) * 0.051;\n\n    gl_FragColor = sum;\n\n  }\n  `,\n}\n"], "names": [], "mappings": "AAoBO,MAAM,qBAA0C;AAAA,EACrD,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,GAAG,EAAE,OAAO,IAAM,IAAM;AAAA,EAC1B;AAAA,EACA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyB7B;"}