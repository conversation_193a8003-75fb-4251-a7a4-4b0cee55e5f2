{"mappings": ";;;;;;;;;;;;;;;A;;;;;;;;;;;;;;;ACmBA,MAAM,CAAC2B,0CAAD,EAAuB3B,yCAAvB,CAAA,GAA6CgB,yBAAkB,CAAC,SAAD,EAAY;IAC/EI,wBAD+E;CAAZ,CAArE,AAAA;AAGA,MAAMQ,oCAAc,GAAGR,wBAAiB,EAAxC,AAAA;AAEA;;oGAEA,CAEA,MAAMS,mCAAa,GAAG,iBAAtB,AAAA;AACA,MAAMC,4CAAsB,GAAG,GAA/B,AAAA;AACA,MAAMC,kCAAY,GAAG,cAArB,AAAA;AAYA,MAAM,CAACC,oDAAD,EAAiCC,+CAAjC,CAAA,GACJN,0CAAoB,CAA8BE,mCAA9B,CADtB,AAAA;AAsBA,MAAM5B,yCAA+C,GACnDiC,CAAAA,KADsD,GAEnD;IACH,MAAM,E,gBACJC,cADI,CAAA,iBAEJC,aAAa,GAAGN,4CAFZ,sBAGJO,iBAAiB,GAAG,GAHhB,4BAIJC,uBAAuB,GAAG,KAJtB,G,UAKJC,QAAAA,CAAAA,EALI,GAMFL,KANJ,AAAM;IAON,MAAM,CAACM,aAAD,EAAgBC,gBAAhB,CAAA,GAAoC5B,eAAA,CAAe,IAAf,CAA1C,AAAA;IACA,MAAM8B,qBAAqB,GAAG9B,aAAA,CAAa,KAAb,CAA9B,AAAA;IACA,MAAMgC,iBAAiB,GAAGhC,aAAA,CAAa,CAAb,CAA1B,AAAA;IAEAA,gBAAA,CAAgB,IAAM;QACpB,MAAMkC,cAAc,GAAGF,iBAAiB,CAACG,OAAzC,AAAA;QACA,OAAO,IAAMC,MAAM,CAACC,YAAP,CAAoBH,cAApB,CAAb;QAAA,CAAA;KAFF,EAGG,EAHH,CAGC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,oDAAD,EADF;QAEI,KAAK,EAAEZ,cADT;QAEE,aAAa,EAAEK,aAFjB;QAGE,aAAa,EAAEJ,aAHjB;QAIE,MAAM,EAAEvB,kBAAA,CAAkB,IAAM;YAC9BoC,MAAM,CAACC,YAAP,CAAoBL,iBAAiB,CAACG,OAAtC,CAAAC,CAAAA;YACAR,gBAAgB,CAAC,KAAD,CAAhB,CAAAA;SAFM,EAGL,EAHK,CAJV;QAQE,OAAO,EAAE5B,kBAAA,CAAkB,IAAM;YAC/BoC,MAAM,CAACC,YAAP,CAAoBL,iBAAiB,CAACG,OAAtC,CAAAC,CAAAA;YACAJ,iBAAiB,CAACG,OAAlB,GAA4BC,MAAM,CAACG,UAAP,CAC1B,IAAMX,gBAAgB,CAAC,IAAD,CADI;YAAA,EAE1BJ,iBAF0B,CAA5B,CAAAQ;SAFO,EAMN;YAACR,iBAAD;SANM,CARX;QAeE,qBAAqB,EAAEM,qBAfzB;QAgBE,wBAAwB,EAAE9B,kBAAA,CAAmBwC,CAAAA,SAAD,GAAwB;YAClEV,qBAAqB,CAACK,OAAtB,GAAgCK,SAAhC,CAAAV;SADwB,EAEvB,EAFuB,CAhB5B;QAmBE,uBAAuB,EAAEL,uBAAzB;KAnBF,EAqBGC,QArBH,CADF,CACE;CApBJ,AA4CC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,mCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMe,kCAAY,GAAG,SAArB,AAAA;AAeA,MAAM,CAACC,4CAAD,EAAyBC,uCAAzB,CAAA,GACJ7B,0CAAoB,CAAsB2B,kCAAtB,CADtB,AAAA;AAqBA,MAAMpD,yCAA+B,GAAIgC,CAAAA,KAAD,GAAsC;IAC5E,MAAM,E,gBACJC,cADI,CAAA,E,UAEJI,QAFI,CAAA,EAGJkB,IAAI,EAAEC,QAHF,CAAA,eAIJC,WAAW,GAAG,KAJV,G,cAKJC,YALI,CAAA,EAMJtB,uBAAuB,EAAEuB,2BANrB,CAAA,EAOJzB,aAAa,EAAE0B,iBAAf1B,CAAAA,EAPI,GAQFF,KARJ,AAAM;IASN,MAAM6B,eAAe,GAAG9B,+CAAyB,CAACqB,kCAAD,EAAepB,KAAK,CAACC,cAArB,CAAjD,AAAA;IACA,MAAM6B,WAAW,GAAGpC,oCAAc,CAACO,cAAD,CAAlC,AAAA;IACA,MAAM,CAAC8B,OAAD,EAAUC,UAAV,CAAA,GAAwBrD,eAAA,CAAyC,IAAzC,CAA9B,AAAA;IACA,MAAMsD,SAAS,GAAGjD,YAAK,EAAvB,AAAA;IACA,MAAMkD,YAAY,GAAGvD,aAAA,CAAa,CAAb,CAArB,AAAA;IACA,MAAMyB,uBAAuB,GAC3BuB,2BAD2B,KAAA,IAAA,IAC3BA,2BAD2B,KAAA,KAAA,CAAA,GAC3BA,2BAD2B,GACIE,eAAe,CAACzB,uBADjD,AAAA;IAEA,MAAMF,aAAa,GAAG0B,iBAAH,KAAA,IAAA,IAAGA,iBAAH,KAAA,KAAA,CAAA,GAAGA,iBAAH,GAAwBC,eAAe,CAAC3B,aAA3D,AAAA;IACA,MAAMiC,iBAAiB,GAAGxD,aAAA,CAAa,KAAb,CAA1B,AAAA;IACA,MAAM,CAAC4C,KAAI,GAAG,KAAR,EAAea,OAAf,CAAA,GAA0B7C,2BAAoB,CAAC;QACnD8C,IAAI,EAAEb,QAD6C;QAEnDc,WAAW,EAAEb,WAFsC;QAGnDc,QAAQ,EAAGhB,CAAAA,IAAD,GAAU;YAClB,IAAIA,IAAJ,EAAU;gBACRM,eAAe,CAACW,MAAhB,EAAA,CADQ,CAGR,uDAFAX;gBAGA,uDAAA;gBACAY,QAAQ,CAACC,aAAT,CAAuB,IAAIC,WAAJ,CAAgB9C,kCAAhB,CAAvB,CAAA4C,CAAAA;aALF,MAOEZ,eAAe,CAACe,OAAhB,EAAAf,CAAAA;YAEFH,YAAY,KAAA,IAAZ,IAAAA,YAAY,KAAA,KAAA,CAAZ,IAAAA,YAAY,CAAGH,IAAH,CAAZ,CAAAG;SACD;KAdiD,CAApD,AAAqD;IAgBrD,MAAMmB,cAAc,GAAGlE,cAAA,CAAc,IAAM;QACzC,OAAO4C,KAAI,GAAIY,iBAAiB,CAACrB,OAAlB,GAA4B,cAA5B,GAA6C,cAAjD,GAAmE,QAA9E,CAAA;KADqB,EAEpB;QAACS,KAAD;KAFoB,CAAvB,AAEC;IAED,MAAMwB,UAAU,GAAGpE,kBAAA,CAAkB,IAAM;QACzCoC,MAAM,CAACC,YAAP,CAAoBkB,YAAY,CAACpB,OAAjC,CAAAC,CAAAA;QACAoB,iBAAiB,CAACrB,OAAlB,GAA4B,KAA5B,CAAAqB;QACAC,OAAO,CAAC,IAAD,CAAP,CAAAA;KAHiB,EAIhB;QAACA,OAAD;KAJgB,CAAnB,AAIC;IAED,MAAMY,WAAW,GAAGrE,kBAAA,CAAkB,IAAM;QAC1CoC,MAAM,CAACC,YAAP,CAAoBkB,YAAY,CAACpB,OAAjC,CAAAC,CAAAA;QACAqB,OAAO,CAAC,KAAD,CAAP,CAAAA;KAFkB,EAGjB;QAACA,OAAD;KAHiB,CAApB,AAGC;IAED,MAAMa,iBAAiB,GAAGtE,kBAAA,CAAkB,IAAM;QAChDoC,MAAM,CAACC,YAAP,CAAoBkB,YAAY,CAACpB,OAAjC,CAAAC,CAAAA;QACAmB,YAAY,CAACpB,OAAb,GAAuBC,MAAM,CAACG,UAAP,CAAkB,IAAM;YAC7CiB,iBAAiB,CAACrB,OAAlB,GAA4B,IAA5B,CAAAqB;YACAC,OAAO,CAAC,IAAD,CAAP,CAAAA;SAFqB,EAGpBlC,aAHoB,CAAvB,CAGC;KALuB,EAMvB;QAACA,aAAD;QAAgBkC,OAAhB;KANuB,CAA1B,AAMC;IAEDzD,gBAAA,CAAgB,IAAM;QACpB,OAAO,IAAMoC,MAAM,CAACC,YAAP,CAAoBkB,YAAY,CAACpB,OAAjC,CAAb;QAAA,CAAA;KADF,EAEG,EAFH,CAEC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,WAAD,EAA0BgB,WAA1B,EAAA,aACE,CAAA,oBAAA,CAAC,4CAAD,EAFJ;QAGM,KAAK,EAAE7B,cADT;QAEE,SAAS,EAAEgC,SAFb;QAGE,IAAI,EAAEV,KAHR;QAIE,cAAc,EAAEsB,cAJlB;QAKE,OAAO,EAAEd,OALX;QAME,eAAe,EAAEC,UANnB;QAOE,cAAc,EAAErD,kBAAA,CAAkB,IAAM;YACtC,IAAIkD,eAAe,CAACvB,aAApB,EAAmC2C,iBAAiB,EAAA,CAApD;iBACKF,UAAU,EADf,CAAA;SADc,EAGb;YAAClB,eAAe,CAACvB,aAAjB;YAAgC2C,iBAAhC;YAAmDF,UAAnD;SAHa,CAPlB;QAWE,cAAc,EAAEpE,kBAAA,CAAkB,IAAM;YACtC,IAAIyB,uBAAJ,EACE4C,WAAW,EAAXA,CAAAA;iBAEA,uFAAA;YACAjC,MAAM,CAACC,YAAP,CAAoBkB,YAAY,CAACpB,OAAjC,CAAAC,CAAAA;SALY,EAOb;YAACiC,WAAD;YAAc5C,uBAAd;SAPa,CAXlB;QAmBE,MAAM,EAAE2C,UAnBV;QAoBE,OAAO,EAAEC,WApBX;QAqBE,uBAAuB,EAAE5C,uBAAzB;KArBF,EAuBGC,QAvBH,CADF,CADF,CAEI;CAhEN,AA2FC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM6C,kCAAY,GAAG,gBAArB,AAAA;AAMA,MAAMjF,yCAAc,GAAA,aAAGU,CAAAA,iBAAA,CACrB,CAACqB,KAAD,EAA0CoD,YAA1C,GAA2D;IACzD,MAAM,E,gBAAEnD,cAAF,CAAA,EAAkB,GAAGoD,YAAH,EAAlB,GAAsCrD,KAA5C,AAAM;IACN,MAAMsD,OAAO,GAAGhC,uCAAiB,CAAC4B,kCAAD,EAAejD,cAAf,CAAjC,AAAA;IACA,MAAM4B,eAAe,GAAG9B,+CAAyB,CAACmD,kCAAD,EAAejD,cAAf,CAAjD,AAAA;IACA,MAAM6B,WAAW,GAAGpC,oCAAc,CAACO,cAAD,CAAlC,AAAA;IACA,MAAMsD,GAAG,GAAG5E,aAAA,CAAoC,IAApC,CAAZ,AAAA;IACA,MAAM6E,YAAY,GAAG3E,sBAAe,CAACuE,YAAD,EAAeG,GAAf,EAAoBD,OAAO,CAACG,eAA5B,CAApC,AAAA;IACA,MAAMC,gBAAgB,GAAG/E,aAAA,CAAa,KAAb,CAAzB,AAAA;IACA,MAAMgF,uBAAuB,GAAGhF,aAAA,CAAa,KAAb,CAAhC,AAAA;IACA,MAAMiF,eAAe,GAAGjF,kBAAA,CAAkB,IAAO+E,gBAAgB,CAAC5C,OAAjB,GAA2B,KAApD;IAAA,EAA4D,EAA5D,CAAxB,AAAA;IAEAnC,gBAAA,CAAgB,IAAM;QACpB,OAAO,IAAM8D,QAAQ,CAACoB,mBAAT,CAA6B,WAA7B,EAA0CD,eAA1C,CAAb;QAAA,CAAA;KADF,EAEG;QAACA,eAAD;KAFH,CAEC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,aAAD,EADF,oCAAA,CAAA;QAC0B,OAAO,EAAP,IAAA;KAAxB,EAAoC9B,WAApC,CAAA,EAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,MAAX,EADF,oCAAA,CAAA;QAEI,oFAAA;QACA,wEAAA;QACA,kBAAA,EAAkBwB,OAAO,CAAC/B,IAAR,GAAe+B,OAAO,CAACrB,SAAvB,GAAmC6B,SAHvD;QAIE,YAAA,EAAYR,OAAO,CAACT,cAApB;KAJF,EAKMQ,YALN,EAAA;QAME,GAAG,EAAEG,YANP;QAOE,aAAa,EAAE5E,2BAAoB,CAACoB,KAAK,CAAC+D,aAAP,EAAuBC,CAAAA,KAAD,GAAW;YAClE,IAAIA,KAAK,CAACC,WAAN,KAAsB,OAA1B,EAAmC,OAAnC;YACA,IACE,CAACN,uBAAuB,CAAC7C,OAAzB,IACA,CAACe,eAAe,CAACpB,qBAAhB,CAAsCK,OAFzC,EAGE;gBACAwC,OAAO,CAACY,cAAR,EAAAZ,CAAAA;gBACAK,uBAAuB,CAAC7C,OAAxB,GAAkC,IAAlC,CAAA6C;aACD;SARgC,CAPrC;QAiBE,cAAc,EAAE/E,2BAAoB,CAACoB,KAAK,CAACmE,cAAP,EAAuB,IAAM;YAC/Db,OAAO,CAACc,cAAR,EAAAd,CAAAA;YACAK,uBAAuB,CAAC7C,OAAxB,GAAkC,KAAlC,CAAA6C;SAFkC,CAjBtC;QAqBE,aAAa,EAAE/E,2BAAoB,CAACoB,KAAK,CAACqE,aAAP,EAAsB,IAAM;YAC7DX,gBAAgB,CAAC5C,OAAjB,GAA2B,IAA3B,CAAA4C;YACAjB,QAAQ,CAAC6B,gBAAT,CAA0B,WAA1B,EAAuCV,eAAvC,EAAwD;gBAAEW,IAAI,EAAE,IAANA;aAA1D,CAAwD,CAAA;SAFvB,CArBrC;QAyBE,OAAO,EAAE3F,2BAAoB,CAACoB,KAAK,CAACwE,OAAP,EAAgB,IAAM;YACjD,IAAI,CAACd,gBAAgB,CAAC5C,OAAtB,EAA+BwC,OAAO,CAACd,MAAR,EAA/B,CAAA;SAD2B,CAzB/B;QA4BE,MAAM,EAAE5D,2BAAoB,CAACoB,KAAK,CAACyE,MAAP,EAAenB,OAAO,CAACV,OAAvB,CA5B9B;QA6BE,OAAO,EAAEhE,2BAAoB,CAACoB,KAAK,CAAC0E,OAAP,EAAgBpB,OAAO,CAACV,OAAxB,CAA7B;KA7BF,CAAA,CADF,CADF,CAEI;CAlBe,CAAvB,AAmDG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM+B,iCAAW,GAAG,eAApB,AAAA;AAGA,MAAM,CAACC,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqCpF,0CAAoB,CAAqBkF,iCAArB,EAAkC;IAC/FG,UAAU,EAAEhB,SAAZgB;CAD6D,CAA/D,AAAiG;AAcjG,MAAM5G,yCAA2C,GAAI8B,CAAAA,KAAD,GAA4C;IAC9F,MAAM,E,gBAAEC,cAAF,CAAA,E,YAAkB6E,UAAlB,CAAA,E,UAA8BzE,QAA9B,CAAA,E,WAAwC0E,SAAAA,CAAAA,EAAxC,GAAsD/E,KAA5D,AAAM;IACN,MAAMsD,OAAO,GAAGhC,uCAAiB,CAACqD,iCAAD,EAAc1E,cAAd,CAAjC,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,oCAAD,EADF;QACkB,KAAK,EAAEA,cAAvB;QAAuC,UAAU,EAAE6E,UAAZ;KAAvC,EAAA,aACE,CAAA,oBAAA,CAAC,eAAD,EADF;QACY,OAAO,EAAEA,UAAU,IAAIxB,OAAO,CAAC/B,IAA/B;KAAV,EAAA,aACE,CAAA,oBAAA,CAAC,aAAD,EADF;QACmB,OAAO,EAAA,IAAxB;QAAyB,SAAS,EAAEwD,SAAX;KAAzB,EACG1E,QADH,CADF,CADF,CADF,CAGM;CANR,AAYC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2E,kCAAY,GAAG,gBAArB,AAAA;AAWA,MAAM7G,yCAAc,GAAA,aAAGQ,CAAAA,iBAAA,CACrB,CAACqB,KAAD,EAA0CoD,YAA1C,GAA2D;IACzD,MAAM6B,aAAa,GAAGJ,sCAAgB,CAACG,kCAAD,EAAehF,KAAK,CAACC,cAArB,CAAtC,AAAA;IACA,MAAM,cAAE6E,UAAU,GAAGG,aAAa,CAACH,UAA7B,SAAyCI,IAAI,GAAG,KAAhD,GAAuD,GAAGC,YAAH,EAAvD,GAA2EnF,KAAjF,AAAM;IACN,MAAMsD,OAAO,GAAGhC,uCAAiB,CAAC0D,kCAAD,EAAehF,KAAK,CAACC,cAArB,CAAjC,AAAA;IAEA,OAAA,aACE,CAAA,oBAAA,CAAC,eAAD,EADF;QACY,OAAO,EAAE6E,UAAU,IAAIxB,OAAO,CAAC/B,IAA/B;KAAV,EACG+B,OAAO,CAAClD,uBAAR,GAAA,aACC,CAAA,oBAAA,CAAC,wCAAD,EAFJ,oCAAA,CAAA;QAEwB,IAAI,EAAE8E,IAAN;KAApB,EAAoCC,YAApC,EAAA;QAAkD,GAAG,EAAE/B,YAAL;KAAlD,CAAA,CADD,GAAA,aAGC,CAAA,oBAAA,CAAC,6CAAD,EAFA,oCAAA,CAAA;QAEyB,IAAI,EAAE8B,IAAN;KAAzB,EAAyCC,YAAzC,EAAA;QAAuD,GAAG,EAAE/B,YAAL;KAAvD,CAAA,CAJJ,CADF,CAKM;CAXa,CAAvB,AAeG;AASH,MAAMgC,6CAAuB,GAAA,aAAGzG,CAAAA,iBAAA,CAG9B,CAACqB,KAAD,EAAmDoD,YAAnD,GAAoE;IACpE,MAAME,OAAO,GAAGhC,uCAAiB,CAAC0D,kCAAD,EAAehF,KAAK,CAACC,cAArB,CAAjC,AAAA;IACA,MAAM4B,eAAe,GAAG9B,+CAAyB,CAACiF,kCAAD,EAAehF,KAAK,CAACC,cAArB,CAAjD,AAAA;IACA,MAAMsD,GAAG,GAAG5E,aAAA,CAA6C,IAA7C,CAAZ,AAAA;IACA,MAAM6E,YAAY,GAAG3E,sBAAe,CAACuE,YAAD,EAAeG,GAAf,CAApC,AAAA;IACA,MAAM,CAAC8B,gBAAD,EAAmBC,mBAAnB,CAAA,GAA0C3G,eAAA,CAA+B,IAA/B,CAAhD,AAAA;IAEA,MAAM,E,SAAEoD,OAAF,CAAA,E,SAAWa,OAAAA,CAAAA,EAAX,GAAuBU,OAA7B,AAAM;IACN,MAAMiC,OAAO,GAAGhC,GAAG,CAACzC,OAApB,AAAA;IAEA,MAAM,E,0BAAE0E,wBAAAA,CAAAA,EAAF,GAA+B3D,eAArC,AAAM;IAEN,MAAM4D,qBAAqB,GAAG9G,kBAAA,CAAkB,IAAM;QACpD2G,mBAAmB,CAAC,IAAD,CAAnB,CAAAA;QACAE,wBAAwB,CAAC,KAAD,CAAxB,CAAAA;KAF4B,EAG3B;QAACA,wBAAD;KAH2B,CAA9B,AAGC;IAED,MAAME,qBAAqB,GAAG/G,kBAAA,CAC5B,CAACqF,KAAD,EAAsB2B,WAAtB,GAAmD;QACjD,MAAMC,aAAa,GAAG5B,KAAK,CAAC4B,aAA5B,AAAA;QACA,MAAMC,SAAS,GAAG;YAAEC,CAAC,EAAE9B,KAAK,CAAC+B,OAAX;YAAoBC,CAAC,EAAEhC,KAAK,CAACiC,OAATD;SAAtC,AAAkB;QAClB,MAAME,QAAQ,GAAGC,yCAAmB,CAACN,SAAD,EAAYD,aAAa,CAACQ,qBAAd,EAAZ,CAApC,AAAA;QAEA,MAAMC,KAAK,GAAGH,QAAQ,KAAK,OAAb,IAAwBA,QAAQ,KAAK,QAArC,GAAgD,EAAhD,GAAqD,CAAnE,AAAA;QACA,MAAMI,OAAO,GAAGJ,QAAQ,KAAK,OAAb,IAAwBA,QAAQ,KAAK,MAArD,AAAA;QACA,MAAMK,UAAU,GAAGD,OAAO,GACtB;YAAER,CAAC,EAAE9B,KAAK,CAAC+B,OAAN,GAAgBM,KAArB;YAA4BL,CAAC,EAAEhC,KAAK,CAACiC,OAATD;SADN,GAEtB;YAAEF,CAAC,EAAE9B,KAAK,CAAC+B,OAAX;YAAoBC,CAAC,EAAEhC,KAAK,CAACiC,OAAN,GAAgBI,KAAnBL;SAFxB,AAEI;QAEJ,MAAMQ,iBAAiB,GAAGC,uCAAiB,CAACd,WAAW,CAACS,qBAAZ,EAAD,CAA3C,AAAA;QACA,MAAMM,SAAS,GAAGC,6BAAO,CAAC;YAACJ,UAAD;eAAgBC,iBAAhB;SAAD,CAAzB,AAAA;QACAlB,mBAAmB,CAACoB,SAAD,CAAnB,CAAApB;QACAE,wBAAwB,CAAC,IAAD,CAAxB,CAAAA;KAf0B,EAiB5B;QAACA,wBAAD;KAjB4B,CAA9B,AAgBG;IAIH7G,gBAAA,CAAgB,IAAM;QACpB,OAAO,IAAM8G,qBAAqB,EAAlC;QAAA,CAAA;KADF,EAEG;QAACA,qBAAD;KAFH,CAEC,CAAA;IAED9G,gBAAA,CAAgB,IAAM;QACpB,IAAIoD,OAAO,IAAIwD,OAAf,EAAwB;YACtB,MAAMqB,kBAAkB,GAAI5C,CAAAA,KAAD,GAAyB0B,qBAAqB,CAAC1B,KAAD,EAAQuB,OAAR,CAAzE;YAAA;YACA,MAAMsB,kBAAkB,GAAI7C,CAAAA,KAAD,GAAyB0B,qBAAqB,CAAC1B,KAAD,EAAQjC,OAAR,CAAzE;YAAA;YAEAA,OAAO,CAACuC,gBAAR,CAAyB,cAAzB,EAAyCsC,kBAAzC,CAAA7E,CAAAA;YACAwD,OAAO,CAACjB,gBAAR,CAAyB,cAAzB,EAAyCuC,kBAAzC,CAAAtB,CAAAA;YACA,OAAO,IAAM;gBACXxD,OAAO,CAAC8B,mBAAR,CAA4B,cAA5B,EAA4C+C,kBAA5C,CAAA7E,CAAAA;gBACAwD,OAAO,CAAC1B,mBAAR,CAA4B,cAA5B,EAA4CgD,kBAA5C,CAAAtB,CAAAA;aAFF,CAGC;SACF;KAXH,EAYG;QAACxD,OAAD;QAAUwD,OAAV;QAAmBG,qBAAnB;QAA0CD,qBAA1C;KAZH,CAYC,CAAA;IAED9G,gBAAA,CAAgB,IAAM;QACpB,IAAI0G,gBAAJ,EAAsB;YACpB,MAAMyB,uBAAuB,GAAI9C,CAAAA,KAAD,GAAyB;gBACvD,MAAM+C,MAAM,GAAG/C,KAAK,CAAC+C,MAArB,AAAA;gBACA,MAAMC,eAAe,GAAG;oBAAElB,CAAC,EAAE9B,KAAK,CAAC+B,OAAX;oBAAoBC,CAAC,EAAEhC,KAAK,CAACiC,OAATD;iBAA5C,AAAwB;gBACxB,MAAMiB,gBAAgB,GAAG,AAAAlF,CAAAA,OAAO,KAAA,IAAP,IAAAA,OAAO,KAAA,KAAA,CAAP,GAAA,KAAA,CAAA,GAAAA,OAAO,CAAEmF,QAAT,CAAkBH,MAAlB,CAAA,CAAA,IAA6BxB,CAAAA,OAA7B,KAAA,IAAA,IAA6BA,OAA7B,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA6BA,OAAO,CAAE2B,QAAT,CAAkBH,MAAlB,CAA7B,CAAA,AAAzB,AAAA;gBACA,MAAMI,yBAAyB,GAAG,CAACC,sCAAgB,CAACJ,eAAD,EAAkB3B,gBAAlB,CAAnD,AAAA;gBAEA,IAAI4B,gBAAJ,EACExB,qBAAqB,EAArBA,CAAAA;qBACK,IAAI0B,yBAAJ,EAA+B;oBACpC1B,qBAAqB,EAArBA,CAAAA;oBACA7C,OAAO,EAAPA,CAAAA;iBACD;aAXH,AAYC;YACDH,QAAQ,CAAC6B,gBAAT,CAA0B,aAA1B,EAAyCwC,uBAAzC,CAAArE,CAAAA;YACA,OAAO,IAAMA,QAAQ,CAACoB,mBAAT,CAA6B,aAA7B,EAA4CiD,uBAA5C,CAAb;YAAA,CAAA;SACD;KAjBH,EAkBG;QAAC/E,OAAD;QAAUwD,OAAV;QAAmBF,gBAAnB;QAAqCzC,OAArC;QAA8C6C,qBAA9C;KAlBH,CAkBC,CAAA;IAED,OAAA,aAAO,CAAA,oBAAA,CAAC,wCAAD,EAAA,oCAAA,CAAA,EAAA,EAAwBzF,KAAxB,EAAP;QAAsC,GAAG,EAAEwD,YAAL;KAA/B,CAAA,CAAP,CAAO;CA9EuB,CAAhC,AA+EC;AAED,MAAM,CAAC6D,0DAAD,EAAuCC,qDAAvC,CAAA,GACJ7H,0CAAoB,CAAC2B,kCAAD,EAAe;IAAEmG,QAAQ,EAAE,KAAVA;CAAjB,CADtB,AACqC;AAuBrC,MAAMC,wCAAkB,GAAA,aAAG7I,CAAAA,iBAAA,CACzB,CAACqB,KAAD,EAA8CoD,YAA9C,GAA+D;IAC7D,MAAM,E,gBACJnD,cADI,CAAA,E,UAEJI,QAFI,CAAA,EAGJ,YAAA,EAAcoH,SAHV,CAAA,E,iBAIJC,eAJI,CAAA,E,sBAKJC,oBALI,CAAA,EAMJ,GAAGxC,YAAH,EANI,GAOFnF,KAPJ,AAAM;IAQN,MAAMsD,OAAO,GAAGhC,uCAAiB,CAAC0D,kCAAD,EAAe/E,cAAf,CAAjC,AAAA;IACA,MAAM6B,WAAW,GAAGpC,oCAAc,CAACO,cAAD,CAAlC,AAAA;IACA,MAAM,E,SAAE2C,OAAAA,CAAAA,EAAF,GAAcU,OAApB,AAX6D,EAa7D,0CAFM;IAGN3E,gBAAA,CAAgB,IAAM;QACpB8D,QAAQ,CAAC6B,gBAAT,CAA0BzE,kCAA1B,EAAwC+C,OAAxC,CAAAH,CAAAA;QACA,OAAO,IAAMA,QAAQ,CAACoB,mBAAT,CAA6BhE,kCAA7B,EAA2C+C,OAA3C,CAAb;QAAA,CAAA;KAFF,EAGG;QAACA,OAAD;KAHH,CAAA,CAd6D,CAmB7D,+CAFC;IAGDjE,gBAAA,CAAgB,IAAM;QACpB,IAAI2E,OAAO,CAACvB,OAAZ,EAAqB;YACnB,MAAM6F,YAAY,GAAI5D,CAAAA,KAAD,GAAkB;gBACrC,MAAM+C,MAAM,GAAG/C,KAAK,CAAC+C,MAArB,AAAA;gBACA,IAAIA,MAAJ,KAAA,IAAA,IAAIA,MAAJ,KAAA,KAAA,CAAA,IAAIA,MAAM,CAAEG,QAAR,CAAiB5D,OAAO,CAACvB,OAAzB,CAAJ,EAAuCa,OAAO,EAA9C,CAAA;aAFF,AAGC;YACD7B,MAAM,CAACuD,gBAAP,CAAwB,QAAxB,EAAkCsD,YAAlC,EAAgD;gBAAEC,OAAO,EAAE,IAATA;aAAlD,CAAgD,CAAA;YAChD,OAAO,IAAM9G,MAAM,CAAC8C,mBAAP,CAA2B,QAA3B,EAAqC+D,YAArC,EAAmD;oBAAEC,OAAO,EAAE,IAATA;iBAArD,CAAb;YAAA,CAAgE;SACjE;KARH,EASG;QAACvE,OAAO,CAACvB,OAAT;QAAkBa,OAAlB;KATH,CASC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,uBAAD,EADF;QAEI,OAAO,EAAA,IADT;QAEE,2BAA2B,EAAE,KAF/B;QAGE,eAAe,EAAE8E,eAHnB;QAIE,oBAAoB,EAAEC,oBAJxB;QAKE,cAAc,EAAG3D,CAAAA,KAAD,GAAWA,KAAK,CAAC8D,cAAN,EAL7B;QAAA;QAME,SAAS,EAAElF,OAAX;KANF,EAAA,aAQE,CAAA,oBAAA,CAAC,cAAD,EARF,oCAAA,CAAA;QASI,YAAA,EAAYU,OAAO,CAACT,cAApB;KADF,EAEMf,WAFN,EAGMqD,YAHN,EAAA;QAIE,GAAG,EAAE/B,YAJP;QAKE,KAAK,EAAE;YACL,GAAG+B,YAAY,CAAC4C,KADX;YAIH,0CAAA,EAA4C,sCAD3C;YAED,yCAAA,EAA2C,qCAF1C;YAGD,0CAAA,EAA4C,sCAH3C;YAID,+BAAA,EAAiC,kCAJhC;YAKD,gCAAA,EAAkC,mCAAlC;SARG;KALT,CAAA,EAAA,aAiBE,CAAA,oBAAA,CAAC,gBAAD,EAAA,IAAA,EAAY1H,QAAZ,CAjBF,EAAA,aAkBE,CAAA,oBAAA,CAAC,0DAAD,EAlBF;QAkBwC,KAAK,EAAEJ,cAA7C;QAA6D,QAAQ,EAAE,IAAV;KAA7D,EAAA,aACE,CAAA,oBAAA,CAAC,YAAD,EADF;QACgC,EAAE,EAAEqD,OAAO,CAACrB,SAA1C;QAAqD,IAAI,EAAC,SAAL;KAArD,EACGwF,SAAS,IAAIpH,QADhB,CADF,CAlBF,CARF,CADF,CA4BQ;CA5De,CAA3B,AAmEG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2H,gCAAU,GAAG,cAAnB,AAAA;AAMA,MAAM5J,yCAAY,GAAA,aAAGO,CAAAA,iBAAA,CACnB,CAACqB,KAAD,EAAwCoD,YAAxC,GAAyD;IACvD,MAAM,E,gBAAEnD,cAAF,CAAA,EAAkB,GAAGgI,UAAH,EAAlB,GAAoCjI,KAA1C,AAAM;IACN,MAAM8B,WAAW,GAAGpC,oCAAc,CAACO,cAAD,CAAlC,AAAA;IACA,MAAMiI,4BAA4B,GAAGZ,qDAA+B,CAClEU,gCADkE,EAElE/H,cAFkE,CAApE,AAHuD,EAOvD,iFAJA;IAKA,+DAAA;IACA,OAAOiI,4BAA4B,CAACX,QAA7B,GAAwC,IAAxC,GAAA,aACL,CAAA,oBAAA,CAAC,YAAD,EAAA,oCAAA,CAAA,EAAA,EAA2BzF,WAA3B,EAA4CmG,UAA5C,EADF;QAC0D,GAAG,EAAE7E,YAAL;KAAxD,CAAA,CADF,CACE;CAXe,CAArB,AAaG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,SAAS+C,yCAAT,CAA6BgC,KAA7B,EAA2CC,IAA3C,EAA0D;IACxD,MAAMC,GAAG,GAAGC,IAAI,CAACC,GAAL,CAASH,IAAI,CAACC,GAAL,GAAWF,KAAK,CAACnC,CAA1B,CAAZ,AAAA;IACA,MAAMwC,MAAM,GAAGF,IAAI,CAACC,GAAL,CAASH,IAAI,CAACI,MAAL,GAAcL,KAAK,CAACnC,CAA7B,CAAf,AAAA;IACA,MAAMyC,KAAK,GAAGH,IAAI,CAACC,GAAL,CAASH,IAAI,CAACK,KAAL,GAAaN,KAAK,CAACrC,CAA5B,CAAd,AAAA;IACA,MAAM4C,IAAI,GAAGJ,IAAI,CAACC,GAAL,CAASH,IAAI,CAACM,IAAL,GAAYP,KAAK,CAACrC,CAA3B,CAAb,AAAA;IAEA,OAAQwC,IAAI,CAACK,GAAL,CAASN,GAAT,EAAcG,MAAd,EAAsBC,KAAtB,EAA6BC,IAA7B,CAAR;QACE,KAAKA,IAAL;YACE,OAAO,MAAP,CAAA;QACF,KAAKD,KAAL;YACE,OAAO,OAAP,CAAA;QACF,KAAKJ,GAAL;YACE,OAAO,KAAP,CAAA;QACF,KAAKG,MAAL;YACE,OAAO,QAAP,CAAA;QACF;YACE,OAAO,IAAP,CAAA;KAVJ;CAYD;AAED,SAAS/B,uCAAT,CAA2B2B,IAA3B,EAA0C;IACxC,MAAM,E,KAAEC,GAAF,CAAA,E,OAAOI,KAAP,CAAA,E,QAAcD,MAAd,CAAA,E,MAAsBE,IAAAA,CAAAA,EAAtB,GAA+BN,IAArC,AAAM;IACN,OAAO;QACL;YAAEtC,CAAC,EAAE4C,IAAL;YAAW1C,CAAC,EAAEqC,GAAHrC;SADN;QAEL;YAAEF,CAAC,EAAE2C,KAAL;YAAYzC,CAAC,EAAEqC,GAAHrC;SAFP;QAGL;YAAEF,CAAC,EAAE2C,KAAL;YAAYzC,CAAC,EAAEwC,MAAHxC;SAHP;QAIL;YAAEF,CAAC,EAAE4C,IAAL;YAAW1C,CAAC,EAAEwC,MAAHxC;SAJN;KAAP,CAIE;C,CAIJ,+CAFC;AAGD,wDAAA;AACA,SAASoB,sCAAT,CAA0Be,KAA1B,EAAwCS,OAAxC,EAA0D;IACxD,MAAM,E,GAAE9C,CAAF,CAAA,E,GAAKE,CAAAA,CAAAA,EAAL,GAAWmC,KAAjB,AAAM;IACN,IAAIU,MAAM,GAAG,KAAb,AAAA;IACA,IAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGH,OAAO,CAACI,MAAR,GAAiB,CAArC,EAAwCF,CAAC,GAAGF,OAAO,CAACI,MAApD,EAA4DD,CAAC,GAAGD,CAAC,EAAjE,CAAqE;QACnE,MAAMG,EAAE,GAAGL,OAAO,CAACE,CAAD,CAAP,CAAWhD,CAAtB,AAAA;QACA,MAAMoD,EAAE,GAAGN,OAAO,CAACE,CAAD,CAAP,CAAW9C,CAAtB,AAAA;QACA,MAAMmD,EAAE,GAAGP,OAAO,CAACG,CAAD,CAAP,CAAWjD,CAAtB,AAAA;QACA,MAAMsD,EAAE,GAAGR,OAAO,CAACG,CAAD,CAAP,CAAW/C,CAAtB,AAJmE,EAMnE,kBAFA;QAGA,MAAMqD,SAAS,GAAKH,EAAE,GAAGlD,CAAN,KAAcoD,EAAE,GAAGpD,CAApB,IAA4BF,CAAC,GAAG,AAACqD,CAAAA,EAAE,GAAGF,EAAN,CAAA,GAAajD,CAAAA,CAAC,GAAGkD,EAAjB,CAAA,GAAwBE,CAAAA,EAAE,GAAGF,EAA7B,CAAA,GAAmCD,EAArF,AAAA;QACA,IAAII,SAAJ,EAAeR,MAAM,GAAG,CAACA,MAAV,CAAf;KACD;IAED,OAAOA,MAAP,CAAA;C,CAGF,yFAFC;AAGD,mDAAA;AACA,SAASlC,6BAAT,CAAkC2C,MAAlC,EAAwE;IACtE,MAAMC,SAAmB,GAAGD,MAAM,CAACE,KAAP,EAA5B,AAAA;IACAD,SAAS,CAACE,IAAV,CAAe,CAACC,CAAD,EAAWC,CAAX,GAAwB;QACrC,IAAID,CAAC,CAAC5D,CAAF,GAAM6D,CAAC,CAAC7D,CAAZ,EAAe,OAAO,EAAP,CAAf;aACK,IAAI4D,CAAC,CAAC5D,CAAF,GAAM6D,CAAC,CAAC7D,CAAZ,EAAe,OAAO,CAAP,CAAf;aACA,IAAI4D,CAAC,CAAC1D,CAAF,GAAM2D,CAAC,CAAC3D,CAAZ,EAAe,OAAO,EAAP,CAAf;aACA,IAAI0D,CAAC,CAAC1D,CAAF,GAAM2D,CAAC,CAAC3D,CAAZ,EAAe,OAAO,CAAP,CAAf;aACA,OAAO,CAAP,CAJL;KADF,CAMC,CAAA;IACD,OAAO4D,sCAAgB,CAACL,SAAD,CAAvB,CAAA;C,CAGF,6FAFC;AAGD,SAASK,sCAAT,CAA2CN,MAA3C,EAAiF;IAC/E,IAAIA,MAAM,CAACN,MAAP,IAAiB,CAArB,EAAwB,OAAOM,MAAM,CAACE,KAAP,EAAP,CAAxB;IAEA,MAAMK,SAAmB,GAAG,EAA5B,AAAA;IACA,IAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,MAAM,CAACN,MAA3B,EAAmCF,CAAC,EAApC,CAAwC;QACtC,MAAMgB,CAAC,GAAGR,MAAM,CAACR,CAAD,CAAhB,AAAA;QACA,MAAOe,SAAS,CAACb,MAAV,IAAoB,CAA3B,CAA8B;YAC5B,MAAMe,CAAC,GAAGF,SAAS,CAACA,SAAS,CAACb,MAAV,GAAmB,CAApB,CAAnB,AAAA;YACA,MAAMgB,CAAC,GAAGH,SAAS,CAACA,SAAS,CAACb,MAAV,GAAmB,CAApB,CAAnB,AAAA;YACA,IAAI,AAACe,CAAAA,CAAC,CAACjE,CAAF,GAAMkE,CAAC,CAAClE,CAAT,CAAA,GAAegE,CAAAA,CAAC,CAAC9D,CAAF,GAAMgE,CAAC,CAAChE,CAAvB,CAAA,IAA6B,AAAC+D,CAAAA,CAAC,CAAC/D,CAAF,GAAMgE,CAAC,CAAChE,CAAT,CAAA,GAAe8D,CAAAA,CAAC,CAAChE,CAAF,GAAMkE,CAAC,CAAClE,CAAvB,CAAA,AAAjC,EAA4D+D,SAAS,CAACI,GAAV,EAAA,CAA5D;iBACK,MADL;SAED;QACDJ,SAAS,CAACK,IAAV,CAAeJ,CAAf,CAAAD,CAAAA;KACD;IACDA,SAAS,CAACI,GAAV,EAAAJ,CAAAA;IAEA,MAAMM,SAAmB,GAAG,EAA5B,AAAA;IACA,IAAK,IAAIrB,EAAC,GAAGQ,MAAM,CAACN,MAAP,GAAgB,CAA7B,EAAgCF,EAAC,IAAI,CAArC,EAAwCA,EAAC,EAAzC,CAA6C;QAC3C,MAAMgB,CAAC,GAAGR,MAAM,CAACR,EAAD,CAAhB,AAAA;QACA,MAAOqB,SAAS,CAACnB,MAAV,IAAoB,CAA3B,CAA8B;YAC5B,MAAMe,CAAC,GAAGI,SAAS,CAACA,SAAS,CAACnB,MAAV,GAAmB,CAApB,CAAnB,AAAA;YACA,MAAMgB,CAAC,GAAGG,SAAS,CAACA,SAAS,CAACnB,MAAV,GAAmB,CAApB,CAAnB,AAAA;YACA,IAAI,AAACe,CAAAA,CAAC,CAACjE,CAAF,GAAMkE,CAAC,CAAClE,CAAT,CAAA,GAAegE,CAAAA,CAAC,CAAC9D,CAAF,GAAMgE,CAAC,CAAChE,CAAvB,CAAA,IAA6B,AAAC+D,CAAAA,CAAC,CAAC/D,CAAF,GAAMgE,CAAC,CAAChE,CAAT,CAAA,GAAe8D,CAAAA,CAAC,CAAChE,CAAF,GAAMkE,CAAC,CAAClE,CAAvB,CAAA,AAAjC,EAA4DqE,SAAS,CAACF,GAAV,EAAA,CAA5D;iBACK,MADL;SAED;QACDE,SAAS,CAACD,IAAV,CAAeJ,CAAf,CAAAK,CAAAA;KACD;IACDA,SAAS,CAACF,GAAV,EAAAE,CAAAA;IAEA,IACEN,SAAS,CAACb,MAAV,KAAqB,CAArB,IACAmB,SAAS,CAACnB,MAAV,KAAqB,CADrB,IAEAa,SAAS,CAAC,CAAD,CAAT,CAAa/D,CAAb,KAAmBqE,SAAS,CAAC,CAAD,CAAT,CAAarE,CAFhC,IAGA+D,SAAS,CAAC,CAAD,CAAT,CAAa7D,CAAb,KAAmBmE,SAAS,CAAC,CAAD,CAAT,CAAanE,CAJlC,EAME,OAAO6D,SAAP,CAAA;SAEA,OAAOA,SAAS,CAACO,MAAV,CAAiBD,SAAjB,CAAP,CAAA;CAEH;AAED,MAAM9L,yCAAQ,GAAGN,yCAAjB,AAAA;AACA,MAAMO,yCAAI,GAAGN,yCAAb,AAAA;AACA,MAAMO,yCAAO,GAAGN,yCAAhB,AAAA;AACA,MAAMO,yCAAM,GAAGN,yCAAf,AAAA;AACA,MAAMO,yCAAO,GAAGN,yCAAhB,AAAA;AACA,MAAMO,yCAAK,GAAGN,yCAAd,AAAA;;ADlsBA", "sources": ["packages/react/tooltip/src/index.ts", "packages/react/tooltip/src/Tooltip.tsx"], "sourcesContent": ["export {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n} from './Tooltip';\nexport type {\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n} from './Tooltip';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slottable } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P = {}> = P & { __scopeTooltip?: Scope };\nconst [createTooltipContext, createTooltipScope] = createContextScope('Tooltip', [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'TooltipProvider';\nconst DEFAULT_DELAY_DURATION = 700;\nconst TOOLTIP_OPEN = 'tooltip.open';\n\ntype TooltipProviderContextValue = {\n  isOpenDelayed: boolean;\n  delayDuration: number;\n  onOpen(): void;\n  onClose(): void;\n  onPointerInTransitChange(inTransit: boolean): void;\n  isPointerInTransitRef: React.MutableRefObject<boolean>;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipProviderContextProvider, useTooltipProviderContext] =\n  createTooltipContext<TooltipProviderContextValue>(PROVIDER_NAME);\n\ninterface TooltipProviderProps {\n  children: React.ReactNode;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst TooltipProvider: React.FC<TooltipProviderProps> = (\n  props: ScopedProps<TooltipProviderProps>\n) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children,\n  } = props;\n  const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n\n  return (\n    <TooltipProviderContextProvider\n      scope={__scopeTooltip}\n      isOpenDelayed={isOpenDelayed}\n      delayDuration={delayDuration}\n      onOpen={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        setIsOpenDelayed(false);\n      }, [])}\n      onClose={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => setIsOpenDelayed(true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration])}\n      isPointerInTransitRef={isPointerInTransitRef}\n      onPointerInTransitChange={React.useCallback((inTransit: boolean) => {\n        isPointerInTransitRef.current = inTransit;\n      }, [])}\n      disableHoverableContent={disableHoverableContent}\n    >\n      {children}\n    </TooltipProviderContextProvider>\n  );\n};\n\nTooltipProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLTIP_NAME = 'Tooltip';\n\ntype TooltipContextValue = {\n  contentId: string;\n  open: boolean;\n  stateAttribute: 'closed' | 'delayed-open' | 'instant-open';\n  trigger: TooltipTriggerElement | null;\n  onTriggerChange(trigger: TooltipTriggerElement | null): void;\n  onTriggerEnter(): void;\n  onTriggerLeave(): void;\n  onOpen(): void;\n  onClose(): void;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipContextProvider, useTooltipContext] =\n  createTooltipContext<TooltipContextValue>(TOOLTIP_NAME);\n\ninterface TooltipProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened. This will\n   * override the prop with the same name passed to Provider.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst Tooltip: React.FC<TooltipProps> = (props: ScopedProps<TooltipProps>) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen = false,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp,\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState<HTMLButtonElement | null>(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent =\n    disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: (open) => {\n      if (open) {\n        providerContext.onOpen();\n\n        // as `onChange` is called within a lifecycle method we\n        // avoid dispatching via `dispatchDiscreteCustomEvent`.\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open);\n    },\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? (wasOpenDelayedRef.current ? 'delayed-open' : 'instant-open') : 'closed';\n  }, [open]);\n\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    setOpen(false);\n  }, [setOpen]);\n\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(openTimerRef.current);\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <TooltipContextProvider\n        scope={__scopeTooltip}\n        contentId={contentId}\n        open={open}\n        stateAttribute={stateAttribute}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        onTriggerEnter={React.useCallback(() => {\n          if (providerContext.isOpenDelayed) handleDelayedOpen();\n          else handleOpen();\n        }, [providerContext.isOpenDelayed, handleDelayedOpen, handleOpen])}\n        onTriggerLeave={React.useCallback(() => {\n          if (disableHoverableContent) {\n            handleClose();\n          } else {\n            // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n            window.clearTimeout(openTimerRef.current);\n          }\n        }, [handleClose, disableHoverableContent])}\n        onOpen={handleOpen}\n        onClose={handleClose}\n        disableHoverableContent={disableHoverableContent}\n      >\n        {children}\n      </TooltipContextProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nTooltip.displayName = TOOLTIP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TooltipTrigger';\n\ntype TooltipTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TooltipTriggerProps extends PrimitiveButtonProps {}\n\nconst TooltipTrigger = React.forwardRef<TooltipTriggerElement, TooltipTriggerProps>(\n  (props: ScopedProps<TooltipTriggerProps>, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef<TooltipTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => (isPointerDownRef.current = false), []);\n\n    React.useEffect(() => {\n      return () => document.removeEventListener('pointerup', handlePointerUp);\n    }, [handlePointerUp]);\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          // We purposefully avoid adding `type=button` here because tooltip triggers are also\n          // commonly anchors and the anchor `type` attribute signifies MIME type.\n          aria-describedby={context.open ? context.contentId : undefined}\n          data-state={context.stateAttribute}\n          {...triggerProps}\n          ref={composedRefs}\n          onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n            if (event.pointerType === 'touch') return;\n            if (\n              !hasPointerMoveOpenedRef.current &&\n              !providerContext.isPointerInTransitRef.current\n            ) {\n              context.onTriggerEnter();\n              hasPointerMoveOpenedRef.current = true;\n            }\n          })}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, () => {\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })}\n          onPointerDown={composeEventHandlers(props.onPointerDown, () => {\n            isPointerDownRef.current = true;\n            document.addEventListener('pointerup', handlePointerUp, { once: true });\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            if (!isPointerDownRef.current) context.onOpen();\n          })}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          onClick={composeEventHandlers(props.onClick, context.onClose)}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nTooltipTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'TooltipPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createTooltipContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface TooltipPortalProps extends Omit<PortalProps, 'asChild'> {\n  children?: React.ReactNode;\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipPortal: React.FC<TooltipPortalProps> = (props: ScopedProps<TooltipPortalProps>) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return (\n    <PortalProvider scope={__scopeTooltip} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nTooltipPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TooltipContent';\n\ntype TooltipContentElement = TooltipContentImplElement;\ninterface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipContent = React.forwardRef<TooltipContentElement, TooltipContentProps>(\n  (props: ScopedProps<TooltipContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = 'top', ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.disableHoverableContent ? (\n          <TooltipContentImpl side={side} {...contentProps} ref={forwardedRef} />\n        ) : (\n          <TooltipContentHoverable side={side} {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\n\ntype TooltipContentHoverableElement = TooltipContentImplElement;\ninterface TooltipContentHoverableProps extends TooltipContentImplProps {}\n\nconst TooltipContentHoverable = React.forwardRef<\n  TooltipContentHoverableElement,\n  TooltipContentHoverableProps\n>((props: ScopedProps<TooltipContentHoverableProps>, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef<TooltipContentHoverableElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState<Polygon | null>(null);\n\n  const { trigger, onClose } = context;\n  const content = ref.current;\n\n  const { onPointerInTransitChange } = providerContext;\n\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n\n  const handleCreateGraceArea = React.useCallback(\n    (event: PointerEvent, hoverTarget: HTMLElement) => {\n      const currentTarget = event.currentTarget as HTMLElement;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n\n      const bleed = exitSide === 'right' || exitSide === 'bottom' ? -5 : 5;\n      const isXAxis = exitSide === 'right' || exitSide === 'left';\n      const startPoint = isXAxis\n        ? { x: event.clientX + bleed, y: event.clientY }\n        : { x: event.clientX, y: event.clientY + bleed };\n\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([startPoint, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, trigger);\n\n      trigger.addEventListener('pointerleave', handleTriggerLeave);\n      content.addEventListener('pointerleave', handleContentLeave);\n      return () => {\n        trigger.removeEventListener('pointerleave', handleTriggerLeave);\n        content.removeEventListener('pointerleave', handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        const target = event.target as HTMLElement;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener('pointermove', handleTrackPointerGrace);\n      return () => document.removeEventListener('pointermove', handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n\n  return <TooltipContentImpl {...props} ref={composedRefs} />;\n});\n\nconst [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] =\n  createTooltipContext(TOOLTIP_NAME, { isInside: false });\n\ntype TooltipContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface TooltipContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * A more descriptive label for accessibility purpose\n   */\n  'aria-label'?: string;\n\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `Tooltip`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n}\n\nconst TooltipContentImpl = React.forwardRef<TooltipContentImplElement, TooltipContentImplProps>(\n  (props: ScopedProps<TooltipContentImplProps>, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      'aria-label': ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n\n    // Close this tooltip if another one opens\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n\n    // Close the tooltip if the trigger is scrolled\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event: Event) => {\n          const target = event.target as HTMLElement;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener('scroll', handleScroll, { capture: true });\n        return () => window.removeEventListener('scroll', handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n\n    return (\n      <DismissableLayer\n        asChild\n        disableOutsidePointerEvents={false}\n        onEscapeKeyDown={onEscapeKeyDown}\n        onPointerDownOutside={onPointerDownOutside}\n        onFocusOutside={(event) => event.preventDefault()}\n        onDismiss={onClose}\n      >\n        <PopperPrimitive.Content\n          data-state={context.stateAttribute}\n          {...popperScope}\n          {...contentProps}\n          ref={forwardedRef}\n          style={{\n            ...contentProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n              '--radix-tooltip-content-transform-origin': 'var(--radix-popper-transform-origin)',\n              '--radix-tooltip-content-available-width': 'var(--radix-popper-available-width)',\n              '--radix-tooltip-content-available-height': 'var(--radix-popper-available-height)',\n              '--radix-tooltip-trigger-width': 'var(--radix-popper-anchor-width)',\n              '--radix-tooltip-trigger-height': 'var(--radix-popper-anchor-height)',\n            },\n          }}\n        >\n          <Slottable>{children}</Slottable>\n          <VisuallyHiddenContentContextProvider scope={__scopeTooltip} isInside={true}>\n            <VisuallyHiddenPrimitive.Root id={context.contentId} role=\"tooltip\">\n              {ariaLabel || children}\n            </VisuallyHiddenPrimitive.Root>\n          </VisuallyHiddenContentContextProvider>\n        </PopperPrimitive.Content>\n      </DismissableLayer>\n    );\n  }\n);\n\nTooltipContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'TooltipArrow';\n\ntype TooltipArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface TooltipArrowProps extends PopperArrowProps {}\n\nconst TooltipArrow = React.forwardRef<TooltipArrowElement, TooltipArrowProps>(\n  (props: ScopedProps<TooltipArrowProps>, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    // if the arrow is inside the `VisuallyHidden`, we don't want to render it all to\n    // prevent issues in positioning the arrow due to the duplicate\n    return visuallyHiddenContentContext.isInside ? null : (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    );\n  }\n);\n\nTooltipArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect) {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left';\n    case right:\n      return 'right';\n    case top:\n      return 'top';\n    case bottom:\n      return 'bottom';\n    default:\n      return null;\n  }\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ];\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice();\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return +1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return +1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1) return points.slice();\n\n  const upperHull: Array<P> = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n\n  const lowerHull: Array<P> = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n\n  if (\n    upperHull.length === 1 &&\n    lowerHull.length === 1 &&\n    upperHull[0].x === lowerHull[0].x &&\n    upperHull[0].y === lowerHull[0].y\n  ) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\n\nconst Provider = TooltipProvider;\nconst Root = Tooltip;\nconst Trigger = TooltipTrigger;\nconst Portal = TooltipPortal;\nconst Content = TooltipContent;\nconst Arrow = TooltipArrow;\n\nexport {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n};\n"], "names": ["createTooltipScope", "TooltipProvider", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "TooltipPortal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TooltipArrow", "Provider", "Root", "<PERSON><PERSON>", "Portal", "Content", "Arrow", "React", "composeEventHandlers", "useComposedRefs", "createContextScope", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "useId", "PopperPrimitive", "createPopperScope", "PortalPrimitive", "Presence", "Primitive", "Slottable", "useControllableState", "VisuallyHiddenPrimitive", "createTooltipContext", "usePopperScope", "PROVIDER_NAME", "DEFAULT_DELAY_DURATION", "TOOLTIP_OPEN", "TooltipProviderContextProvider", "useTooltipProviderContext", "props", "__scopeTooltip", "delayDuration", "skipDelayDuration", "disableHover<PERSON><PERSON><PERSON>nt", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "isPointerInTransitRef", "useRef", "skip<PERSON>elayTimerRef", "useEffect", "skip<PERSON><PERSON><PERSON>T<PERSON>r", "current", "window", "clearTimeout", "useCallback", "setTimeout", "inTransit", "TOOLTIP_NAME", "TooltipContextProvider", "useTooltipContext", "open", "openProp", "defaultOpen", "onOpenChange", "disableHoverableContentProp", "delayDurationProp", "providerContext", "popperScope", "trigger", "setTrigger", "contentId", "openTimerRef", "wasOpenDelayedRef", "<PERSON><PERSON><PERSON>", "prop", "defaultProp", "onChange", "onOpen", "document", "dispatchEvent", "CustomEvent", "onClose", "stateAttribute", "useMemo", "handleOpen", "handleClose", "handleDelayedOpen", "TRIGGER_NAME", "forwardRef", "forwardedRef", "triggerProps", "context", "ref", "composedRefs", "onTriggerChange", "isPointerDownRef", "hasPointerMoveOpenedRef", "handlePointerUp", "removeEventListener", "undefined", "onPointerMove", "event", "pointerType", "onTriggerEnter", "onPointerLeave", "onTriggerLeave", "onPointerDown", "addEventListener", "once", "onFocus", "onBlur", "onClick", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "container", "CONTENT_NAME", "portalContext", "side", "contentProps", "TooltipContentHoverable", "pointerGraceArea", "setPointerGraceArea", "content", "onPointerInTransitChange", "handleRemoveGraceArea", "handleCreateGraceArea", "hoverTarget", "currentTarget", "exitPoint", "x", "clientX", "y", "clientY", "exitSide", "getExitSideFromRect", "getBoundingClientRect", "bleed", "isXAxis", "startPoint", "hoverTargetPoints", "getPointsFromRect", "grace<PERSON><PERSON>", "getHull", "handleTriggerLeave", "handleContentLeave", "handleTrackPointerGrace", "target", "pointerPosition", "hasEnteredTarget", "contains", "isPointerOutsideGraceArea", "isPointInPolygon", "VisuallyHiddenContentContextProvider", "useVisuallyHiddenContentContext", "isInside", "TooltipContentImpl", "aria<PERSON><PERSON><PERSON>", "onEscapeKeyDown", "onPointerDownOutside", "handleScroll", "capture", "preventDefault", "style", "ARROW_NAME", "arrowProps", "visuallyHiddenContentContext", "point", "rect", "top", "Math", "abs", "bottom", "right", "left", "min", "polygon", "inside", "i", "j", "length", "xi", "yi", "xj", "yj", "intersect", "points", "newPoints", "slice", "sort", "a", "b", "getHullPresorted", "upperHull", "p", "q", "r", "pop", "push", "lowerHull", "concat"], "version": 3, "file": "index.module.js.map"}