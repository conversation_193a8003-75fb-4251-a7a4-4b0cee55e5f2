import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { SAFETY_KEYWORDS } from '@/lib/constants'
import { sanitizeInput } from '@/lib/utils'

// Initialize OpenAI client (optional - will work without API key)
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null

// Safety monitoring function
function detectSafetyRisk(message: string): { riskLevel: string; keywords: string[] } {
  const lowerMessage = message.toLowerCase()
  const detectedKeywords: string[] = []
  let riskLevel = 'low'

  // Check for emergency keywords
  for (const keyword of SAFETY_KEYWORDS.emergency) {
    if (lowerMessage.includes(keyword)) {
      detectedKeywords.push(keyword)
      riskLevel = 'critical'
    }
  }

  // Check for self-harm keywords
  if (riskLevel !== 'critical') {
    for (const keyword of SAFETY_KEYWORDS.selfHarm) {
      if (lowerMessage.includes(keyword)) {
        detectedKeywords.push(keyword)
        riskLevel = 'high'
      }
    }
  }

  // Check for abuse keywords
  if (riskLevel === 'low') {
    for (const keyword of SAFETY_KEYWORDS.abuse) {
      if (lowerMessage.includes(keyword)) {
        detectedKeywords.push(keyword)
        riskLevel = 'high'
      }
    }
  }

  // Check for distress keywords
  if (riskLevel === 'low') {
    for (const keyword of SAFETY_KEYWORDS.distress) {
      if (lowerMessage.includes(keyword)) {
        detectedKeywords.push(keyword)
        riskLevel = 'medium'
      }
    }
  }

  return { riskLevel, keywords: detectedKeywords }
}

// System prompt for the AI counselor
const SYSTEM_PROMPT = `You are a compassionate AI mental health counselor specializing in supporting women and children. Your role is to:

1. Provide empathetic, non-judgmental emotional support
2. Offer practical coping strategies and techniques
3. Validate feelings and experiences
4. Encourage professional help when appropriate
5. Maintain a safe, supportive environment

Guidelines:
- Always prioritize safety and well-being
- Use age-appropriate language
- Be culturally sensitive
- Never provide medical diagnoses
- Encourage professional help for serious concerns
- If someone mentions self-harm or abuse, provide crisis resources immediately

Remember: You are here to listen, support, and guide - not to replace professional therapy or medical care.`

export async function POST(request: NextRequest) {
  try {
    const { message, sessionId, userId } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // Sanitize input
    const sanitizedMessage = sanitizeInput(message)

    // Perform safety monitoring
    const safetyCheck = detectSafetyRisk(sanitizedMessage)

    // If critical risk detected, provide immediate crisis resources
    if (safetyCheck.riskLevel === 'critical') {
      const crisisResponse = {
        role: 'assistant',
        content: `I'm very concerned about what you've shared. Your safety is the most important thing right now. Please reach out for immediate help:

🚨 **Emergency Services: Call 911**
📞 **Crisis Hotline: Call 988** (Suicide & Crisis Lifeline)
💬 **Crisis Text Line: Text HOME to 741741**

You don't have to go through this alone. There are people who want to help you right now. Please reach out to one of these resources immediately.

Would you like me to help you find local emergency resources or someone you can talk to?`,
        timestamp: new Date().toISOString(),
        safetyAlert: {
          riskLevel: safetyCheck.riskLevel,
          keywords: safetyCheck.keywords
        }
      }

      return NextResponse.json({
        success: true,
        data: crisisResponse
      })
    }

    // Generate AI response using OpenAI
    let aiResponse = ''

    if (openai) {
      try {
        const completion = await openai.chat.completions.create({
          model: 'gpt-4',
          messages: [
            { role: 'system', content: SYSTEM_PROMPT },
            { role: 'user', content: sanitizedMessage }
          ],
          max_tokens: 500,
          temperature: 0.7,
        })

        aiResponse = completion.choices[0]?.message?.content || 'I understand you\'re reaching out. Can you tell me more about how you\'re feeling?'
      } catch (openaiError) {
        console.error('OpenAI API error:', openaiError)
        // Fallback response if OpenAI fails
        aiResponse = 'I hear you and I\'m here to listen. Can you tell me more about what\'s on your mind today?'
      }
    } else {
      // Fallback response when no API key is configured
      aiResponse = 'Thank you for sharing with me. I\'m here to support you. Can you tell me more about how you\'re feeling right now?'
    }

    // Add safety resources if medium or high risk
    if (safetyCheck.riskLevel === 'high') {
      aiResponse += '\n\n**If you need immediate support:**\n📞 Crisis Hotline: 988\n💬 Text HOME to 741741'
    } else if (safetyCheck.riskLevel === 'medium') {
      aiResponse += '\n\n**Remember:** If you ever need immediate help, call 988 or text HOME to 741741'
    }

    const response = {
      role: 'assistant',
      content: aiResponse,
      timestamp: new Date().toISOString(),
      safetyAlert: safetyCheck.riskLevel !== 'low' ? {
        riskLevel: safetyCheck.riskLevel,
        keywords: safetyCheck.keywords
      } : undefined
    }

    return NextResponse.json({
      success: true,
      data: response
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
