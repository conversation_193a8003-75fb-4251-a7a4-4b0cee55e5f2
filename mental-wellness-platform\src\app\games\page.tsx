'use client'

import { useState } from 'react'
import { PlayI<PERSON>, ClockIcon, StarIcon, UserGroupIcon } from '@heroicons/react/24/outline'
import { GAME_CATEGORIES, AGE_GROUPS } from '@/lib/constants'

interface Game {
  id: string
  title: string
  description: string
  category: keyof typeof GAME_CATEGORIES
  ageGroup: keyof typeof AGE_GROUPS | 'all'
  duration: number
  difficulty: 'easy' | 'medium' | 'hard'
  thumbnail: string
  rating: number
  players: number
  benefits: string[]
}

const games: Game[] = [
  {
    id: '1',
    title: 'Breathing Garden',
    description: 'A peaceful breathing exercise game where you help flowers bloom by following breathing patterns.',
    category: 'stress_relief',
    ageGroup: 'all',
    duration: 5,
    difficulty: 'easy',
    thumbnail: '🌸',
    rating: 4.8,
    players: 1,
    benefits: ['Reduces anxiety', 'Improves focus', 'Calms mind']
  },
  {
    id: '2',
    title: 'Emotion Detective',
    description: 'Learn to identify and understand different emotions through interactive scenarios.',
    category: 'emotional_regulation',
    ageGroup: 'child',
    duration: 15,
    difficulty: 'easy',
    thumbnail: '🕵️‍♀️',
    rating: 4.6,
    players: 1,
    benefits: ['Emotional awareness', 'Empathy building', 'Social skills']
  },
  {
    id: '3',
    title: 'Mindful Maze',
    description: 'Navigate through calming mazes while practicing mindfulness techniques.',
    category: 'mindfulness',
    ageGroup: 'teen',
    duration: 10,
    difficulty: 'medium',
    thumbnail: '🧩',
    rating: 4.7,
    players: 1,
    benefits: ['Mindfulness', 'Problem solving', 'Stress relief']
  },
  {
    id: '4',
    title: 'Coping Castle',
    description: 'Build a castle while learning different coping strategies for difficult situations.',
    category: 'coping_skills',
    ageGroup: 'all',
    duration: 20,
    difficulty: 'medium',
    thumbnail: '🏰',
    rating: 4.9,
    players: 1,
    benefits: ['Coping strategies', 'Resilience', 'Problem solving']
  },
  {
    id: '5',
    title: 'Worry Warriors',
    description: 'Transform worries into positive thoughts in this empowering adventure game.',
    category: 'emotional_regulation',
    ageGroup: 'teen',
    duration: 25,
    difficulty: 'hard',
    thumbnail: '⚔️',
    rating: 4.5,
    players: 1,
    benefits: ['Anxiety management', 'Positive thinking', 'Self-confidence']
  },
  {
    id: '6',
    title: 'Gratitude Garden',
    description: 'Plant and grow a beautiful garden by expressing gratitude and positive thoughts.',
    category: 'mindfulness',
    ageGroup: 'all',
    duration: 12,
    difficulty: 'easy',
    thumbnail: '🌻',
    rating: 4.8,
    players: 1,
    benefits: ['Gratitude practice', 'Positive mindset', 'Emotional wellbeing']
  }
]

export default function GamesPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedAgeGroup, setSelectedAgeGroup] = useState<string>('all')

  const filteredGames = games.filter(game => {
    const categoryMatch = selectedCategory === 'all' || game.category === selectedCategory
    const ageMatch = selectedAgeGroup === 'all' || game.ageGroup === selectedAgeGroup || game.ageGroup === 'all'
    return categoryMatch && ageMatch
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'hard': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Therapeutic Games
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Fun, interactive games designed to help manage stress, build coping skills, 
            and promote emotional wellbeing for women and children.
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Categories</option>
                {Object.entries(GAME_CATEGORIES).map(([key, label]) => (
                  <option key={key} value={key}>{label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Age Group
              </label>
              <select
                value={selectedAgeGroup}
                onChange={(e) => setSelectedAgeGroup(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Ages</option>
                {Object.entries(AGE_GROUPS).map(([key, group]) => (
                  <option key={key} value={key}>{group.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Games Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGames.map((game) => (
            <div key={game.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-4xl">{game.thumbnail}</div>
                  <div className="flex items-center space-x-1">
                    <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">{game.rating}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {game.title}
                </h3>
                
                <p className="text-gray-600 mb-4 text-sm">
                  {game.description}
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(game.difficulty)}`}>
                    {game.difficulty}
                  </span>
                  <span className="px-2 py-1 rounded-full text-xs font-medium text-purple-600 bg-purple-100">
                    {GAME_CATEGORIES[game.category]}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {game.duration} min
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="h-4 w-4 mr-1" />
                    {game.players} player
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Benefits:</h4>
                  <div className="flex flex-wrap gap-1">
                    {game.benefits.map((benefit, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => {
                    if (game.id === '1') {
                      window.location.href = '/games/breathing-garden'
                    } else {
                      alert('This game is coming soon! Try the Breathing Garden for now.')
                    }
                  }}
                  className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors flex items-center justify-center"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  {game.id === '1' ? 'Play Now' : 'Coming Soon'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredGames.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No games found for the selected filters.</p>
          </div>
        )}

        {/* Info Section */}
        <div className="mt-16 bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">How Therapeutic Games Help</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-2">🧠</div>
              <h3 className="font-semibold text-gray-900 mb-2">Cognitive Skills</h3>
              <p className="text-sm text-gray-600">Improve problem-solving, memory, and focus through engaging gameplay.</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">💝</div>
              <h3 className="font-semibold text-gray-900 mb-2">Emotional Regulation</h3>
              <p className="text-sm text-gray-600">Learn to identify, understand, and manage emotions effectively.</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">🌱</div>
              <h3 className="font-semibold text-gray-900 mb-2">Stress Relief</h3>
              <p className="text-sm text-gray-600">Reduce anxiety and stress through calming, mindful activities.</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">💪</div>
              <h3 className="font-semibold text-gray-900 mb-2">Coping Skills</h3>
              <p className="text-sm text-gray-600">Build resilience and learn healthy ways to handle challenges.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
