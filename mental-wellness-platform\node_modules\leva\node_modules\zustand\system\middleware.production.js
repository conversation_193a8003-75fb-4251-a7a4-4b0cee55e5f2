System.register([],function(O){"use strict";return{execute:function(){O("devtools",A);var P=Object.defineProperty,E=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable,T=(n,t,e)=>t in n?P(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,x=(n,t)=>{for(var e in t||(t={}))N.call(t,e)&&T(n,e,t[e]);if(E)for(var e of E(t))D.call(t,e)&&T(n,e,t[e]);return n};const C=O("redux",(n,t)=>(e,i,s)=>(s.dispatch=a=>(e(p=>n(p,a),!1,a),a),s.dispatchFromDevtools=!0,x({dispatch:(...a)=>s.dispatch(...a)},t)));function A(n,t){return(e,i,s)=>{var a;let p=!1;typeof t=="string"&&!p&&(console.warn("[zustand devtools middleware]: passing `name` as directly will be not allowed in next majorpass the `name` in an object `{ name: ... }` instead"),p=!0);const u=t===void 0?{name:void 0,anonymousActionType:void 0}:typeof t=="string"?{name:t}:t;typeof((a=u==null?void 0:u.serialize)==null?void 0:a.options)<"u"&&console.warn("[zustand devtools middleware]: `serialize.options` is deprecated, just use `serialize`");let f;try{f=window.__REDUX_DEVTOOLS_EXTENSION__||window.top.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!f)return n(e,i,s);let l=Object.create(f.connect(u)),v=!1;Object.defineProperty(s,"devtools",{get:()=>(v||(console.warn("[zustand devtools middleware] `devtools` property on the store is deprecated it will be removed in the next major.\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly"),v=!0),l),set:d=>{v||(console.warn("[zustand devtools middleware] `api.devtools` is deprecated, it will be removed in the next major.\nYou shouldn't interact with the extension directly. But in case you still want to you can patch `window.__REDUX_DEVTOOLS_EXTENSION__` directly"),v=!0),l=d}});let y=!1;Object.defineProperty(l,"prefix",{get:()=>(y||(console.warn("[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\nWe no longer prefix the actions/names"+u.name===void 0?", pass the `name` option to create a separate instance of devtools for each store.":", because the `name` option already creates a separate instance of devtools for each store."),y=!0),""),set:()=>{y||(console.warn("[zustand devtools middleware] along with `api.devtools`, `api.devtools.prefix` is deprecated.\nWe no longer prefix the actions/names"+u.name===void 0?", pass the `name` option to create a separate instance of devtools for each store.":", because the `name` option already creates a separate instance of devtools for each store."),y=!0)}});let h=!0;s.setState=(d,r,o)=>{e(d,r),h&&l.send(o===void 0?{type:u.anonymousActionType||"anonymous"}:typeof o=="string"?{type:o}:o,i())};const w=(...d)=>{const r=h;h=!1,e(...d),h=r},b=n(s.setState,i,s);if(l.init(b),s.dispatchFromDevtools&&typeof s.dispatch=="function"){let d=!1;const r=s.dispatch;s.dispatch=(...o)=>{o[0].type==="__setState"&&!d&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),d=!0),r(...o)}}return l.subscribe(d=>{var r;switch(d.type){case"ACTION":if(typeof d.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return _(d.payload,o=>{if(o.type==="__setState"){w(o.state);return}!s.dispatchFromDevtools||typeof s.dispatch=="function"&&s.dispatch(o)});case"DISPATCH":switch(d.payload.type){case"RESET":return w(b),l.init(s.getState());case"COMMIT":return l.init(s.getState());case"ROLLBACK":return _(d.state,o=>{w(o),l.init(s.getState())});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return _(d.state,o=>{w(o)});case"IMPORT_STATE":{const{nextLiftedState:o}=d.payload,c=(r=o.computedStates.slice(-1)[0])==null?void 0:r.state;if(!c)return;w(c),l.send(null,o);return}case"PAUSE_RECORDING":return h=!h}return}}),b}}const _=(n,t)=>{let e;try{e=JSON.parse(n)}catch(i){console.error("[zustand devtools middleware] Could not parse the received json",i)}e!==void 0&&t(e)},L=O("subscribeWithSelector",n=>(t,e,i)=>{const s=i.subscribe;return i.subscribe=(a,p,u)=>{let f=a;if(p){const l=(u==null?void 0:u.equalityFn)||Object.is;let v=a(i.getState());f=y=>{const h=a(y);if(!l(v,h)){const w=v;p(v=h,w)}},u!=null&&u.fireImmediately&&p(v,v)}return s(f)},n(t,e,i)}),k=O("combine",(n,t)=>(e,i,s)=>Object.assign({},n,t(e,i,s)));var R=Object.defineProperty,z=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable,j=(n,t,e)=>t in n?R(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e,g=(n,t)=>{for(var e in t||(t={}))U.call(t,e)&&j(n,e,t[e]);if(z)for(var e of z(t))X.call(t,e)&&j(n,e,t[e]);return n};const S=n=>t=>{try{const e=n(t);return e instanceof Promise?e:{then(i){return S(i)(e)},catch(i){return this}}}catch(e){return{then(i){return this},catch(i){return S(i)(e)}}}},F=O("persist",(n,t)=>(e,i,s)=>{let a=g({getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:r=>r,version:0,merge:(r,o)=>g(g({},o),r)},t);(a.blacklist||a.whitelist)&&console.warn(`The ${a.blacklist?"blacklist":"whitelist"} option is deprecated and will be removed in the next version. Please use the 'partialize' option instead.`);let p=!1;const u=new Set,f=new Set;let l;try{l=a.getStorage()}catch{}if(l)l.removeItem||console.warn(`[zustand persist middleware] The given storage for item '${a.name}' does not contain a 'removeItem' method, which will be required in v4.`);else return n((...r)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),e(...r)},i,s);const v=S(a.serialize),y=()=>{const r=a.partialize(g({},i()));a.whitelist&&Object.keys(r).forEach(m=>{var I;!((I=a.whitelist)!=null&&I.includes(m))&&delete r[m]}),a.blacklist&&a.blacklist.forEach(m=>delete r[m]);let o;const c=v({state:r,version:a.version}).then(m=>l.setItem(a.name,m)).catch(m=>{o=m});if(o)throw o;return c},h=s.setState;s.setState=(r,o)=>{h(r,o),y()};const w=n((...r)=>{e(...r),y()},i,s);let b;const d=()=>{var r;if(!l)return;p=!1,u.forEach(c=>c(i()));const o=((r=a.onRehydrateStorage)==null?void 0:r.call(a,i()))||void 0;return S(l.getItem.bind(l))(a.name).then(c=>{if(c)return a.deserialize(c)}).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==a.version){if(a.migrate)return a.migrate(c.state,c.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return c.state}).then(c=>{var m;return b=a.merge(c,(m=i())!=null?m:w),e(b,!0),y()}).then(()=>{o==null||o(b,void 0),p=!0,f.forEach(c=>c(b))}).catch(c=>{o==null||o(void 0,c)})};return s.persist={setOptions:r=>{a=g(g({},a),r),r.getStorage&&(l=r.getStorage())},clearStorage:()=>{var r;(r=l==null?void 0:l.removeItem)==null||r.call(l,a.name)},rehydrate:()=>d(),hasHydrated:()=>p,onHydrate:r=>(u.add(r),()=>{u.delete(r)}),onFinishHydration:r=>(f.add(r),()=>{f.delete(r)})},d(),b||w})}}});
