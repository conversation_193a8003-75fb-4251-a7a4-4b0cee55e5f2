{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40react-three/fiber/dist/events-cf57b220.esm.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport { createWithEqualityFn } from 'zustand/traditional';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useFiber, useContextBridge, traverseFiber } from 'its-fine';\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\n * Returns the instance's initial (outmost) root.\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n/**\n * Safely flush async effects when testing, simulating a legacy root.\n * @deprecated Import from React instead. import { act } from 'react'\n */\n// Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = React['act' + ''];\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = value => value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\nconst useIsomorphicLayoutEffect = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && (((_window$document = window.document) == null ? void 0 : _window$document.createElement) || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative'))() ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\n * Bridges renderer Context and StrictMode from a primary renderer.\n */\nfunction useBridge() {\n  const fiber = useFiber();\n  const ContextBridge = useContextBridge();\n  return React.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!traverseFiber(fiber, true, node => node.type === React.StrictMode);\n    const Root = strict ? React.StrictMode : React.Fragment;\n    return /*#__PURE__*/jsx(Root, {\n      children: /*#__PURE__*/jsx(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\n\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */(_ErrorBoundary => (_ErrorBoundary = class ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}, _ErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n}), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\n * Returns instance root state\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  nul: a => a === null,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {},\n    meshes: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n      if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) object.__r3f = instance;\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  target = root;\n  for (const part of key.split('-')) {\n    var _target;\n    key = part;\n    root = target;\n    target = (_target = target) == null ? void 0 : _target[key];\n  }\n\n  // TODO: change key to 'foo-bar' if target is undefined?\n\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n  let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n  try {\n    if (!ctor) {\n      ctor = new root.constructor();\n      MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n    }\n  } catch (e) {\n    // ...\n  }\n  return ctor;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      const ctor = getMemoizedPrototype(root);\n      if (!is.und(ctor)) changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenColorMap', 'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  var _instance$object;\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n      continue;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Layers must be written to the mask property\n    if (target instanceof THREE.Layers && value instanceof THREE.Layers) {\n      target.mask = value.mask;\n    }\n    // Set colors if valid color representation for automatic conversion (copy)\n    else if (target instanceof THREE.Color && isColorRepresentation(value)) {\n      target.set(value);\n    }\n    // Copy if properties match signatures and implement math interface (likely read-only)\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n      target.copy(value);\n    }\n    // Set array types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n      if (typeof target.fromArray === 'function') target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n      // Allow setting array scalars\n      if (typeof target.setScalar === 'function') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n    }\n    // Else, just overwrite the value\n    else {\n      var _root$key;\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === THREE.RGBAFormat && root[key].type === THREE.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        root[key].colorSpace = THREE.SRGBColorSpace;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n    const object = instance.object;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && object.raycast !== null) {\n      rootState.internal.interaction.push(object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';else if (instance.object.isMaterial) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\n * Release pointer captures.\n * This is called by releasePointerCapture in the API, and when an object is removed.\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        let state = getRootState(hit.object);\n\n        // If the object is not managed by R3F, it might be parented to an element which is.\n        // Traverse upwards until we find a managed parent and use its state instead.\n        if (!state) {\n          hit.object.traverseAncestors(obj => {\n            const parentState = getRootState(obj);\n            if (parentState) {\n              state = parentState;\n              return false;\n            }\n          });\n        }\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\n        MAYBE TODO, DELETE IF NOT: \n          Check if the object is captured, captured events should not have intersects running in parallel\n          But wouldn't it be better to just replace capturedMap with a single entry?\n          Also, are we OK with straight up making picking up multiple objects impossible?\n          \n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \n        if (pointerId !== undefined) {\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\n          if (capturedMeshSet) {\n            const captured = capturedMeshSet.get(eventObject)\n            if (captured && captured.localState.stopped) return\n          }\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /* @__PURE__ */React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = createWithEqualityFn((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/React.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\n * Exposes an object's {@link Instance}.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\n *\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  React.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\n */\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\n * Executes a callback before render in a shared frame loop.\n * Can order effects with render priority or manually render with a positive priority.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\n * Returns a node graph of an object with named nodes & materials.\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor$1(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\n * Synchronously loads and caches assets with a three loader.\n *\n * Note: this hook's caller must be wrapped with `React.Suspense`\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\n * Preloads an asset into cache as a side-effect.\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\n * Removes a loaded asset from cache.\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([loader, ...keys]);\n};\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nfunction createReconciler(config) {\n  const reconciler = Reconciler(config);\n  reconciler.injectIntoDevTools({\n    bundleType: typeof process !== 'undefined' && process.env.NODE_ENV !== 'production' ? 1 : 0,\n    rendererPackageName: '@react-three/fiber',\n    version: React.version\n  });\n  return reconciler;\n}\nconst NoEventPriority = 0;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = type => `${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = toPascalCase(type);\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  // Remove three* prefix from elements if native element not present\n  type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const target = catalogue[toPascalCase(child.type)];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n  }\n\n  // Set initial props\n  applyProps(child.object, child.props);\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      // If the child is already in the parent's children array, move it to the new position\n      // Otherwise, just insert it at the target position\n      const existingIndex = parent.object.children.indexOf(child.object);\n      if (existingIndex !== -1) {\n        parent.object.children.splice(existingIndex, 1);\n        const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n        parent.object.children.splice(adjustedIndex, 0, child.object);\n      } else {\n        child.object.parent = parent.object;\n        parent.object.children.splice(childIndex, 0, child.object);\n        child.object.dispatchEvent({\n          type: 'added'\n        });\n        parent.object.dispatchEvent({\n          type: 'childadded',\n          child: child.object\n        });\n      }\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else unstable_scheduleCallback(unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const target = catalogue[toPascalCase(instance.type)];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, make it no-op\nconst handleTextInstance = () => {};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition: () => false,\n  trackSchedulerEvent: () => {},\n  resolveEventType: () => null,\n  resolveEventTimeStamp: () => -1.1,\n  requestPostPaintCallback() {},\n  maySuspendCommit: () => false,\n  preloadInstance: () => true,\n  // true indicates already loaded\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady: () => null,\n  NotPendingTransition: null,\n  HostTransitionContext: /* @__PURE__ */React.createContext(null),\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return ContinuousEventPriority;\n      default:\n        return DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\n\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let lastCamera;\n  let configured = false;\n  let pending = null;\n  return {\n    async configure(props = {}) {\n      let resolve;\n      pending = new Promise(_resolve => resolve = _resolve);\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) {\n        const defaultProps = {\n          canvas: canvas,\n          powerPreference: 'high-performance',\n          antialias: true,\n          alpha: true\n        };\n        const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n        if (isRenderer(customRenderer)) {\n          gl = customRenderer;\n        } else {\n          gl = new THREE.WebGLRenderer({\n            ...defaultProps,\n            ...glConfig\n          });\n        }\n        state.set({\n          gl\n        });\n      }\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new THREE.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n      THREE.ColorManagement.enabled = !legacy;\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        gl.outputColorSpace = linear ? THREE.LinearSRGBColorSpace : THREE.SRGBColorSpace;\n        gl.toneMapping = flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping;\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      resolve();\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured && !pending) this.configure();\n      pending.then(() => {\n        reconciler.updateContainer( /*#__PURE__*/jsx(Provider, {\n          store: store,\n          children: children,\n          onCreated: onCreated,\n          rootElement: canvas\n        }), fiber, null, () => undefined);\n      });\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notify that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\n   *  <Canvas>\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = React.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = createWithEqualityFn((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return (\n    /*#__PURE__*/\n    // @ts-ignore, reconciler types are not maintained\n    jsx(Fragment, {\n      children: reconciler.createPortal( /*#__PURE__*/jsx(context.Provider, {\n        value: usePortalStore,\n        children: children\n      }), usePortalStore, null)\n    })\n  );\n}\n\n/**\n * Force React to flush any updates inside the provided callback synchronously and immediately.\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\n * having to revert to a non-React solution.\n */\nfunction flushSync(fn) {\n  return reconciler.flushSync(fn);\n}\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\n * Adds a global render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\n * Adds a global after-render callback which is called each frame.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\n * Adds a global callback which is called when rendering stops.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = false;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      if (events.handlers) {\n        for (const name in events.handlers) {\n          const event = events.handlers[name];\n          const [eventName, passive] = DOM_EVENTS[name];\n          target.addEventListener(eventName, event, {\n            passive\n          });\n        }\n      }\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        if (events.handlers) {\n          for (const name in events.handlers) {\n            const event = events.handlers[name];\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        }\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\nexport { useStore as A, Block as B, useThree as C, useFrame as D, ErrorBoundary as E, useGraph as F, useLoader as G, _roots as _, useMutableCallback as a, useIsomorphicLayoutEffect as b, createRoot as c, unmountComponentAtNode as d, extend as e, createPointerEvents as f, createEvents as g, flushGlobalEffects as h, isRef as i, addEffect as j, addAfterEffect as k, addTail as l, invalidate as m, advance as n, createPortal as o, flushSync as p, context as q, reconciler as r, applyProps as s, threeTypes as t, useBridge as u, getRootState as v, dispose as w, act as x, buildGraph as y, useInstanceHandle as z };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAstCuB;AAttCvB;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;IAC1C,WAAW;AACb;AAEA;;CAEC,GACD,SAAS,gBAAgB,QAAQ;IAC/B,IAAI,OAAO,SAAS,IAAI;IACxB,MAAO,KAAK,QAAQ,GAAG,YAAY,CAAE,OAAO,KAAK,QAAQ,GAAG,YAAY;IACxE,OAAO;AACT;AACA;;;CAGC,GACD,+DAA+D;AAC/D,kDAAkD;AAClD,MAAM,MAAM,6JAAK,CAAC,QAAQ,GAAG;AAC7B,MAAM,uBAAuB,CAAA,MAAO,OAAO,IAAI,oBAAoB;AACnE,MAAM,QAAQ,CAAA,MAAO,OAAO,IAAI,cAAc,CAAC;AAC/C,MAAM,wBAAwB,CAAA,QAAS,SAAS,QAAQ,CAAC,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,OAAO;AAEhI;;;;;;;;CAQC,GACD,MAAM,4BAA4B,aAAa,GAAE,CAAC,CAAC,kBAAkB,oBAAsB,OAAO,WAAW,eAAe,CAAC,CAAC,CAAC,mBAAmB,OAAO,QAAQ,KAAK,OAAO,KAAK,IAAI,iBAAiB,aAAa,KAAK,CAAC,CAAC,oBAAoB,OAAO,SAAS,KAAK,OAAO,KAAK,IAAI,kBAAkB,OAAO,MAAM,aAAa,CAAC,MAAM,8JAAM,eAAe,GAAG,8JAAM,SAAS;AAC9W,SAAS,mBAAmB,EAAE;IAC5B,MAAM,MAAM,8JAAM,MAAM,CAAC;IACzB;wDAA0B,IAAM,KAAK,CAAC,IAAI,OAAO,GAAG,EAAE;uDAAG;QAAC;KAAG;IAC7D,OAAO;AACT;AACA;;CAEC,GACD,SAAS;IACP,MAAM,QAAQ,CAAA,GAAA,+IAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,gBAAgB,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD;IACrC,OAAO,8JAAM,OAAO;6BAAC;qCAAM;wBAAC,EAC1B,QAAQ,EACT;oBACC,MAAM,SAAS,CAAC,CAAC,CAAA,GAAA,+IAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;6CAAM,CAAA,OAAQ,KAAK,IAAI,KAAK,8JAAM,UAAU;;oBAClF,MAAM,OAAO,SAAS,8JAAM,UAAU,GAAG,8JAAM,QAAQ;oBACvD,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,MAAM;wBAC5B,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,eAAe;4BACxC,UAAU;wBACZ;oBACF;gBACF;;4BAAG;QAAC;QAAO;KAAc;AAC3B;AACA,SAAS,MAAM,KAEd;QAFc,EACb,GAAG,EACJ,GAFc;IAGb;2CAA0B;YACxB,IAAI,IAAI;mDAAQ,IAAM;;YACtB;mDAAO,IAAM,IAAI;;QACnB;0CAAG;QAAC;KAAI;IACR,OAAO;AACT;AAEA,uFAAuF;AACvF,MAAM,gBAAgB,aAAa,GAAE,CAAC,CAAA,iBAAkB,CAAC,iBAAiB,MAAM,sBAAsB,8JAAM,SAAS;QAOnH,kBAAkB,GAAG,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACjB;QACA,SAAS;YACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;QACtD;QAXA,YAAY,GAAG,IAAI,CAAE;YACnB,KAAK,IAAI;YACT,IAAI,CAAC,KAAK,GAAG;gBACX,OAAO;YACT;QACF;IAOF,GAAG,eAAe,wBAAwB,GAAG,IAAM,CAAC;YAClD,OAAO;QACT,CAAC,GAAG,cAAc,CAAC;AACnB,SAAS,aAAa,GAAG;IACvB,IAAI;IACJ,uEAAuE;IACvE,qEAAqE;IACrE,MAAM,SAAS,OAAO,WAAW,cAAc,CAAC,wBAAwB,OAAO,gBAAgB,KAAK,OAAO,wBAAwB,IAAI;IACvI,OAAO,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI;AAC3E;AAEA;;CAEC,GACD,SAAS,aAAa,GAAG;IACvB,IAAI;IACJ,OAAO,CAAC,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ;AACjE;AACA,oCAAoC;AACpC,MAAM,KAAK;IACT,KAAK,CAAA,IAAK,MAAM,OAAO,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,OAAO,MAAM;IACxD,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,MAAM,KAAK;IACrB,KAAK,CAAA,IAAK,MAAM;IAChB,KAAK,CAAA,IAAK,MAAM,OAAO,CAAC;IACxB,KAAI,CAAC,EAAE,CAAC;YAAE,EACR,SAAS,SAAS,EAClB,UAAU,WAAW,EACrB,SAAS,IAAI,EACd,GAJS,iEAIN,CAAC;QACH,wDAAwD;QACxD,IAAI,OAAO,MAAM,OAAO,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO;QACjD,mCAAmC;QACnC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,OAAO,MAAM;QACtD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,IAAI,SAAS,YAAY,aAAa,OAAO,MAAM;QACnD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,IAAI,SAAS,WAAW,aAAa,OAAO,MAAM;QAClD,gEAAgE;QAChE,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,GAAG,OAAO;QACxC,+BAA+B;QAC/B,IAAI;QACJ,mCAAmC;QACnC,IAAK,KAAK,EAAG,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO;QACnC,qCAAqC;QACrC,IAAI,SAAS,WAAW,aAAa,YAAY,WAAW;YAC1D,IAAK,KAAK,SAAS,IAAI,EAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;gBAChD;gBACA,SAAS;YACX,IAAI,OAAO;QACb,OAAO;YACL,IAAK,KAAK,SAAS,IAAI,EAAG,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO;QACtD;QACA,oBAAoB;QACpB,IAAI,GAAG,GAAG,CAAC,IAAI;YACb,kDAAkD;YAClD,IAAI,SAAS,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,KAAK,GAAG,OAAO;YACtD,mDAAmD;YACnD,IAAI,SAAS,OAAO,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,OAAO;YAChF,gCAAgC;YAChC,IAAI,MAAM,GAAG,OAAO;QACtB;QACA,OAAO;IACT;AACF;AAEA,qDAAqD;AACrD,SAAS,WAAW,MAAM;IACxB,MAAM,OAAO;QACX,OAAO,CAAC;QACR,WAAW,CAAC;QACZ,QAAQ,CAAC;IACX;IACA,IAAI,QAAQ;QACV,OAAO,QAAQ,CAAC,CAAA;YACd,IAAI,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG;YACrC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,QAAQ;YACxG,IAAI,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG;QACpE;IACF;IACA,OAAO;AACT;AACA,4CAA4C;AAC5C,SAAS,QAAQ,GAAG;IAClB,IAAI,IAAI,IAAI,KAAK,SAAS,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO;IACpE,IAAK,MAAM,KAAK,IAAK;QACnB,MAAM,OAAO,GAAG,CAAC,EAAE;QACnB,IAAI,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO;IAC3H;AACF;AACA,MAAM,uBAAuB;IAAC;IAAY;IAAO;CAAM;AAEvD,kDAAkD;AAClD,SAAS,iBAAiB,KAAK;IAC7B,MAAM,QAAQ,CAAC;IACf,IAAK,MAAM,OAAO,MAAO;QACvB,IAAI,CAAC,qBAAqB,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IAClE;IACA,OAAO;AACT;AAEA,iEAAiE;AACjE,SAAS,QAAQ,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;IACxC,MAAM,SAAS;IAEf,6BAA6B;IAC7B,IAAI,WAAW,UAAU,OAAO,KAAK,IAAI,OAAO,KAAK;IACrD,IAAI,CAAC,UAAU;QACb,WAAW;YACT;YACA;YACA,QAAQ;YACR,UAAU,EAAE;YACZ,OAAO,iBAAiB;YACxB;YACA,YAAY;YACZ,UAAU,CAAC;YACX,UAAU;QACZ;QACA,IAAI,QAAQ,OAAO,KAAK,GAAG;IAC7B;IACA,OAAO;AACT;AACA,SAAS,QAAQ,IAAI,EAAE,GAAG;IACxB,IAAI,SAAS,IAAI,CAAC,IAAI;IACtB,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM,OAAO;QAC7B;QACA;QACA;IACF;IAEA,yBAAyB;IACzB,SAAS;IACT,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,KAAM;QACjC,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS,CAAC,UAAU,MAAM,KAAK,OAAO,KAAK,IAAI,OAAO,CAAC,IAAI;IAC7D;IAEA,wDAAwD;IAExD,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA,qDAAqD;AACrD,MAAM,cAAc;AACpB,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG;QAC9B,iDAAiD;QACjD,IAAI,YAAY,IAAI,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG;YACxC,MAAM,QAAQ,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa;YACtD,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,QAAQ,OAAO,MAAM,EAAE;YAC3B,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE;QAC/C;QACA,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,QAAQ,OAAO,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI;QAChC,IAAI,CAAC,IAAI,GAAG,MAAM,MAAM;IAC1B,OAAO,IAAI,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG;QACrC,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,MAAM,MAAM;IACvE;AACF;AACA,SAAS,OAAO,MAAM,EAAE,KAAK;IAC3B,IAAI,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,GAAG;QAC9B,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,QAAQ,OAAO,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM;QAC7C,MAAM,WAAW,MAAM,cAAc;QACrC,wFAAwF;QACxF,IAAI,aAAa,WAAW,OAAO,IAAI,CAAC,IAAI;aAEvC,IAAI,CAAC,IAAI,GAAG;IACnB,OAAO;QACL,MAAM,cAAc,IAAI,OAAO,KAAK,IAAI,MAAM,cAAc,CAAC,OAAO,MAAM,EAAE,MAAM,MAAM;IAC1F;IACA,OAAO,MAAM,cAAc;AAC7B;AACA,MAAM,iBAAiB;OAAI;IAC3B,iBAAiB;IACjB;IAAQ;IAAW;IAAU;IAAU;IACvC,iBAAiB;IACjB;CAAU;AACV,MAAM,sBAAsB,IAAI;AAChC,SAAS,qBAAqB,IAAI;IAChC,IAAI,OAAO,oBAAoB,GAAG,CAAC,KAAK,WAAW;IACnD,IAAI;QACF,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,KAAK,WAAW;YAC3B,oBAAoB,GAAG,CAAC,KAAK,WAAW,EAAE;QAC5C;IACF,EAAE,OAAO,GAAG;IACV,MAAM;IACR;IACA,OAAO;AACT;AAEA,wEAAwE;AACxE,SAAS,UAAU,QAAQ,EAAE,QAAQ;IACnC,MAAM,eAAe,CAAC;IAEtB,qBAAqB;IACrB,IAAK,MAAM,QAAQ,SAAU;QAC3B,qBAAqB;QACrB,IAAI,eAAe,QAAQ,CAAC,OAAO;QACnC,sBAAsB;QACtB,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,KAAK,GAAG;QAElD,0BAA0B;QAC1B,YAAY,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;QAEnC,sBAAsB;QACtB,IAAK,MAAM,SAAS,SAAU;YAC5B,IAAI,MAAM,UAAU,CAAC,AAAC,GAAO,OAAL,MAAK,OAAK,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;QACzE;IACF;IAEA,8BAA8B;IAC9B,IAAK,MAAM,QAAQ,SAAS,KAAK,CAAE;QACjC,IAAI,eAAe,QAAQ,CAAC,SAAS,SAAS,cAAc,CAAC,OAAO;QACpE,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,QAAQ,SAAS,MAAM,EAAE;QAE7B,kDAAkD;QAClD,0EAA0E;QAC1E,+EAA+E;QAC/E,kDAAkD;QAClD,4DAA4D;QAC5D,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,KAAK,GAAG;YACrD,0EAA0E;YAC1E,MAAM,OAAO,qBAAqB;YAClC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;QAClD,OAAO;YACL,uDAAuD;YACvD,YAAY,CAAC,IAAI,GAAG;QACtB;IACF;IACA,OAAO;AACT;AAEA,gDAAgD;AAChD,gDAAgD;AAChD,MAAM,YAAY;IAAC;IAAO;IAAe;IAAiB;IAAoB;CAAS;AACvF,MAAM,cAAc;AACpB,yDAAyD;AACzD,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,IAAI;IACJ,MAAM,WAAW,OAAO,KAAK;IAC7B,MAAM,YAAY,YAAY,gBAAgB,UAAU,QAAQ;IAChE,MAAM,eAAe,YAAY,OAAO,KAAK,IAAI,SAAS,UAAU;IACpE,IAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,QAAQ,KAAK,CAAC,KAAK;QAEvB,6BAA6B;QAC7B,IAAI,eAAe,QAAQ,CAAC,OAAO;QAEnC,iEAAiE;QACjE,IAAI,YAAY,YAAY,IAAI,CAAC,OAAO;YACtC,IAAI,OAAO,UAAU,YAAY,SAAS,QAAQ,CAAC,KAAK,GAAG;iBAAW,OAAO,SAAS,QAAQ,CAAC,KAAK;YACpG,SAAS,UAAU,GAAG,OAAO,IAAI,CAAC,SAAS,QAAQ,EAAE,MAAM;YAC3D;QACF;QAEA,iCAAiC;QACjC,yDAAyD;QACzD,IAAI,UAAU,WAAW;QACzB,IAAI,EACF,IAAI,EACJ,GAAG,EACH,MAAM,EACP,GAAG,QAAQ,QAAQ;QAEpB,8CAA8C;QAC9C,IAAI,kBAAkB,kJAAA,CAAA,SAAY,IAAI,iBAAiB,kJAAA,CAAA,SAAY,EAAE;YACnE,OAAO,IAAI,GAAG,MAAM,IAAI;QAC1B,OAEK,IAAI,kBAAkB,kJAAA,CAAA,QAAW,IAAI,sBAAsB,QAAQ;YACtE,OAAO,GAAG,CAAC;QACb,OAEK,IAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,GAAG,KAAK,cAAc,OAAO,OAAO,IAAI,KAAK,cAAc,SAAS,QAAQ,MAAM,WAAW,IAAI,OAAO,WAAW,KAAK,MAAM,WAAW,EAAE;YACjN,OAAO,IAAI,CAAC;QACd,OAEK,IAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,GAAG,KAAK,cAAc,MAAM,OAAO,CAAC,QAAQ;YAClH,IAAI,OAAO,OAAO,SAAS,KAAK,YAAY,OAAO,SAAS,CAAC;iBAAY,OAAO,GAAG,IAAI;QACzF,OAEK,IAAI,WAAW,QAAQ,OAAO,WAAW,YAAY,OAAO,OAAO,GAAG,KAAK,cAAc,OAAO,UAAU,UAAU;YACvH,8BAA8B;YAC9B,IAAI,OAAO,OAAO,SAAS,KAAK,YAAY,OAAO,SAAS,CAAC;iBAExD,OAAO,GAAG,CAAC;QAClB,OAEK;YACH,IAAI;YACJ,IAAI,CAAC,IAAI,GAAG;YAEZ,8DAA8D;YAC9D,yDAAyD;YACzD,gDAAgD;YAChD,IAAI,aAAa,CAAC,UAAU,MAAM,IAAI,UAAU,QAAQ,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,IAAI,KAAK,QAAQ,UAAU,SAAS,IACvH,uFAAuF;YACvF,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,kJAAA,CAAA,aAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,kJAAA,CAAA,mBAAsB,EAAE;gBAClF,wFAAwF;gBACxF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,kJAAA,CAAA,iBAAoB;YAC7C;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,YAAY,QAAQ,SAAS,MAAM,IAAI,aAAa,QAAQ,UAAU,QAAQ,IAAI,CAAC,mBAAmB,SAAS,MAAM,KAAK,QAAQ,iBAAiB,UAAU,IAAI,iBAAiB,SAAS,UAAU,EAAE;QACzM,MAAM,SAAS,SAAS,MAAM;QAC9B,iEAAiE;QACjE,MAAM,QAAQ,UAAU,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC;QACrD,IAAI,QAAQ,CAAC,GAAG,UAAU,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;QAC7D,wEAAwE;QACxE,IAAI,SAAS,UAAU,IAAI,OAAO,OAAO,KAAK,MAAM;YAClD,UAAU,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;QACtC;IACF;IAEA,uCAAuC;IACvC,IAAI,YAAY,SAAS,KAAK,CAAC,MAAM,KAAK,WAAW;QACnD,IAAI,SAAS,MAAM,CAAC,gBAAgB,EAAE,SAAS,KAAK,CAAC,MAAM,GAAG;aAAgB,IAAI,SAAS,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,CAAC,MAAM,GAAG;IACxI;IAEA,wCAAwC;IACxC,IAAI,UAAU,mBAAmB;IACjC,OAAO;AACT;AACA,SAAS,mBAAmB,QAAQ;IAClC,IAAI;IACJ,IAAI,CAAC,SAAS,MAAM,EAAE;IACtB,SAAS,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,SAAS,MAAM;IAClF,MAAM,QAAQ,CAAC,iBAAiB,SAAS,IAAI,KAAK,OAAO,KAAK,IAAI,eAAe,QAAQ,IAAI,OAAO,KAAK,IAAI,eAAe,QAAQ;IACpI,IAAI,SAAS,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG,MAAM,UAAU;AAC5D;AACA,SAAS,aAAa,MAAM,EAAE,IAAI;IAChC,wDAAwD;IACxD,wDAAwD;IACxD,IAAI,OAAO,MAAM,EAAE;IACnB,IAAI,qBAAqB,SAAS;QAChC,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG,CAAC;QAC5B,OAAO,KAAK,GAAG,KAAK,KAAK,GAAG;QAC5B,OAAO,GAAG,GAAG,KAAK,MAAM,GAAG;QAC3B,OAAO,MAAM,GAAG,KAAK,MAAM,GAAG,CAAC;IACjC,OAAO;QACL,OAAO,MAAM,GAAG,KAAK,KAAK,GAAG,KAAK,MAAM;IAC1C;IACA,OAAO,sBAAsB;AAC/B;AACA,MAAM,aAAa,CAAA,SAAU,UAAU,OAAO,KAAK,IAAI,OAAO,UAAU;AAExE,SAAS,OAAO,KAAK;IACnB,OAAO,CAAC,MAAM,WAAW,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,MAAM,MAAM,KAAK,GAAG,MAAM,UAAU;AACxF;AAEA;;;CAGC,GACD,SAAS,8BAA8B,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS;IAC1E,MAAM,cAAc,SAAS,GAAG,CAAC;IACjC,IAAI,aAAa;QACf,SAAS,MAAM,CAAC;QAChB,yDAAyD;QACzD,IAAI,SAAS,IAAI,KAAK,GAAG;YACvB,YAAY,MAAM,CAAC;YACnB,YAAY,MAAM,CAAC,qBAAqB,CAAC;QAC3C;IACF;AACF;AACA,SAAS,oBAAoB,KAAK,EAAE,MAAM;IACxC,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;IAClB,uDAAuD;IACvD,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9D,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9D,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC/B,IAAI,MAAM,WAAW,KAAK,UAAU,MAAM,MAAM,KAAK,QAAQ;YAC3D,iDAAiD;YACjD,SAAS,OAAO,CAAC,MAAM,CAAC;QAC1B;IACF;IACA,SAAS,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU;QACtC,8BAA8B,SAAS,WAAW,EAAE,QAAQ,UAAU;IACxE;AACF;AACA,SAAS,aAAa,KAAK;IACzB,qBAAqB,GACrB,SAAS,kBAAkB,KAAK;QAC9B,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;QAClB,MAAM,KAAK,MAAM,OAAO,GAAG,SAAS,YAAY,CAAC,EAAE;QACnD,MAAM,KAAK,MAAM,OAAO,GAAG,SAAS,YAAY,CAAC,EAAE;QACnD,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;IAC7C;IAEA,uGAAuG,GACvG,SAAS,oBAAoB,OAAO;QAClC,OAAO,QAAQ,MAAM,CAAC,CAAA,MAAO;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAO;aAAQ,CAAC,IAAI,CAAC,CAAA;gBAC1E,IAAI;gBACJ,OAAO,CAAC,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK;YAChF;IACF;IACA,SAAS,UAAU,KAAK,EAAE,MAAM;QAC9B,MAAM,QAAQ,MAAM,QAAQ;QAC5B,MAAM,aAAa,IAAI;QACvB,MAAM,gBAAgB,EAAE;QACxB,2CAA2C;QAC3C,MAAM,gBAAgB,SAAS,OAAO,MAAM,QAAQ,CAAC,WAAW,IAAI,MAAM,QAAQ,CAAC,WAAW;QAC9F,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,MAAM,QAAQ,aAAa,aAAa,CAAC,EAAE;YAC3C,IAAI,OAAO;gBACT,MAAM,SAAS,CAAC,MAAM,GAAG;YAC3B;QACF;QACA,IAAI,CAAC,MAAM,YAAY,EAAE;YACvB,kDAAkD;YAClD,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO;QACtE;QACA,SAAS,cAAc,GAAG;YACxB,MAAM,QAAQ,aAAa;YAC3B,kFAAkF;YAClF,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,OAAO,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,MAAM,OAAO,EAAE;YAEjF,gFAAgF;YAChF,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW;gBACxC,IAAI;gBACJ,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,OAAO,CAAC,sBAAsB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,oBAAoB,QAAQ;gBACrK,uEAAuE;gBACvE,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW,MAAM,SAAS,CAAC,MAAM,GAAG;YACrE;YAEA,6BAA6B;YAC7B,OAAO,MAAM,SAAS,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QACjF;QAEA,iBAAiB;QACjB,IAAI,OAAO,aACX,oBAAoB;SACnB,OAAO,CAAC,cACT,sCAAsC;SACrC,IAAI,CAAC,CAAC,GAAG;YACR,MAAM,SAAS,aAAa,EAAE,MAAM;YACpC,MAAM,SAAS,aAAa,EAAE,MAAM;YACpC,IAAI,CAAC,UAAU,CAAC,QAAQ,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YACtD,OAAO,OAAO,MAAM,CAAC,QAAQ,GAAG,OAAO,MAAM,CAAC,QAAQ,IAAI,EAAE,QAAQ,GAAG,EAAE,QAAQ;QACnF,EACA,wBAAwB;SACvB,MAAM,CAAC,CAAA;YACN,MAAM,KAAK,OAAO;YAClB,IAAI,WAAW,GAAG,CAAC,KAAK,OAAO;YAC/B,WAAW,GAAG,CAAC;YACf,OAAO;QACT;QAEA,kDAAkD;QAClD,8FAA8F;QAC9F,IAAI,MAAM,MAAM,CAAC,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM;QAE1D,4DAA4D;QAC5D,KAAK,MAAM,OAAO,KAAM;YACtB,IAAI,cAAc,IAAI,MAAM;YAC5B,kBAAkB;YAClB,MAAO,YAAa;gBAClB,IAAI;gBACJ,IAAI,CAAC,QAAQ,YAAY,KAAK,KAAK,QAAQ,MAAM,UAAU,EAAE,cAAc,IAAI,CAAC;oBAC9E,GAAG,GAAG;oBACN;gBACF;gBACA,cAAc,YAAY,MAAM;YAClC;QACF;QAEA,oFAAoF;QACpF,IAAI,eAAe,SAAS,MAAM,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;YAC3E,KAAK,IAAI,eAAe,MAAM,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,EAAE,MAAM,GAAI;gBAChF,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO,YAAY,YAAY,IAAI,cAAc,IAAI,CAAC,YAAY,YAAY;YACpG;QACF;QACA,OAAO;IACT;IAEA,0DAA0D,GAC1D,SAAS,iBAAiB,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ;QAC7D,gEAAgE;QAChE,IAAI,cAAc,MAAM,EAAE;YACxB,MAAM,aAAa;gBACjB,SAAS;YACX;YACA,KAAK,MAAM,OAAO,cAAe;gBAC/B,IAAI,QAAQ,aAAa,IAAI,MAAM;gBAEnC,oFAAoF;gBACpF,6EAA6E;gBAC7E,IAAI,CAAC,OAAO;oBACV,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAA;wBAC3B,MAAM,cAAc,aAAa;wBACjC,IAAI,aAAa;4BACf,QAAQ;4BACR,OAAO;wBACT;oBACF;gBACF;gBACA,IAAI,OAAO;oBACT,MAAM,EACJ,SAAS,EACT,OAAO,EACP,MAAM,EACN,QAAQ,EACT,GAAG;oBACJ,MAAM,mBAAmB,IAAI,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC;oBAC9E,MAAM,oBAAoB,CAAA;wBACxB,IAAI,uBAAuB;wBAC3B,OAAO,CAAC,wBAAwB,CAAC,yBAAyB,SAAS,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,uBAAuB,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,OAAO,wBAAwB;oBAC5L;oBACA,MAAM,oBAAoB,CAAA;wBACxB,MAAM,cAAc;4BAClB,cAAc;4BACd,QAAQ,MAAM,MAAM;wBACtB;wBACA,IAAI,SAAS,WAAW,CAAC,GAAG,CAAC,KAAK;4BAChC,kEAAkE;4BAClE,qBAAqB;4BACrB,SAAS,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,WAAW,EAAE;wBACpD,OAAO;4BACL,gEAAgE;4BAChE,+DAA+D;4BAC/D,iBAAiB;4BACjB,SAAS,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;gCAAC;oCAAC,IAAI,WAAW;oCAAE;iCAAY;6BAAC;wBACvE;wBACA,MAAM,MAAM,CAAC,iBAAiB,CAAC;oBACjC;oBACA,MAAM,wBAAwB,CAAA;wBAC5B,MAAM,WAAW,SAAS,WAAW,CAAC,GAAG,CAAC;wBAC1C,IAAI,UAAU;4BACZ,8BAA8B,SAAS,WAAW,EAAE,IAAI,WAAW,EAAE,UAAU;wBACjF;oBACF;oBAEA,yBAAyB;oBACzB,IAAI,oBAAoB,CAAC;oBACzB,uaAAua;oBACva,IAAK,IAAI,QAAQ,MAAO;wBACtB,IAAI,WAAW,KAAK,CAAC,KAAK;wBAC1B,mEAAmE;wBACnE,mCAAmC;wBACnC,IAAI,OAAO,aAAa,YAAY,iBAAiB,CAAC,KAAK,GAAG;oBAChE;oBACA,IAAI,eAAe;wBACjB,GAAG,GAAG;wBACN,GAAG,iBAAiB;wBACpB;wBACA;wBACA,SAAS,WAAW,OAAO;wBAC3B;wBACA;wBACA,KAAK,UAAU,GAAG;wBAClB,QAAQ;wBACR,iDAAiD;wBACjD;4BACE,yDAAyD;4BACzD,8EAA8E;4BAC9E,MAAM,qBAAqB,eAAe,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS;4BAE3F,uCAAuC;4BACvC,IACA,0CAA0C;4BAC1C,CAAC,sBACD,oDAAoD;4BACpD,mBAAmB,GAAG,CAAC,IAAI,WAAW,GAAG;gCACvC,aAAa,OAAO,GAAG,WAAW,OAAO,GAAG;gCAC5C,yDAAyD;gCACzD,mFAAmF;gCACnF,IAAI,SAAS,OAAO,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,IAAI,WAAW,GAAG;oCAC/G,gFAAgF;oCAChF,MAAM,SAAS,cAAc,KAAK,CAAC,GAAG,cAAc,OAAO,CAAC;oCAC5D,cAAc;2CAAI;wCAAQ;qCAAI;gCAChC;4BACF;wBACF;wBACA,iEAAiE;wBACjE,QAAQ;4BACN;4BACA;4BACA;wBACF;wBACA,eAAe;4BACb;4BACA;4BACA;wBACF;wBACA,aAAa;oBACf;oBAEA,mBAAmB;oBACnB,SAAS;oBACT,uDAAuD;oBACvD,IAAI,WAAW,OAAO,KAAK,MAAM;gBACnC;YACF;QACF;QACA,OAAO;IACT;IACA,SAAS,cAAc,aAAa;QAClC,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;QAClB,KAAK,MAAM,cAAc,SAAS,OAAO,CAAC,MAAM,GAAI;YAClD,wFAAwF;YACxF,2EAA2E;YAC3E,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,KAAK,KAAK,WAAW,KAAK,IAAI,IAAI,UAAU,KAAK,WAAW,UAAU,GAAG;gBACvK,MAAM,cAAc,WAAW,WAAW;gBAC1C,MAAM,WAAW,YAAY,KAAK;gBAClC,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO;gBAC/B,IAAI,YAAY,QAAQ,SAAS,UAAU,EAAE;oBAC3C,MAAM,WAAW,SAAS,QAAQ;oBAClC,iDAAiD;oBACjD,MAAM,OAAO;wBACX,GAAG,UAAU;wBACb;oBACF;oBACA,SAAS,YAAY,IAAI,OAAO,KAAK,IAAI,SAAS,YAAY,CAAC;oBAC/D,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC;gBACrE;YACF;QACF;IACF;IACA,SAAS,cAAc,KAAK,EAAE,OAAO;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;YACjC,YAAY,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,eAAe,CAAC;QACrH;IACF;IACA,SAAS,cAAc,IAAI;QACzB,wBAAwB;QACxB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAM,cAAc,EAAE;YAC/B,KAAK;gBACH,OAAO,CAAA;oBACL,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;oBAClB,IAAI,eAAe,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;wBACrE,qFAAqF;wBACrF,2FAA2F;wBAC3F,0FAA0F;wBAC1F,qFAAqF;wBACrF,sBAAsB;4BACpB,kDAAkD;4BAClD,IAAI,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;gCAC7C,SAAS,WAAW,CAAC,MAAM,CAAC,MAAM,SAAS;gCAC3C,cAAc,EAAE;4BAClB;wBACF;oBACF;gBACF;QACJ;QAEA,kCAAkC;QAClC,OAAO,SAAS,YAAY,KAAK;YAC/B,MAAM,EACJ,eAAe,EACf,QAAQ,EACT,GAAG,MAAM,QAAQ;YAElB,oBAAoB;YACpB,SAAS,SAAS,CAAC,OAAO,GAAG;YAE7B,uBAAuB;YACvB,MAAM,gBAAgB,SAAS;YAC/B,MAAM,eAAe,SAAS,aAAa,SAAS,mBAAmB,SAAS;YAChF,MAAM,SAAS,gBAAgB,sBAAsB;YACrD,MAAM,OAAO,UAAU,OAAO;YAC9B,MAAM,QAAQ,eAAe,kBAAkB,SAAS;YAExD,2CAA2C;YAC3C,IAAI,SAAS,iBAAiB;gBAC5B,SAAS,YAAY,GAAG;oBAAC,MAAM,OAAO;oBAAE,MAAM,OAAO;iBAAC;gBACtD,SAAS,WAAW,GAAG,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,WAAW;YACxD;YAEA,mEAAmE;YACnE,wFAAwF;YACxF,IAAI,gBAAgB,CAAC,KAAK,MAAM,EAAE;gBAChC,IAAI,SAAS,GAAG;oBACd,cAAc,OAAO,SAAS,WAAW;oBACzC,IAAI,iBAAiB,gBAAgB;gBACvC;YACF;YACA,uBAAuB;YACvB,IAAI,eAAe,cAAc;YACjC,SAAS,YAAY,IAAI;gBACvB,MAAM,cAAc,KAAK,WAAW;gBACpC,MAAM,WAAW,YAAY,KAAK;gBAElC,6BAA6B;gBAC7B,IAAI,CAAC,CAAC,YAAY,QAAQ,SAAS,UAAU,GAAG;gBAChD,MAAM,WAAW,SAAS,QAAQ;gBAElC;;;;;;;;;;;;;SAaC,GAED,IAAI,eAAe;oBACjB,iBAAiB;oBACjB,IAAI,SAAS,aAAa,IAAI,SAAS,cAAc,IAAI,SAAS,YAAY,IAAI,SAAS,cAAc,EAAE;wBACzG,wDAAwD;wBACxD,MAAM,KAAK,OAAO;wBAClB,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;wBACzC,IAAI,CAAC,aAAa;4BAChB,wEAAwE;4BACxE,SAAS,OAAO,CAAC,GAAG,CAAC,IAAI;4BACzB,SAAS,aAAa,IAAI,OAAO,KAAK,IAAI,SAAS,aAAa,CAAC;4BACjE,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC;wBACrE,OAAO,IAAI,YAAY,OAAO,EAAE;4BAC9B,8FAA8F;4BAC9F,KAAK,eAAe;wBACtB;oBACF;oBACA,kBAAkB;oBAClB,SAAS,aAAa,IAAI,OAAO,KAAK,IAAI,SAAS,aAAa,CAAC;gBACnE,OAAO;oBACL,uBAAuB;oBACvB,MAAM,UAAU,QAAQ,CAAC,KAAK;oBAC9B,IAAI,SAAS;wBACX,2FAA2F;wBAC3F,oCAAoC;wBACpC,IAAI,CAAC,gBAAgB,SAAS,WAAW,CAAC,QAAQ,CAAC,cAAc;4BAC/D,mCAAmC;4BACnC,cAAc,OAAO,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,SAAS,WAAW,CAAC,QAAQ,CAAC;4BAC1F,uBAAuB;4BACvB,QAAQ;wBACV;oBACF,OAAO;wBACL,6GAA6G;wBAC7G,IAAI,gBAAgB,SAAS,WAAW,CAAC,QAAQ,CAAC,cAAc;4BAC9D,cAAc,OAAO,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,SAAS,WAAW,CAAC,QAAQ,CAAC;wBAC5F;oBACF;gBACF;YACF;YACA,iBAAiB,MAAM,OAAO,OAAO;QACvC;IACF;IACA,OAAO;QACL;IACF;AACF;AAEA,MAAM,aAAa,CAAA,MAAO,CAAC,CAAC,CAAC,OAAO,QAAQ,IAAI,MAAM;AACtD,MAAM,UAAU,aAAa,GAAE,8JAAM,aAAa,CAAC;AACnD,MAAM,cAAc,CAAC,YAAY;IAC/B,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE,CAAC,KAAK;QAC3C,MAAM,WAAW,IAAI,kJAAA,CAAA,UAAa;QAClC,MAAM,gBAAgB,IAAI,kJAAA,CAAA,UAAa;QACvC,MAAM,aAAa,IAAI,kJAAA,CAAA,UAAa;QACpC,SAAS;gBAAmB,SAAA,iEAAS,MAAM,MAAM,EAAE,SAAA,iEAAS,eAAe,OAAA,iEAAO,MAAM,IAAI;YAC1F,MAAM,EACJ,KAAK,EACL,MAAM,EACN,GAAG,EACH,IAAI,EACL,GAAG;YACJ,MAAM,SAAS,QAAQ;YACvB,IAAI,OAAO,SAAS,EAAE,WAAW,IAAI,CAAC;iBAAa,WAAW,GAAG,IAAI;YACrE,MAAM,WAAW,OAAO,gBAAgB,CAAC,UAAU,UAAU,CAAC;YAC9D,IAAI,qBAAqB,SAAS;gBAChC,OAAO;oBACL,OAAO,QAAQ,OAAO,IAAI;oBAC1B,QAAQ,SAAS,OAAO,IAAI;oBAC5B;oBACA;oBACA,QAAQ;oBACR;oBACA;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,kCAAkC;gBAC1E,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,UAAU,iBAAiB;gBAC7D,MAAM,IAAI,IAAI,CAAC,QAAQ,MAAM;gBAC7B,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR;oBACA;oBACA,QAAQ,QAAQ;oBAChB;oBACA;gBACF;YACF;QACF;QACA,IAAI,qBAAqB;QACzB,MAAM,wBAAwB,CAAA,UAAW,IAAI,CAAA,QAAS,CAAC;oBACrD,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB;oBACF;gBACF,CAAC;QACD,MAAM,UAAU,IAAI,kJAAA,CAAA,UAAa;QACjC,MAAM,YAAY;YAChB;YACA;YACA,0CAA0C;YAC1C,IAAI;YACJ,QAAQ;YACR,WAAW;YACX,QAAQ;gBACN,UAAU;gBACV,SAAS;gBACT,WAAW;YACb;YACA,OAAO;YACP,IAAI;YACJ,YAAY;oBAAC,0EAAS;uBAAM,WAAW,OAAO;;YAC9C,SAAS,CAAC,WAAW,mBAAqB,QAAQ,WAAW,kBAAkB;YAC/E,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO,IAAI,kJAAA,CAAA,QAAW;YACtB;YACA,OAAO;YACP,WAAW;YACX,iBAAiB;YACjB,aAAa;gBACX,SAAS;gBACT,KAAK;gBACL,KAAK;gBACL,UAAU;gBACV,SAAS;oBACP,MAAM,QAAQ;oBACd,gBAAgB;oBAChB,IAAI,oBAAoB,aAAa;oBACrC,8BAA8B;oBAC9B,IAAI,MAAM,WAAW,CAAC,OAAO,KAAK,MAAM,WAAW,CAAC,GAAG,EAAE,sBAAsB,MAAM,WAAW,CAAC,GAAG;oBACpG,wFAAwF;oBACxF,qBAAqB,WAAW,IAAM,sBAAsB,MAAM,WAAW,CAAC,GAAG,GAAG,MAAM,WAAW,CAAC,QAAQ;gBAChH;YACF;YACA,MAAM;gBACJ,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,MAAM;YACR;YACA,UAAU;gBACR,YAAY;gBACZ,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR;YACF;YACA,WAAW,CAAA,SAAU,IAAI,CAAA,QAAS,CAAC;wBACjC,GAAG,KAAK;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,GAAG,MAAM;wBACX;oBACF,CAAC;YACD,SAAS,SAAC,OAAO;oBAAQ,uEAAM,GAAG,wEAAO;gBACvC,MAAM,SAAS,MAAM,MAAM;gBAC3B,MAAM,OAAO;oBACX;oBACA;oBACA;oBACA;gBACF;gBACA,IAAI,CAAA,QAAS,CAAC;wBACZ;wBACA,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,GAAG,mBAAmB,QAAQ,eAAe,KAAK;wBACpD;oBACF,CAAC;YACH;YACA,QAAQ,CAAA,MAAO,IAAI,CAAA;oBACjB,MAAM,WAAW,aAAa;oBAC9B,OAAO;wBACL,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,KAAK;4BACL,YAAY,MAAM,QAAQ,CAAC,UAAU,IAAI;wBAC3C;oBACF;gBACF;YACA,cAAc;oBAAC,6EAAY;gBACzB,MAAM,QAAQ,MAAM,KAAK;gBAEzB,iFAAiF;gBACjF,MAAM,IAAI;gBACV,MAAM,WAAW,GAAG;gBACpB,IAAI,cAAc,SAAS;oBACzB,MAAM,KAAK;oBACX,MAAM,WAAW,GAAG;gBACtB;gBACA,IAAI,IAAM,CAAC;wBACT;oBACF,CAAC;YACH;YACA,cAAc;YACd,UAAU;gBACR,SAAS;gBACT,aAAa,EAAE;gBACf,SAAS,IAAI;gBACb,aAAa,EAAE;gBACf,cAAc;oBAAC;oBAAG;iBAAE;gBACpB,aAAa,EAAE;gBACf,aAAa,IAAI;gBACjB,WAAW,WAAW,GAAE,8JAAM,SAAS;gBACvC,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,WAAW,CAAC,KAAK,UAAU;oBACzB,MAAM,WAAW,MAAM,QAAQ;oBAC/B,mFAAmF;oBACnF,iFAAiF;oBACjF,6EAA6E;oBAC7E,uDAAuD;oBACvD,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC;oBAC7D,SAAS,WAAW,CAAC,IAAI,CAAC;wBACxB;wBACA;wBACA;oBACF;oBACA,uEAAuE;oBACvE,6DAA6D;oBAC7D,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;oBAClF,OAAO;wBACL,MAAM,WAAW,MAAM,QAAQ;wBAC/B,IAAI,YAAY,QAAQ,SAAS,WAAW,EAAE;4BAC5C,2DAA2D;4BAC3D,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC;4BAC7D,8BAA8B;4BAC9B,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;wBACpE;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA,MAAM,QAAQ,UAAU,QAAQ;IAChC,IAAI,UAAU,MAAM,IAAI;IACxB,IAAI,SAAS,MAAM,QAAQ,CAAC,GAAG;IAC/B,IAAI,YAAY,MAAM,MAAM;IAC5B,UAAU,SAAS,CAAC;QAClB,MAAM,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,EAAE,EACF,GAAG,EACJ,GAAG,UAAU,QAAQ;QAEtB,+DAA+D;QAC/D,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,QAAQ,MAAM,IAAI,SAAS,GAAG,KAAK,QAAQ;YAC7F,UAAU;YACV,SAAS,SAAS,GAAG;YACrB,2BAA2B;YAC3B,aAAa,QAAQ;YACrB,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG,aAAa,CAAC,SAAS,GAAG;YACnD,MAAM,cAAc,OAAO,sBAAsB,eAAe,GAAG,UAAU,YAAY;YACzF,GAAG,OAAO,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE;QACtC;QAEA,0CAA0C;QAC1C,IAAI,WAAW,WAAW;YACxB,YAAY;YACZ,kBAAkB;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,GAAG,MAAM,QAAQ,CAAC,kBAAkB,CAAC,OAAO;oBAC9C;gBACF,CAAC;QACH;IACF;IAEA,2BAA2B;IAC3B,UAAU,SAAS,CAAC,CAAA,QAAS,WAAW;IAExC,oBAAoB;IACpB,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,GAAG;IAC5B,MAAM,WAAW,8JAAM,MAAM,CAAC;IAC9B,8JAAM,mBAAmB,CAAC;iDAAU,IAAM,IAAI,OAAO,CAAC,KAAK;gDAAE;QAAC;KAAI;IAClE,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS;IACP,MAAM,QAAQ,8JAAM,UAAU,CAAC;IAC/B,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM;IAC5B,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS;QAAS,WAAA,iEAAW,CAAA,QAAS,OAAO;IAC3C,OAAO,WAAW,UAAU;AAC9B;AAEA;;;;CAIC,GACD,SAAS,SAAS,QAAQ;QAAE,iBAAA,iEAAiB;IAC3C,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS;IACrD,cAAc;IACd,MAAM,MAAM,mBAAmB;IAC/B,6CAA6C;IAC7C;8CAA0B,IAAM,UAAU,KAAK,gBAAgB;6CAAQ;QAAC;QAAgB;QAAW;KAAM;IACzG,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,SAAS,MAAM;IACtB,OAAO,8JAAM,OAAO;4BAAC,IAAM,WAAW;2BAAS;QAAC;KAAO;AACzD;AACA,MAAM,kBAAkB,IAAI;AAC5B,MAAM,kBAAkB,CAAA;IACtB,IAAI;IACJ,OAAO,OAAO,UAAU,cAAc,CAAC,SAAS,OAAO,KAAK,IAAI,CAAC,mBAAmB,MAAM,SAAS,KAAK,OAAO,KAAK,IAAI,iBAAiB,WAAW,MAAM;AAC5J;AACA,SAAS,UAAU,UAAU,EAAE,UAAU;IACvC,OAAO,SAAU,KAAK;QAAE,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,QAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YAAG,MAAH,OAAA,KAAA,SAAA,CAAA,KAAQ;;QAC9B,IAAI;QAEJ,uDAAuD;QACvD,IAAI,gBAAgB,QAAQ;YAC1B,SAAS,gBAAgB,GAAG,CAAC;YAC7B,IAAI,CAAC,QAAQ;gBACX,SAAS,IAAI;gBACb,gBAAgB,GAAG,CAAC,OAAO;YAC7B;QACF,OAAO;YACL,SAAS;QACX;QAEA,0BAA0B;QAC1B,IAAI,YAAY,WAAW;QAE3B,oCAAoC;QACpC,OAAO,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA,QAAS,IAAI,QAAQ,CAAC,KAAK,SAAW,OAAO,IAAI,CAAC,OAAO,CAAA;oBACpF,IAAI,WAAW,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,MAAM,WAAW,KAAK,KAAK;oBAC7F,IAAI;gBACN,GAAG,YAAY,CAAA,QAAS,OAAO,IAAI,MAAM,AAAC,kBAA2B,OAAV,OAAM,MAA2C,OAAvC,SAAS,OAAO,KAAK,IAAI,MAAM,OAAO;IAC7G;AACF;AAEA;;;;;CAKC,GACD,SAAS,UAAU,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU;IACtD,oCAAoC;IACpC,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,YAAY,aAAa;QAAC;WAAW;KAAK,EAAE;QAC5E,OAAO,GAAG,GAAG;IACf;IACA,uBAAuB;IACvB,OAAO,MAAM,OAAO,CAAC,SAAS,UAAU,OAAO,CAAC,EAAE;AACpD;AAEA;;CAEC,GACD,UAAU,OAAO,GAAG,SAAU,MAAM,EAAE,KAAK,EAAE,UAAU;IACrD,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,aAAa;QAAC;WAAW;KAAK;AACzD;AAEA;;CAEC,GACD,UAAU,KAAK,GAAG,SAAU,MAAM,EAAE,KAAK;IACvC,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,OAAO,CAAA,GAAA,4IAAA,CAAA,QAAK,AAAD,EAAE;QAAC;WAAW;KAAK;AAChC;AAEA,iDAAiD;AACjD,iDAAiD;AAEjD,SAAS,iBAAiB,MAAM;IAC9B,MAAM,aAAa,CAAA,GAAA,+IAAA,CAAA,UAAU,AAAD,EAAE;IAC9B,WAAW,kBAAkB,CAAC;QAC5B,YAAY,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,oDAAyB,eAAe,IAAI;QAC1F,qBAAqB;QACrB,SAAS,8JAAM,OAAO;IACxB;IACA,OAAO;AACT;AACA,MAAM,kBAAkB;AAExB,qCAAqC;AACrC,wDAAwD;AACxD,uDAAuD;AAEvD,MAAM,YAAY,CAAC;AACnB,MAAM,eAAe;AACrB,MAAM,eAAe,CAAA,OAAQ,AAAC,GAA0B,OAAxB,IAAI,CAAC,EAAE,CAAC,WAAW,IAAmB,OAAd,KAAK,KAAK,CAAC;AACnE,IAAI,IAAI;AACR,MAAM,gBAAgB,CAAA,SAAU,OAAO,WAAW;AAClD,SAAS,OAAO,OAAO;IACrB,IAAI,cAAc,UAAU;QAC1B,MAAM,YAAY,AAAC,GAAM,OAAJ;QACrB,SAAS,CAAC,UAAU,GAAG;QACvB,OAAO;IACT,OAAO;QACL,OAAO,MAAM,CAAC,WAAW;IAC3B;AACF;AACA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACnC,4BAA4B;IAC5B,MAAM,OAAO,aAAa;IAC1B,MAAM,SAAS,SAAS,CAAC,KAAK;IAE9B,0BAA0B;IAC1B,IAAI,SAAS,eAAe,CAAC,QAAQ,MAAM,IAAI,MAAM,AAAC,QAAY,OAAL,MAAK;IAElE,sBAAsB;IACtB,IAAI,SAAS,eAAe,CAAC,MAAM,MAAM,EAAE,MAAM,IAAI,MAAO;IAE5D,oDAAoD;IACpD,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM;AAC9E;AACA,SAAS,eAAe,IAAI,EAAE,KAAK,EAAE,IAAI;IACvC,IAAI;IACJ,mEAAmE;IACnE,OAAO,aAAa,SAAS,YAAY,OAAO,KAAK,OAAO,CAAC,cAAc;IAC3E,iBAAiB,MAAM;IAEvB,sEAAsE;IACtE,IAAI,SAAS,eAAe,CAAC,gBAAgB,MAAM,MAAM,KAAK,QAAQ,cAAc,KAAK,EAAE,OAAO,MAAM,MAAM,CAAC,KAAK;IACpH,OAAO,QAAQ,MAAM,MAAM,EAAE,MAAM,MAAM;AAC3C;AACA,SAAS,aAAa,QAAQ;IAC5B,IAAI,CAAC,SAAS,QAAQ,EAAE;QACtB,IAAI;QACJ,IAAI,SAAS,KAAK,CAAC,MAAM,IAAI,CAAC,mBAAmB,SAAS,MAAM,KAAK,QAAQ,iBAAiB,MAAM,EAAE;YACpG,OAAO,SAAS,MAAM,EAAE;QAC1B,OAAO,IAAI,WAAW,SAAS,MAAM,GAAG;YACtC,SAAS,MAAM,CAAC,OAAO,GAAG;QAC5B;QACA,SAAS,QAAQ,GAAG;QACpB,mBAAmB;IACrB;AACF;AACA,SAAS,eAAe,QAAQ;IAC9B,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI;QACJ,IAAI,SAAS,KAAK,CAAC,MAAM,IAAI,CAAC,oBAAoB,SAAS,MAAM,KAAK,QAAQ,kBAAkB,MAAM,EAAE;YACtG,OAAO,SAAS,MAAM,EAAE;QAC1B,OAAO,IAAI,WAAW,SAAS,MAAM,KAAK,SAAS,KAAK,CAAC,OAAO,KAAK,OAAO;YAC1E,SAAS,MAAM,CAAC,OAAO,GAAG;QAC5B;QACA,SAAS,QAAQ,GAAG;QACpB,mBAAmB;IACrB;AACF;AAEA,iDAAiD;AACjD,sFAAsF;AACtF,SAAS,uBAAuB,MAAM,EAAE,KAAK,EAAE,WAAW;IACxD,2DAA2D;IAC3D,sFAAsF;IACtF,MAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ;IACjC,IAAI,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,KAAK,MAAM,KAAK,EAAE;IAErD,oCAAoC;IACpC,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,IAAI,qBAAqB;QACzB,4BAA4B;QAC5B,MAAM,SAAS,SAAS,CAAC,aAAa,MAAM,IAAI,EAAE;QAElD,gBAAgB;QAChB,MAAM,MAAM,GAAG,CAAC,sBAAsB,MAAM,KAAK,CAAC,MAAM,KAAK,OAAO,sBAAsB,IAAI,UAAW,CAAC,oBAAoB,MAAM,KAAK,CAAC,IAAI,KAAK,OAAO,oBAAoB,EAAE;QAChL,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB;IAEA,oBAAoB;IACpB,WAAW,MAAM,MAAM,EAAE,MAAM,KAAK;IAEpC,kBAAkB;IAClB,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;QACtB,OAAO,QAAQ;IACjB,OAAO,IAAI,WAAW,MAAM,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG;QAChE,MAAM,aAAa,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,OAAO,KAAK,IAAI,YAAY,MAAM;QACnG,IAAI,eAAe,eAAe,CAAC,GAAG;YACpC,sFAAsF;YACtF,mDAAmD;YACnD,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,MAAM;YACjE,IAAI,kBAAkB,CAAC,GAAG;gBACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe;gBAC7C,MAAM,gBAAgB,gBAAgB,aAAa,aAAa,IAAI;gBACpE,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,GAAG,MAAM,MAAM;YAC9D,OAAO;gBACL,MAAM,MAAM,CAAC,MAAM,GAAG,OAAO,MAAM;gBACnC,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,MAAM;gBACzD,MAAM,MAAM,CAAC,aAAa,CAAC;oBACzB,MAAM;gBACR;gBACA,OAAO,MAAM,CAAC,aAAa,CAAC;oBAC1B,MAAM;oBACN,OAAO,MAAM,MAAM;gBACrB;YACF;QACF,OAAO;YACL,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,MAAM;QAChC;IACF;IAEA,eAAe;IACf,KAAK,MAAM,iBAAiB,MAAM,QAAQ,CAAE,uBAAuB,OAAO;IAE1E,oCAAoC;IACpC,mBAAmB;AACrB;AACA,SAAS,YAAY,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;IAEZ,iBAAiB;IACjB,MAAM,MAAM,GAAG;IACf,OAAO,QAAQ,CAAC,IAAI,CAAC;IAErB,4BAA4B;IAC5B,uBAAuB,QAAQ;AACjC;AACA,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,WAAW;IAC9C,IAAI,CAAC,SAAS,CAAC,aAAa;IAE5B,iBAAiB;IACjB,MAAM,MAAM,GAAG;IACf,MAAM,aAAa,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC3C,IAAI,eAAe,CAAC,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,YAAY,GAAG;SAAY,OAAO,QAAQ,CAAC,IAAI,CAAC;IAE9F,4BAA4B;IAC5B,uBAAuB,QAAQ,OAAO;AACxC;AACA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,OAAO,KAAK,YAAY;QACxC,MAAM,gBAAgB;YACpB,IAAI;gBACF,OAAO,OAAO;YAChB,EAAE,UAAM;YACN,QAAQ;YACV;QACF;QAEA,gDAAgD;QAChD,IAAI,OAAO,6BAA6B,aAAa;aAEhD,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD,EAAE,kLAAA,CAAA,wBAAqB,EAAE;IACxD;AACF;AACA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,OAAO;IACzC,IAAI,CAAC,OAAO;IAEZ,mBAAmB;IACnB,MAAM,MAAM,GAAG;IACf,MAAM,aAAa,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC3C,IAAI,eAAe,CAAC,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,YAAY;IAE1D,yBAAyB;IACzB,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;QACtB,OAAO,QAAQ;IACjB,OAAO,IAAI,WAAW,MAAM,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG;QAChE,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;QACjC,oBAAoB,gBAAgB,QAAQ,MAAM,MAAM;IAC1D;IAEA,oEAAoE;IACpE,MAAM,gBAAgB,MAAM,KAAK,CAAC,OAAO,KAAK,QAAQ,YAAY;IAElE,uCAAuC;IACvC,IAAK,IAAI,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACnD,MAAM,OAAO,MAAM,QAAQ,CAAC,EAAE;QAC9B,YAAY,OAAO,MAAM;IAC3B;IACA,MAAM,QAAQ,CAAC,MAAM,GAAG;IAExB,yBAAyB;IACzB,OAAO,MAAM,MAAM,CAAC,KAAK;IAEzB,wDAAwD;IACxD,gFAAgF;IAChF,kDAAkD;IAClD,2BAA2B;IAC3B,6CAA6C;IAC7C,oEAAoE;IACpE,IAAI,iBAAiB,MAAM,IAAI,KAAK,eAAe,MAAM,MAAM,CAAC,IAAI,KAAK,SAAS;QAChF,cAAc,MAAM,MAAM;IAC5B;IAEA,2DAA2D;IAC3D,IAAI,YAAY,WAAW,mBAAmB;AAChD;AACA,SAAS,YAAY,KAAK,EAAE,cAAc;IACxC,KAAK,MAAM,UAAU;QAAC;QAAO,MAAM,SAAS;KAAC,CAAE;QAC7C,IAAI,WAAW,MAAM;YACnB,IAAI,OAAO,OAAO,GAAG,KAAK,YAAY;gBACpC,OAAO,UAAU,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU;gBACtD,MAAM,UAAU,OAAO,GAAG,CAAC;gBAC3B,IAAI,OAAO,YAAY,YAAY,OAAO,UAAU,GAAG;YACzD,OAAO,IAAI,OAAO,GAAG,EAAE;gBACrB,OAAO,GAAG,CAAC,OAAO,GAAG;YACvB;QACF;IACF;AACF;AACA,MAAM,gBAAgB,EAAE;AACxB,SAAS;IACP,kBAAkB;IAClB,KAAK,MAAM,CAAC,SAAS,IAAI,cAAe;QACtC,MAAM,SAAS,SAAS,MAAM;QAC9B,IAAI,QAAQ;YACV,IAAI,SAAS,KAAK,CAAC,MAAM,EAAE;gBACzB,OAAO,QAAQ;YACjB,OAAO,IAAI,WAAW,SAAS,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG;gBACnE,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,MAAM;YACtC;YACA,KAAK,MAAM,SAAS,SAAS,QAAQ,CAAE;gBACrC,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;oBACtB,OAAO,UAAU;gBACnB,OAAO,IAAI,WAAW,MAAM,MAAM,KAAK,WAAW,SAAS,MAAM,GAAG;oBAClE,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,MAAM;gBACrC;YACF;QACF;QAEA,uDAAuD;QACvD,qEAAqE;QACrE,wEAAwE;QACxE,iEAAiE;QACjE,IAAI,SAAS,QAAQ,EAAE,eAAe;QAEtC,gCAAgC;QAChC,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,SAAS,MAAM,CAAC,KAAK;QACvD,IAAI,SAAS,IAAI,KAAK,aAAa,cAAc,SAAS,MAAM;IAClE;IAEA,kBAAkB;IAClB,KAAK,MAAM,CAAC,UAAU,OAAO,MAAM,IAAI,cAAe;QACpD,SAAS,KAAK,GAAG;QACjB,MAAM,SAAS,SAAS,MAAM;QAC9B,IAAI,QAAQ;YACV,IAAI,uBAAuB;YAC3B,4BAA4B;YAC5B,MAAM,SAAS,SAAS,CAAC,aAAa,SAAS,IAAI,EAAE;YAErD,gBAAgB;YAChB,SAAS,MAAM,GAAG,CAAC,wBAAwB,SAAS,KAAK,CAAC,MAAM,KAAK,OAAO,wBAAwB,IAAI,UAAW,CAAC,uBAAuB,SAAS,KAAK,CAAC,IAAI,KAAK,OAAO,uBAAuB,EAAE;YACnM,SAAS,MAAM,CAAC,KAAK,GAAG;YACxB,YAAY,OAAO,SAAS,MAAM;YAElC,oBAAoB;YACpB,WAAW,SAAS,MAAM,EAAE,SAAS,KAAK;YAC1C,IAAI,SAAS,KAAK,CAAC,MAAM,EAAE;gBACzB,OAAO,QAAQ;YACjB,OAAO,IAAI,WAAW,SAAS,MAAM,KAAK,WAAW,OAAO,MAAM,GAAG;gBACnE,OAAO,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM;YACnC;YACA,KAAK,MAAM,SAAS,SAAS,QAAQ,CAAE;gBACrC,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;oBACtB,OAAO,UAAU;gBACnB,OAAO,IAAI,WAAW,MAAM,MAAM,KAAK,WAAW,SAAS,MAAM,GAAG;oBAClE,SAAS,MAAM,CAAC,GAAG,CAAC,MAAM,MAAM;gBAClC;YACF;YAEA,oCAAoC;YACpC,mBAAmB;QACrB;IACF;IACA,cAAc,MAAM,GAAG;AACzB;AAEA,6CAA6C;AAC7C,MAAM,qBAAqB,KAAO;AAClC,MAAM,aAAa,CAAC;AACpB,IAAI,wBAAwB;AAE5B,+FAA+F;AAC/F,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,aAAa,aAAa,GAAE,iBAAiB;IACjD,mBAAmB;IACnB,kBAAkB;IAClB,kBAAkB;IAClB,qBAAqB;IACrB,mBAAmB;IACnB;IACA;IACA;IACA,oBAAoB;IACpB;IACA,wBAAuB,SAAS,EAAE,KAAK;QACrC,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK,CAAC,KAAK;QAC9C,IAAI,CAAC,SAAS,CAAC,OAAO;QACtB,YAAY,OAAO;IACrB;IACA,0BAAyB,SAAS,EAAE,KAAK;QACvC,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK,CAAC,KAAK;QAC9C,IAAI,CAAC,SAAS,CAAC,OAAO;QACtB,YAAY,OAAO;IACrB;IACA,yBAAwB,SAAS,EAAE,KAAK,EAAE,WAAW;QACnD,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK,CAAC,KAAK;QAC9C,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO;QACtC,aAAa,OAAO,OAAO;IAC7B;IACA,oBAAoB,IAAM;IAC1B,qBAAqB,IAAM;IAC3B,cAAa,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK;QACpD,IAAI,gBAAgB,gBAAgB;QACpC,iBAAiB,MAAM;QACvB,IAAI,cAAc;QAElB,gDAAgD;QAChD,IAAI,SAAS,IAAI,KAAK,eAAe,SAAS,MAAM,KAAK,SAAS,MAAM,EAAE,cAAc;aAEnF,IAAI,CAAC,CAAC,iBAAiB,SAAS,IAAI,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM,MAAM,CAAC,CAAC,iBAAiB,SAAS,IAAI,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM,GAAG,cAAc;aAE/K,IAAI,CAAC,kBAAkB,SAAS,IAAI,KAAK,QAAQ,gBAAgB,IAAI,CAAC,CAAC,OAAO;YACjF,IAAI;YACJ,OAAO,UAAU,CAAC,CAAC,kBAAkB,SAAS,IAAI,KAAK,OAAO,KAAK,IAAI,eAAe,CAAC,MAAM;QAC/F,IAAI,cAAc;QAElB,gEAAgE;QAChE,IAAI,aAAa;YACf,cAAc,IAAI,CAAC;gBAAC;gBAAU;oBAC5B,GAAG,QAAQ;gBACb;gBAAG;aAAM;QACX,OAAO;YACL,mDAAmD;YACnD,MAAM,eAAe,UAAU,UAAU;YACzC,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,EAAE;gBACpC,OAAO,MAAM,CAAC,SAAS,KAAK,EAAE;gBAC9B,WAAW,SAAS,MAAM,EAAE;YAC9B;QACF;QAEA,gFAAgF;QAChF,MAAM,gBAAgB,MAAM,OAAO,KAAK,QAAQ,CAAC,MAAM,KAAK,GAAG,MAAM,MAAM;QAC3E,IAAI,eAAe;IACrB;IACA,yBAAyB,IAAM;IAC/B,gBAAe;IACf,mBAAmB,CAAA,WAAY,YAAY,OAAO,KAAK,IAAI,SAAS,MAAM;IAC1E,kBAAkB,IAAM;IACxB,oBAAoB,CAAA,YAAa,QAAQ,UAAU,QAAQ,GAAG,KAAK,EAAE,WAAW,IAAI,CAAC;IACrF,kBAAkB,KAAO;IACzB,sBAAsB,IAAM;IAC5B,gBAAgB,IAAM;IACtB;IACA;IACA,oBAAoB;IACpB,kBAAkB;IAClB,oBAAoB;IACpB,iBAAiB,OAAO,eAAe,aAAa,aAAa;IACjE,eAAe,OAAO,iBAAiB,aAAa,eAAe;IACnE,WAAW,CAAC;IACZ,qBAAqB,IAAM;IAC3B,6BAA4B;IAC5B,4BAA2B;IAC3B,0BAAyB;IACzB,uBAAsB;IACtB,sBAAsB,IAAM;IAC5B,8BAA8B,IAAM;IACpC,qBAAqB,KAAO;IAC5B,kBAAkB,IAAM;IACxB,uBAAuB,IAAM,CAAC;IAC9B,6BAA4B;IAC5B,kBAAkB,IAAM;IACxB,iBAAiB,IAAM;IACvB,gCAAgC;IAChC,0BAAyB;IACzB,oBAAmB;IACnB,wBAAwB,IAAM;IAC9B,sBAAsB;IACtB,uBAAuB,aAAa,GAAE,8JAAM,aAAa,CAAC;IAC1D,0BAAyB,WAAW;QAClC,wBAAwB;IAC1B;IACA;QACE,OAAO;IACT;IACA;QACE,IAAI;QACJ,IAAI,0BAA0B,iBAAiB,OAAO;QACtD,OAAQ,OAAO,WAAW,eAAe,CAAC,CAAC,gBAAgB,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,cAAc,IAAI;YAC5G,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,mJAAA,CAAA,wBAAqB;YAC9B,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,mJAAA,CAAA,0BAAuB;YAChC;gBACE,OAAO,mJAAA,CAAA,uBAAoB;QAC/B;IACF;IACA,sBAAqB;AACvB;AAEA,MAAM,SAAS,IAAI;AACnB,MAAM,eAAe;IACnB,SAAS;IACT,QAAQ;AACV;AACA,SAAS,mBAAmB,MAAM,EAAE,IAAI;IACtC,IAAI,CAAC,QAAQ,OAAO,sBAAsB,eAAe,kBAAkB,qBAAqB,OAAO,aAAa,EAAE;QACpH,MAAM,EACJ,KAAK,EACL,MAAM,EACN,GAAG,EACH,IAAI,EACL,GAAG,OAAO,aAAa,CAAC,qBAAqB;QAC9C,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,OAAO,IAAI,CAAC,QAAQ,OAAO,oBAAoB,eAAe,kBAAkB,iBAAiB;QAC/F,OAAO;YACL,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;YACrB,KAAK;YACL,MAAM;QACR;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;QACN,GAAG,IAAI;IACT;AACF;AACA,SAAS,WAAW,MAAM;IACxB,2CAA2C;IAC3C,MAAM,WAAW,OAAO,GAAG,CAAC;IAC5B,MAAM,YAAY,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK;IAC5D,MAAM,YAAY,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK;IAC5D,IAAI,UAAU,QAAQ,IAAI,CAAC;IAE3B,yDAAyD;IACzD,wDAAwD;IACxD,MAAM,sBAAsB,OAAO,gBAAgB,aACnD,gEAAgE;IAChE,0CAA0C;IAC1C,cACA,sEAAsE;IACtE,QAAQ,KAAK;IAEb,eAAe;IACf,MAAM,QAAQ,aAAa,YAAY,YAAY;IACnD,kBAAkB;IAClB,MAAM,QAAQ,aAAa,WAAW,eAAe,CAAC,OACtD,YAAY;IACZ,mJAAA,CAAA,iBAAc,EACd,MAAM;IACN,MACA,sBAAsB;IACtB,OACA,eAAe;IACf,MACA,qCAAqC;IACrC,IACA,mBAAmB;IACnB,qBACA,kBAAkB;IAClB,qBACA,gBAAgB;IAChB,qBACA,qBAAqB;IACrB,KAAK,sBAAsB;;IAE3B,SAAS;IACT,IAAI,CAAC,UAAU,OAAO,GAAG,CAAC,QAAQ;QAChC;QACA;IACF;IAEA,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI,aAAa;IACjB,IAAI,UAAU;IACd,OAAO;QACL,MAAM;gBAAU,QAAA,iEAAQ,CAAC;YACvB,IAAI;YACJ,UAAU,IAAI,QAAQ,CAAA,WAAY,UAAU;YAC5C,IAAI,EACF,IAAI,QAAQ,EACZ,MAAM,SAAS,EACf,OAAO,YAAY,EACnB,MAAM,EACN,WAAW,iBAAiB,EAC5B,UAAU,KAAK,EACf,SAAS,KAAK,EACd,OAAO,KAAK,EACZ,SAAS,KAAK,EACd,eAAe,KAAK,EACpB,YAAY,QAAQ,EACpB,MAAM;gBAAC;gBAAG;aAAE,EACZ,WAAW,EACX,WAAW,cAAc,EACzB,QAAQ,aAAa,EACrB,eAAe,EAChB,GAAG;YACJ,IAAI,QAAQ,MAAM,QAAQ;YAE1B,mCAAmC;YACnC,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,CAAC,MAAM,EAAE,EAAE;gBACb,MAAM,eAAe;oBACnB,QAAQ;oBACR,iBAAiB;oBACjB,WAAW;oBACX,OAAO;gBACT;gBACA,MAAM,iBAAiB,OAAO,aAAa,aAAa,MAAM,SAAS,gBAAgB;gBACvF,IAAI,WAAW,iBAAiB;oBAC9B,KAAK;gBACP,OAAO;oBACL,KAAK,IAAI,oKAAA,CAAA,gBAAmB,CAAC;wBAC3B,GAAG,YAAY;wBACf,GAAG,QAAQ;oBACb;gBACF;gBACA,MAAM,GAAG,CAAC;oBACR;gBACF;YACF;YAEA,oCAAoC;YACpC,IAAI,YAAY,MAAM,SAAS;YAC/B,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC;gBACxB,WAAW,YAAY,IAAI,kJAAA,CAAA,YAAe;YAC5C;YAEA,wBAAwB;YACxB,MAAM,EACJ,MAAM,EACN,GAAG,SACJ,GAAG,kBAAkB,CAAC;YACvB,IAAI,CAAC,GAAG,GAAG,CAAC,SAAS,WAAW,eAAe,WAAW,WAAW;gBACnE,GAAG,OAAO;YACZ;YACA,IAAI,CAAC,GAAG,GAAG,CAAC,QAAQ,UAAU,MAAM,EAAE,eAAe,WAAW,WAAW;gBACzE,QAAQ;oBACN,GAAG,UAAU,MAAM;oBACnB,GAAG,MAAM;gBACX;YACF;YAEA,4DAA4D;YAC5D,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,cAAc,CAAC,GAAG,GAAG,CAAC,YAAY,eAAe,eAAe;gBACpG,aAAa;gBACb,MAAM,WAAW,iBAAiB,OAAO,KAAK,IAAI,cAAc,QAAQ;gBACxE,MAAM,SAAS,WAAW,gBAAgB,eAAe,IAAI,kJAAA,CAAA,qBAAwB,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,QAAQ,IAAI,kJAAA,CAAA,oBAAuB,CAAC,IAAI,GAAG,KAAK;gBACvJ,IAAI,CAAC,UAAU;oBACb,OAAO,QAAQ,CAAC,CAAC,GAAG;oBACpB,IAAI,eAAe;wBACjB,WAAW,QAAQ;wBACnB,4CAA4C;wBAC5C,0DAA0D;wBAC1D,IAAI,CAAC,OAAO,MAAM,EAAE;4BAClB,IAAI,YAAY,iBAAiB,UAAU,iBAAiB,WAAW,iBAAiB,YAAY,iBAAiB,SAAS,eAAe;gCAC3I,OAAO,MAAM,GAAG;gCAChB,OAAO,sBAAsB;4BAC/B;wBACF;oBACF;oBACA,mCAAmC;oBACnC,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,iBAAiB,QAAQ,cAAc,QAAQ,GAAG,OAAO,MAAM,CAAC,GAAG,GAAG;gBAC/F;gBACA,MAAM,GAAG,CAAC;oBACR;gBACF;gBAEA,sBAAsB;gBACtB,gDAAgD;gBAChD,UAAU,MAAM,GAAG;YACrB;YAEA,gCAAgC;YAChC,IAAI,CAAC,MAAM,KAAK,EAAE;gBAChB,IAAI;gBACJ,IAAI,gBAAgB,QAAQ,aAAa,OAAO,EAAE;oBAChD,QAAQ;oBACR,QAAQ,OAAO,OAAO,IAAI,CAAC;gBAC7B,OAAO;oBACL,QAAQ,IAAI,kJAAA,CAAA,QAAW;oBACvB,QAAQ,OAAO,OAAO,IAAI,CAAC;oBAC3B,IAAI,cAAc,WAAW,OAAO;gBACtC;gBACA,MAAM,GAAG,CAAC;oBACR;gBACF;YACF;YAEA,0BAA0B;YAC1B,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC;gBAC9C,QAAQ,OAAO;YACjB;YACA,6DAA6D;YAC7D,MAAM,OAAO,mBAAmB,QAAQ;YACxC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,IAAI,EAAE,eAAe;gBAC3C,MAAM,OAAO,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;YAC5D;YACA,mBAAmB;YACnB,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,KAAK,aAAa,MAAM,MAAM,MAAM,CAAC;YAClE,kBAAkB;YAClB,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM,YAAY,CAAC;YACtD,uBAAuB;YACvB,IAAI,CAAC,MAAM,eAAe,EAAE,MAAM,GAAG,CAAC;gBACpC;YACF;YACA,oBAAoB;YACpB,IAAI,eAAe,CAAC,GAAG,GAAG,CAAC,aAAa,MAAM,WAAW,EAAE,eAAe,MAAM,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC5F,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,GAAG,WAAW;oBAChB;gBACF,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,MAAM,EAAE,EAAE;gBACb,IAAI;gBACJ,iCAAiC;gBACjC,MAAM,gBAAgB,CAAC,WAAW;oBAChC,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,IAAI,MAAM,SAAS,KAAK,SAAS;oBACjC,QAAQ,WAAW,MAAM,OAAO;gBAClC;gBAEA,qCAAqC;gBACrC,MAAM,sBAAsB;oBAC1B,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,MAAM,EAAE,CAAC,EAAE,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY;oBAC9C,MAAM,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,GAAG,gBAAgB;oBACxE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW;gBAC5C;gBAEA,wBAAwB;gBACxB,MAAM,KAAK;oBACT;wBACE,MAAM,KAAK,MAAM,QAAQ,GAAG,EAAE;wBAC9B,GAAG,EAAE,CAAC,gBAAgB,CAAC,gBAAgB;wBACvC,GAAG,EAAE,CAAC,gBAAgB,CAAC,cAAc;oBACvC;oBACA;wBACE,MAAM,KAAK,MAAM,QAAQ,GAAG,EAAE;wBAC9B,GAAG,EAAE,CAAC,mBAAmB,CAAC,gBAAgB;wBAC1C,GAAG,EAAE,CAAC,mBAAmB,CAAC,cAAc;oBAC1C;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,KAAK,OAAO,KAAK,IAAI,OAAO,gBAAgB,MAAM,YAAY,GAAG,OAAO;gBACnG,MAAM,GAAG,CAAC;oBACR;gBACF;YACF;YAEA,gBAAgB;YAChB,IAAI,GAAG,SAAS,EAAE;gBAChB,MAAM,aAAa,GAAG,SAAS,CAAC,OAAO;gBACvC,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI;gBACjC,GAAG,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;gBACzB,IAAI,GAAG,GAAG,CAAC,UAAU;oBACnB,GAAG,SAAS,CAAC,IAAI,GAAG,kJAAA,CAAA,mBAAsB;gBAC5C,OAAO,IAAI,GAAG,GAAG,CAAC,UAAU;oBAC1B,IAAI;oBACJ,MAAM,QAAQ;wBACZ,OAAO,kJAAA,CAAA,iBAAoB;wBAC3B,YAAY,kJAAA,CAAA,eAAkB;wBAC9B,MAAM,kJAAA,CAAA,mBAAsB;wBAC5B,UAAU,kJAAA,CAAA,eAAkB;oBAC9B;oBACA,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,iBAAiB,KAAK,CAAC,QAAQ,KAAK,OAAO,iBAAiB,kJAAA,CAAA,mBAAsB;gBACzG,OAAO,IAAI,GAAG,GAAG,CAAC,UAAU;oBAC1B,OAAO,MAAM,CAAC,GAAG,SAAS,EAAE;gBAC9B;gBACA,IAAI,eAAe,GAAG,SAAS,CAAC,OAAO,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,WAAW,GAAG;YACvG;YACA,kJAAA,CAAA,kBAAqB,CAAC,OAAO,GAAG,CAAC;YAEjC,8CAA8C;YAC9C,IAAI,CAAC,YAAY;gBACf,GAAG,gBAAgB,GAAG,SAAS,kJAAA,CAAA,uBAA0B,GAAG,kJAAA,CAAA,iBAAoB;gBAChF,GAAG,WAAW,GAAG,OAAO,kJAAA,CAAA,gBAAmB,GAAG,kJAAA,CAAA,wBAA2B;YAC3E;YAEA,gCAAgC;YAChC,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,CAAC,IAAM,CAAC;oBAC5C;gBACF,CAAC;YACD,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,CAAC,IAAM,CAAC;oBAC5C;gBACF,CAAC;YACD,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM,GAAG,CAAC,IAAM,CAAC;oBACxC;gBACF,CAAC;YAED,eAAe;YACf,IAAI,YAAY,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,WAAW,aAAa,CAAC,GAAG,GAAG,CAAC,UAAU,IAAI,eAAe,WAAW,IAAI;YAElH,aAAa;YACb,YAAY;YACZ,aAAa;YACb;YACA,OAAO,IAAI;QACb;QACA,QAAO,QAAQ;YACb,0DAA0D;YAC1D,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC,SAAS;YAC3C,QAAQ,IAAI,CAAC;gBACX,WAAW,eAAe,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,UAAU;oBACrD,OAAO;oBACP,UAAU;oBACV,WAAW;oBACX,aAAa;gBACf,IAAI,OAAO,MAAM,IAAM;YACzB;YACA,OAAO;QACT;QACA;YACE,uBAAuB;QACzB;IACF;AACF;AACA,SAAS,SAAS,KAKjB;QALiB,EAChB,KAAK,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACZ,GALiB;IAMhB;8CAA0B;YACxB,MAAM,QAAQ,MAAM,QAAQ;YAC5B,mDAAmD;YACnD,MAAM,GAAG;sDAAC,CAAA,QAAS,CAAC;wBAClB,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,QAAQ;wBACV;oBACF,CAAC;;YACD,sFAAsF;YACtF,IAAI,WAAW,UAAU;YACzB,wFAAwF;YACxF,gDAAgD;YAChD,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC;QACrG,uDAAuD;QACzD;6CAAG,EAAE;IACL,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,EAAE;QACxC,OAAO;QACP,UAAU;IACZ;AACF;AACA,SAAS,uBAAuB,MAAM,EAAE,QAAQ;IAC9C,MAAM,OAAO,OAAO,GAAG,CAAC;IACxB,MAAM,QAAQ,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK;IAChD,IAAI,OAAO;QACT,MAAM,QAAQ,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ;QACzD,IAAI,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;QACnC,WAAW,eAAe,CAAC,MAAM,OAAO,MAAM;YAC5C,IAAI,OAAO;gBACT,WAAW;oBACT,IAAI;wBACF,IAAI,WAAW,uBAAuB,YAAY;wBAClD,MAAM,MAAM,CAAC,UAAU,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,UAAU;wBAClE,CAAC,YAAY,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,CAAC,wBAAwB,UAAU,WAAW,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,IAAI,OAAO,KAAK,IAAI,sBAAsB,OAAO;wBAC3L,CAAC,aAAa,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,WAAW,gBAAgB,IAAI,OAAO,KAAK,IAAI,WAAW,gBAAgB;wBACrH,IAAI,CAAC,aAAa,MAAM,EAAE,KAAK,QAAQ,WAAW,EAAE,EAAE,MAAM,EAAE,CAAC,UAAU;wBACzE,QAAQ,MAAM,KAAK;wBACnB,OAAO,MAAM,CAAC;wBACd,IAAI,UAAU,SAAS;oBACzB,EAAE,OAAO,GAAG;oBACV,OAAO,GACT;gBACF,GAAG;YACL;QACF;IACF;AACF;AACA,SAAS,aAAa,QAAQ,EAAE,SAAS,EAAE,KAAK;IAC9C,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAC9B,UAAU;QACV,WAAW;QACX,OAAO;IACT;AACF;AACA,SAAS,OAAO,KAIf;QAJe,EACd,QAAQ,CAAC,CAAC,EACV,QAAQ,EACR,SAAS,EACV,GAJe;IAKd;;;;4BAI0B,GAC1B,MAAM,EACJ,MAAM,EACN,IAAI,EACJ,GAAG,MACJ,GAAG;IACJ,MAAM,eAAe;IACrB,MAAM,CAAC,UAAU,GAAG,8JAAM,QAAQ;2BAAC,IAAM,IAAI,kJAAA,CAAA,YAAe;;IAC5D,MAAM,CAAC,QAAQ,GAAG,8JAAM,QAAQ;2BAAC,IAAM,IAAI,kJAAA,CAAA,UAAa;;IACxD,MAAM,SAAS;6CAAmB,CAAC,WAAW;YAC5C,IAAI,WAAW;YACf,IAAI,YAAY,MAAM,IAAI,MAAM;gBAC9B,MAAM,SAAS,YAAY,MAAM;gBACjC,8CAA8C;gBAC9C,WAAW,UAAU,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,IAAI,kJAAA,CAAA,UAAa,IAAI;gBAC9E,kEAAkE;gBAClE,IAAI,WAAW,UAAU,MAAM,EAAE,aAAa,QAAQ;YACxD;YACA,OAAO;gBACL,oDAAoD;gBACpD,GAAG,SAAS;gBACZ,GAAG,WAAW;gBACd,gFAAgF;gBAChF,OAAO;gBACP;gBACA;gBACA,OAAO;gBACP,6CAA6C;gBAC7C;gBACA,kEAAkE;gBAClE,QAAQ;oBACN,GAAG,UAAU,MAAM;oBACnB,GAAG,YAAY,MAAM;oBACrB,GAAG,MAAM;gBACX;gBACA,MAAM;oBACJ,GAAG,UAAU,IAAI;oBACjB,GAAG,IAAI;gBACT;gBACA,UAAU;oBACR,GAAG,UAAU,QAAQ;oBACrB,GAAG,QAAQ;gBACb;gBACA,wCAAwC;gBACxC,SAAS;yDAAE,CAAA,SAAU,YAAY,GAAG;iEAAC,CAAA,QAAS,CAAC;oCAC7C,GAAG,KAAK;oCACR,QAAQ;wCACN,GAAG,MAAM,MAAM;wCACf,GAAG,MAAM;oCACX;gCACF,CAAC;;;YACH;QACF;;IACA,MAAM,iBAAiB,8JAAM,OAAO;0CAAC;YACnC,+EAA+E;YAC/E,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD;wDAAE,CAAC,KAAK,MAAQ,CAAC;wBAChD,GAAG,IAAI;wBACP;wBACA;oBACF,CAAC;;YAED,sFAAsF;YACtF,MAAM;2DAAW,CAAA,OAAQ,MAAM,QAAQ;mEAAC,CAAA,QAAS,OAAO,OAAO,CAAC,MAAM;;;YACtE,SAAS,aAAa,QAAQ;YAC9B,aAAa,SAAS,CAAC;YACvB,OAAO;QACP,uDAAuD;QACzD;yCAAG;QAAC;QAAc;KAAU;IAC5B,OACE,WAAW,GACX,kDAAkD;IAClD,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,sKAAA,CAAA,WAAQ,EAAE;QACZ,UAAU,WAAW,YAAY,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,EAAE;YACpE,OAAO;YACP,UAAU;QACZ,IAAI,gBAAgB;IACtB;AAEJ;AAEA;;;;;CAKC,GACD,SAAS,UAAU,EAAE;IACnB,OAAO,WAAW,SAAS,CAAC;AAC9B;AAEA,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,MAAM,MAAM;QACV;IACF;IACA,KAAK,GAAG,CAAC;IACT,OAAO,IAAM,KAAK,KAAK,MAAM,CAAC;AAChC;AACA,MAAM,gBAAgB,IAAI;AAC1B,MAAM,qBAAqB,IAAI;AAC/B,MAAM,oBAAoB,IAAI;AAE9B;;;CAGC,GACD,MAAM,YAAY,CAAA,WAAY,WAAW,UAAU;AAEnD;;;CAGC,GACD,MAAM,iBAAiB,CAAA,WAAY,WAAW,UAAU;AAExD;;;CAGC,GACD,MAAM,UAAU,CAAA,WAAY,WAAW,UAAU;AACjD,SAAS,IAAI,OAAO,EAAE,SAAS;IAC7B,IAAI,CAAC,QAAQ,IAAI,EAAE;IACnB,KAAK,MAAM,EACT,QAAQ,EACT,IAAI,QAAQ,MAAM,GAAI;QACrB,SAAS;IACX;AACF;AACA,SAAS,mBAAmB,IAAI,EAAE,SAAS;IACzC,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B,KAAK;YACH,OAAO,IAAI,oBAAoB;QACjC,KAAK;YACH,OAAO,IAAI,mBAAmB;IAClC;AACF;AACA,IAAI;AACJ,IAAI;AACJ,SAAS,OAAO,SAAS,EAAE,KAAK,EAAE,KAAK;IACrC,oBAAoB;IACpB,IAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ;IAEhC,kFAAkF;IAClF,IAAI,MAAM,SAAS,KAAK,WAAW,OAAO,cAAc,UAAU;QAChE,QAAQ,YAAY,MAAM,KAAK,CAAC,WAAW;QAC3C,MAAM,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,WAAW;QAC7C,MAAM,KAAK,CAAC,WAAW,GAAG;IAC5B;IAEA,8BAA8B;IAC9B,cAAc,MAAM,QAAQ,CAAC,WAAW;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,eAAe,WAAW,CAAC,EAAE;QAC7B,aAAa,GAAG,CAAC,OAAO,CAAC,aAAa,KAAK,CAAC,QAAQ,IAAI,OAAO;IACjE;IAEA,iBAAiB;IACjB,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,MAAM;IAE1F,uBAAuB;IACvB,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,GAAG;IAC5D,OAAO,MAAM,SAAS,KAAK,WAAW,IAAI,MAAM,QAAQ,CAAC,MAAM;AACjE;AACA,IAAI,UAAU;AACd,IAAI,qBAAqB;AACzB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,KAAK,SAAS;IACrB,QAAQ,sBAAsB;IAC9B,UAAU;IACV,SAAS;IAET,cAAc;IACd,mBAAmB,UAAU;IAE7B,mBAAmB;IACnB,qBAAqB;IACrB,KAAK,MAAM,QAAQ,OAAO,MAAM,GAAI;QAClC,IAAI;QACJ,QAAQ,KAAK,KAAK,CAAC,QAAQ;QAE3B,4DAA4D;QAC5D,IAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,SAAS,KAAK,YAAY,MAAM,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,aAAa,YAAY,GAAG;YAChK,UAAU,OAAO,WAAW;QAC9B;IACF;IACA,qBAAqB;IAErB,oBAAoB;IACpB,mBAAmB,SAAS;IAE5B,0CAA0C;IAC1C,IAAI,WAAW,GAAG;QAChB,0DAA0D;QAC1D,mBAAmB,QAAQ;QAE3B,wBAAwB;QACxB,UAAU;QACV,OAAO,qBAAqB;IAC9B;AACF;AAEA;;;CAGC,GACD,SAAS,WAAW,KAAK;QAAE,SAAA,iEAAS;IAClC,IAAI;IACJ,IAAI,CAAC,OAAO,OAAO,OAAO,OAAO,CAAC,CAAA,OAAQ,WAAW,KAAK,KAAK,CAAC,QAAQ,IAAI;IAC5E,IAAI,CAAC,gBAAgB,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,cAAc,YAAY,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,IAAI,MAAM,SAAS,KAAK,SAAS;IAClI,IAAI,SAAS,GAAG;QACd,oDAAoD;QACpD,4CAA4C;QAC5C,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG;IAC/D,OAAO;QACL,IAAI,oBAAoB;YACtB,4EAA4E;YAC5E,MAAM,QAAQ,CAAC,MAAM,GAAG;QAC1B,OAAO;YACL,gEAAgE;YAChE,MAAM,QAAQ,CAAC,MAAM,GAAG;QAC1B;IACF;IAEA,4CAA4C;IAC5C,IAAI,CAAC,SAAS;QACZ,UAAU;QACV,sBAAsB;IACxB;AACF;AAEA;;;CAGC,GACD,SAAS,QAAQ,SAAS;QAAE,mBAAA,iEAAmB,MAAM,sDAAO;IAC1D,IAAI,kBAAkB,mBAAmB,UAAU;IACnD,IAAI,CAAC,OAAO,KAAK,MAAM,QAAQ,OAAO,MAAM,GAAI,OAAO,WAAW,KAAK,KAAK,CAAC,QAAQ;SAAS,OAAO,WAAW,OAAO;IACvH,IAAI,kBAAkB,mBAAmB,SAAS;AACpD;AAEA,MAAM,aAAa;IACjB,SAAS;QAAC;QAAS;KAAM;IACzB,eAAe;QAAC;QAAe;KAAM;IACrC,eAAe;QAAC;QAAY;KAAM;IAClC,SAAS;QAAC;QAAS;KAAK;IACxB,eAAe;QAAC;QAAe;KAAK;IACpC,aAAa;QAAC;QAAa;KAAK;IAChC,gBAAgB;QAAC;QAAgB;KAAK;IACtC,eAAe;QAAC;QAAe;KAAK;IACpC,iBAAiB;QAAC;QAAiB;KAAK;IACxC,sBAAsB;QAAC;QAAsB;KAAK;AACpD;AAEA,sCAAsC,GACtC,SAAS,oBAAoB,KAAK;IAChC,MAAM,EACJ,aAAa,EACd,GAAG,aAAa;IACjB,OAAO;QACL,UAAU;QACV,SAAS;QACT,SAAQ,KAAK,EAAE,KAAK,EAAE,QAAQ;YAC5B,uDAAuD;YACvD,4FAA4F;YAC5F,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI;YACvG,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;QAC3D;QACA,WAAW;QACX,UAAU,OAAO,IAAI,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,CAAC;gBACtD,GAAG,GAAG;gBACN,CAAC,IAAI,EAAE,cAAc;YACvB,CAAC,GAAG,CAAC;QACL,QAAQ;YACN,IAAI;YACJ,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG,MAAM,QAAQ;YAClB,IAAI,CAAC,sBAAsB,SAAS,SAAS,KAAK,QAAQ,oBAAoB,OAAO,IAAI,OAAO,QAAQ,EAAE,OAAO,QAAQ,CAAC,aAAa,CAAC,SAAS,SAAS,CAAC,OAAO;QACpK;QACA,SAAS,CAAA;YACP,MAAM,EACJ,GAAG,EACH,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,OAAO,UAAU,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU;YACtD,IAAI,CAAA,QAAS,CAAC;oBACZ,QAAQ;wBACN,GAAG,MAAM,MAAM;wBACf,WAAW;oBACb;gBACF,CAAC;YACD,IAAI,OAAO,QAAQ,EAAE;gBACnB,IAAK,MAAM,QAAQ,OAAO,QAAQ,CAAE;oBAClC,MAAM,QAAQ,OAAO,QAAQ,CAAC,KAAK;oBACnC,MAAM,CAAC,WAAW,QAAQ,GAAG,UAAU,CAAC,KAAK;oBAC7C,OAAO,gBAAgB,CAAC,WAAW,OAAO;wBACxC;oBACF;gBACF;YACF;QACF;QACA,YAAY;YACV,MAAM,EACJ,GAAG,EACH,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,IAAI,OAAO,SAAS,EAAE;gBACpB,IAAI,OAAO,QAAQ,EAAE;oBACnB,IAAK,MAAM,QAAQ,OAAO,QAAQ,CAAE;wBAClC,MAAM,QAAQ,OAAO,QAAQ,CAAC,KAAK;wBACnC,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC,KAAK;wBACpC,OAAO,SAAS,CAAC,mBAAmB,CAAC,WAAW;oBAClD;gBACF;gBACA,IAAI,CAAA,QAAS,CAAC;wBACZ,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,WAAW;wBACb;oBACF,CAAC;YACH;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40react-three/fiber/dist/react-three-fiber.esm.js"], "sourcesContent": ["import { e as extend, u as useBridge, a as useMutableCallback, b as useIsomorphicLayoutEffect, c as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode, f as createPointerEvents } from './events-cf57b220.esm.js';\nexport { t as ReactThreeFiber, _ as _roots, x as act, k as addAfterEffect, j as addEffect, l as addTail, n as advance, s as applyProps, y as buildGraph, q as context, g as createEvents, o as createPortal, c as createRoot, w as dispose, f as events, e as extend, h as flushGlobalEffects, p as flushSync, v as getRootState, m as invalidate, r as reconciler, d as unmountComponentAtNode, D as useFrame, F as useGraph, z as useInstanceHandle, G as useLoader, A as useStore, C as useThree } from './events-cf57b220.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand/traditional';\nimport 'react-reconciler';\nimport 'scheduler';\nimport 'suspend-react';\n\nfunction CanvasImpl({\n  ref,\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      async function run() {\n        await root.current.configure({\n          gl,\n          scene,\n          events,\n          shadows,\n          linear,\n          flat,\n          legacy,\n          orthographic,\n          frameloop,\n          dpr,\n          performance,\n          raycaster,\n          camera,\n          size: containerRect,\n          // Pass mutable reference to onPointerMissed so it's free to update\n          onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n          onCreated: state => {\n            // Connect to event source\n            state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n            // Set up compute function\n            if (eventPrefix) {\n              state.setEvents({\n                compute: (event, state) => {\n                  const x = event[eventPrefix + 'X'];\n                  const y = event[eventPrefix + 'Y'];\n                  state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                  state.raycaster.setFromCamera(state.pointer, state.camera);\n                }\n              });\n            }\n            // Call onCreated callback\n            onCreated == null ? void 0 : onCreated(state);\n          }\n        });\n        root.current.render( /*#__PURE__*/jsx(Bridge, {\n          children: /*#__PURE__*/jsx(ErrorBoundary, {\n            set: setError,\n            children: /*#__PURE__*/jsx(React.Suspense, {\n              fallback: /*#__PURE__*/jsx(Block, {\n                set: setBlock\n              }),\n              children: children != null ? children : null\n            })\n          })\n        }));\n      }\n      run();\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n}\n\n/**\n * A DOM canvas which accepts threejs elements as children.\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\n */\nfunction Canvas(props) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props\n    })\n  });\n}\n\nexport { Canvas };\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;AAGA,SAAS,WAAW,KAwBnB;QAxBmB,EAClB,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,EAAE,EACF,SAAS,iLAAA,CAAA,IAAmB,EAC5B,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,SAAS,EACT,GAAG,EACH,WAAW,EACX,SAAS,EACT,MAAM,EACN,KAAK,EACL,eAAe,EACf,SAAS,EACT,GAAG,OACJ,GAxBmB;IAyBlB,sDAAsD;IACtD,4EAA4E;IAC5E,yDAAyD;IACzD,6JAAA,CAAA,UAAa;8BAAC,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAM,AAAD,EAAE;6BAAQ,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAS,AAAD;IACvB,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAU,AAAD,EAAE;QAC/C,QAAQ;QACR,UAAU;YACR,QAAQ;YACR,QAAQ;QACV;QACA,GAAG,MAAM;IACX;IACA,MAAM,YAAY,6JAAA,CAAA,SAAY,CAAC;IAC/B,MAAM,SAAS,6JAAA,CAAA,SAAY,CAAC;IAC5B,6JAAA,CAAA,sBAAyB,CAAC;0CAAK,IAAM,UAAU,OAAO;;IACtD,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEzC,yDAAyD;IACzD,IAAI,OAAO,MAAM;IACjB,4DAA4D;IAC5D,IAAI,OAAO,MAAM;IACjB,MAAM,OAAO,6JAAA,CAAA,SAAY,CAAC;IAC1B,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD;gDAAE;YACxB,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,cAAc,KAAK,GAAG,KAAK,cAAc,MAAM,GAAG,KAAK,QAAQ;gBACjE,IAAI,CAAC,KAAK,OAAO,EAAE,KAAK,OAAO,GAAG,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE;gBAC7C,eAAe;oBACb,MAAM,KAAK,OAAO,CAAC,SAAS,CAAC;wBAC3B;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA,MAAM;wBACN,mEAAmE;wBACnE,eAAe;wEAAE;iEAAI;oCAAA;;uCAAS,oBAAoB,OAAO,IAAI,OAAO,KAAK,IAAI,oBAAoB,OAAO,IAAI;;;wBAC5G,SAAS;wEAAE,CAAA;gCACT,0BAA0B;gCAC1B,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA,GAAA,iLAAA,CAAA,IAAK,AAAD,EAAE,eAAe,YAAY,OAAO,GAAG,cAAc,OAAO,OAAO;gCAClJ,0BAA0B;gCAC1B,IAAI,aAAa;oCACf,MAAM,SAAS,CAAC;wCACd,OAAO;wFAAE,CAAC,OAAO;gDACf,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI;gDAClC,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI;gDAClC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI;gDAC/E,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;4CAC3D;;oCACF;gCACF;gCACA,0BAA0B;gCAC1B,aAAa,OAAO,KAAK,IAAI,UAAU;4BACzC;;oBACF;oBACA,KAAK,OAAO,CAAC,MAAM,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;wBAC5C,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,iLAAA,CAAA,IAAa,EAAE;4BACxC,KAAK;4BACL,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,6JAAA,CAAA,WAAc,EAAE;gCACzC,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,iLAAA,CAAA,IAAK,EAAE;oCAChC,KAAK;gCACP;gCACA,UAAU,YAAY,OAAO,WAAW;4BAC1C;wBACF;oBACF;gBACF;gBACA;YACF;QACF;;IACA,6JAAA,CAAA,YAAe;gCAAC;YACd,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,QAAQ;wCAAO,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;;QAClD;+BAAG,EAAE;IAEL,+EAA+E;IAC/E,sEAAsE;IACtE,MAAM,gBAAgB,cAAc,SAAS;IAC7C,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,KAAK;QACL,OAAO;YACL,UAAU;YACV,OAAO;YACP,QAAQ;YACR,UAAU;YACV;YACA,GAAG,KAAK;QACV;QACA,GAAG,KAAK;QACR,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAChC,KAAK;YACL,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;YACA,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,UAAU;gBACnC,KAAK;gBACL,OAAO;oBACL,SAAS;gBACX;gBACA,UAAU;YACZ;QACF;IACF;AACF;AAEA;;;CAGC,GACD,SAAS,OAAO,KAAK;IACnB,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,+IAAA,CAAA,gBAAa,EAAE;QACrC,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,YAAY;YACrC,GAAG,KAAK;QACV;IACF;AACF", "ignoreList": [0], "debugId": null}}]}