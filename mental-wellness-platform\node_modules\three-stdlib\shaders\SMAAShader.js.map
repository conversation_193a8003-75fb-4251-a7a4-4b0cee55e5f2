{"version": 3, "file": "SMAAShader.js", "sources": ["../../src/shaders/SMAAShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * WebGL port of Subpixel Morphological Antialiasing (SMAA) v2.8\n * Preset: SMAA 1x Medium (with color edge detection)\n * https://github.com/iryoku/smaa/releases/tag/v2.8\n */\n\nexport const SMAAEdgesShader = {\n  defines: {\n    SMAA_THRESHOLD: '0.1',\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2(1 / 1024, 1 / 512) },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[ 3 ];\n\n    void SMAAEdgeDetectionVS( vec2 texcoord ) {\n    \tvOffset[ 0 ] = texcoord.xyxy + resolution.xyxy * vec4( -1.0, 0.0, 0.0,  1.0 ); // WebGL port note: Changed sign in W component\n    \tvOffset[ 1 ] = texcoord.xyxy + resolution.xyxy * vec4(  1.0, 0.0, 0.0, -1.0 ); // WebGL port note: Changed sign in W component\n    \tvOffset[ 2 ] = texcoord.xyxy + resolution.xyxy * vec4( -2.0, 0.0, 0.0,  2.0 ); // WebGL port note: Changed sign in W component\n    }\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tSMAAEdgeDetectionVS( vUv );\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[ 3 ];\n\n    vec4 SMAAColorEdgeDetectionPS( vec2 texcoord, vec4 offset[3], sampler2D colorTex ) {\n    \tvec2 threshold = vec2( SMAA_THRESHOLD, SMAA_THRESHOLD );\n\n    // Calculate color deltas:\n    \tvec4 delta;\n    \tvec3 C = texture2D( colorTex, texcoord ).rgb;\n\n    \tvec3 Cleft = texture2D( colorTex, offset[0].xy ).rgb;\n    \tvec3 t = abs( C - Cleft );\n    \tdelta.x = max( max( t.r, t.g ), t.b );\n\n    \tvec3 Ctop = texture2D( colorTex, offset[0].zw ).rgb;\n    \tt = abs( C - Ctop );\n    \tdelta.y = max( max( t.r, t.g ), t.b );\n\n    // We do the usual threshold:\n    \tvec2 edges = step( threshold, delta.xy );\n\n    // Then discard if there is no edge:\n    \tif ( dot( edges, vec2( 1.0, 1.0 ) ) == 0.0 )\n    \t\tdiscard;\n\n    // Calculate right and bottom deltas:\n    \tvec3 Cright = texture2D( colorTex, offset[1].xy ).rgb;\n    \tt = abs( C - Cright );\n    \tdelta.z = max( max( t.r, t.g ), t.b );\n\n    \tvec3 Cbottom  = texture2D( colorTex, offset[1].zw ).rgb;\n    \tt = abs( C - Cbottom );\n    \tdelta.w = max( max( t.r, t.g ), t.b );\n\n    // Calculate the maximum delta in the direct neighborhood:\n    \tfloat maxDelta = max( max( max( delta.x, delta.y ), delta.z ), delta.w );\n\n    // Calculate left-left and top-top deltas:\n    \tvec3 Cleftleft  = texture2D( colorTex, offset[2].xy ).rgb;\n    \tt = abs( C - Cleftleft );\n    \tdelta.z = max( max( t.r, t.g ), t.b );\n\n    \tvec3 Ctoptop = texture2D( colorTex, offset[2].zw ).rgb;\n    \tt = abs( C - Ctoptop );\n    \tdelta.w = max( max( t.r, t.g ), t.b );\n\n    // Calculate the final maximum delta:\n    \tmaxDelta = max( max( maxDelta, delta.z ), delta.w );\n\n    // Local contrast adaptation in action:\n    \tedges.xy *= step( 0.5 * maxDelta, delta.xy );\n\n    \treturn vec4( edges, 0.0, 0.0 );\n    }\n\n    void main() {\n\n    \tgl_FragColor = SMAAColorEdgeDetectionPS( vUv, vOffset, tDiffuse );\n\n    }\n  `,\n}\n\nexport const SMAAWeightsShader = {\n  defines: {\n    SMAA_MAX_SEARCH_STEPS: '8',\n    SMAA_AREATEX_MAX_DISTANCE: '16',\n    SMAA_AREATEX_PIXEL_SIZE: '( 1.0 / vec2( 160.0, 560.0 ) )',\n    SMAA_AREATEX_SUBTEX_SIZE: '( 1.0 / 7.0 )',\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    tArea: { value: null },\n    tSearch: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2(1 / 1024, 1 / 512) },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[ 3 ];\n    varying vec2 vPixcoord;\n\n    void SMAABlendingWeightCalculationVS( vec2 texcoord ) {\n    \tvPixcoord = texcoord / resolution;\n\n    // We will use these offsets for the searches later on (see @PSEUDO_GATHER4):\n    \tvOffset[ 0 ] = texcoord.xyxy + resolution.xyxy * vec4( -0.25, 0.125, 1.25, 0.125 ); // WebGL port note: Changed sign in Y and W components\n    \tvOffset[ 1 ] = texcoord.xyxy + resolution.xyxy * vec4( -0.125, 0.25, -0.125, -1.25 ); // WebGL port note: Changed sign in Y and W components\n\n    // And these for the searches, they indicate the ends of the loops:\n    \tvOffset[ 2 ] = vec4( vOffset[ 0 ].xz, vOffset[ 1 ].yw ) + vec4( -2.0, 2.0, -2.0, 2.0 ) * resolution.xxyy * float( SMAA_MAX_SEARCH_STEPS );\n\n    }\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tSMAABlendingWeightCalculationVS( vUv );\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #define SMAASampleLevelZeroOffset( tex, coord, offset ) texture2D( tex, coord + float( offset ) * resolution, 0.0 )\n\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tArea;\n    uniform sampler2D tSearch;\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[3];\n    varying vec2 vPixcoord;\n\n    #if __VERSION__ == 100\n    vec2 round( vec2 x ) {\n    \treturn sign( x ) * floor( abs( x ) + 0.5 );\n    }\n    #endif\n\n    float SMAASearchLength( sampler2D searchTex, vec2 e, float bias, float scale ) {\n    // Not required if searchTex accesses are set to point:\n    // float2 SEARCH_TEX_PIXEL_SIZE = 1.0 / float2(66.0, 33.0);\n    // e = float2(bias, 0.0) + 0.5 * SEARCH_TEX_PIXEL_SIZE +\n    //     e * float2(scale, 1.0) * float2(64.0, 32.0) * SEARCH_TEX_PIXEL_SIZE;\n    \te.r = bias + e.r * scale;\n    \treturn 255.0 * texture2D( searchTex, e, 0.0 ).r;\n    }\n\n    float SMAASearchXLeft( sampler2D edgesTex, sampler2D searchTex, vec2 texcoord, float end ) {\n    /**\n     * @PSEUDO_GATHER4\n     * This texcoord has been offset by (-0.25, -0.125) in the vertex shader to\n     * sample between edge, thus fetching four edges in a row.\n     * Sampling with different offsets in each direction allows to disambiguate\n     * which edges are active from the four fetched ones.\n     */\n    \tvec2 e = vec2( 0.0, 1.0 );\n\n    \tfor ( int i = 0; i < SMAA_MAX_SEARCH_STEPS; i ++ ) { // WebGL port note: Changed while to for\n    \t\te = texture2D( edgesTex, texcoord, 0.0 ).rg;\n    \t\ttexcoord -= vec2( 2.0, 0.0 ) * resolution;\n    \t\tif ( ! ( texcoord.x > end && e.g > 0.8281 && e.r == 0.0 ) ) break;\n    \t}\n\n    // We correct the previous (-0.25, -0.125) offset we applied:\n    \ttexcoord.x += 0.25 * resolution.x;\n\n    // The searches are bias by 1, so adjust the coords accordingly:\n    \ttexcoord.x += resolution.x;\n\n    // Disambiguate the length added by the last step:\n    \ttexcoord.x += 2.0 * resolution.x; // Undo last step\n    \ttexcoord.x -= resolution.x * SMAASearchLength(searchTex, e, 0.0, 0.5);\n\n    \treturn texcoord.x;\n    }\n\n    float SMAASearchXRight( sampler2D edgesTex, sampler2D searchTex, vec2 texcoord, float end ) {\n    \tvec2 e = vec2( 0.0, 1.0 );\n\n    \tfor ( int i = 0; i < SMAA_MAX_SEARCH_STEPS; i ++ ) { // WebGL port note: Changed while to for\n    \t\te = texture2D( edgesTex, texcoord, 0.0 ).rg;\n    \t\ttexcoord += vec2( 2.0, 0.0 ) * resolution;\n    \t\tif ( ! ( texcoord.x < end && e.g > 0.8281 && e.r == 0.0 ) ) break;\n    \t}\n\n    \ttexcoord.x -= 0.25 * resolution.x;\n    \ttexcoord.x -= resolution.x;\n    \ttexcoord.x -= 2.0 * resolution.x;\n    \ttexcoord.x += resolution.x * SMAASearchLength( searchTex, e, 0.5, 0.5 );\n\n    \treturn texcoord.x;\n    }\n\n    float SMAASearchYUp( sampler2D edgesTex, sampler2D searchTex, vec2 texcoord, float end ) {\n    \tvec2 e = vec2( 1.0, 0.0 );\n\n    \tfor ( int i = 0; i < SMAA_MAX_SEARCH_STEPS; i ++ ) { // WebGL port note: Changed while to for\n    \t\te = texture2D( edgesTex, texcoord, 0.0 ).rg;\n    \t\ttexcoord += vec2( 0.0, 2.0 ) * resolution; // WebGL port note: Changed sign\n    \t\tif ( ! ( texcoord.y > end && e.r > 0.8281 && e.g == 0.0 ) ) break;\n    \t}\n\n    \ttexcoord.y -= 0.25 * resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y -= resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y -= 2.0 * resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y += resolution.y * SMAASearchLength( searchTex, e.gr, 0.0, 0.5 ); // WebGL port note: Changed sign\n\n    \treturn texcoord.y;\n    }\n\n    float SMAASearchYDown( sampler2D edgesTex, sampler2D searchTex, vec2 texcoord, float end ) {\n    \tvec2 e = vec2( 1.0, 0.0 );\n\n    \tfor ( int i = 0; i < SMAA_MAX_SEARCH_STEPS; i ++ ) { // WebGL port note: Changed while to for\n    \t\te = texture2D( edgesTex, texcoord, 0.0 ).rg;\n    \t\ttexcoord -= vec2( 0.0, 2.0 ) * resolution; // WebGL port note: Changed sign\n    \t\tif ( ! ( texcoord.y < end && e.r > 0.8281 && e.g == 0.0 ) ) break;\n    \t}\n\n    \ttexcoord.y += 0.25 * resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y += resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y += 2.0 * resolution.y; // WebGL port note: Changed sign\n    \ttexcoord.y -= resolution.y * SMAASearchLength( searchTex, e.gr, 0.5, 0.5 ); // WebGL port note: Changed sign\n\n    \treturn texcoord.y;\n    }\n\n    vec2 SMAAArea( sampler2D areaTex, vec2 dist, float e1, float e2, float offset ) {\n    // Rounding prevents precision errors of bilinear filtering:\n    \tvec2 texcoord = float( SMAA_AREATEX_MAX_DISTANCE ) * round( 4.0 * vec2( e1, e2 ) ) + dist;\n\n    // We do a scale and bias for mapping to texel space:\n    \ttexcoord = SMAA_AREATEX_PIXEL_SIZE * texcoord + ( 0.5 * SMAA_AREATEX_PIXEL_SIZE );\n\n    // Move to proper place, according to the subpixel offset:\n    \ttexcoord.y += SMAA_AREATEX_SUBTEX_SIZE * offset;\n\n    \treturn texture2D( areaTex, texcoord, 0.0 ).rg;\n    }\n\n    vec4 SMAABlendingWeightCalculationPS( vec2 texcoord, vec2 pixcoord, vec4 offset[ 3 ], sampler2D edgesTex, sampler2D areaTex, sampler2D searchTex, ivec4 subsampleIndices ) {\n    \tvec4 weights = vec4( 0.0, 0.0, 0.0, 0.0 );\n\n    \tvec2 e = texture2D( edgesTex, texcoord ).rg;\n\n    \tif ( e.g > 0.0 ) { // Edge at north\n    \t\tvec2 d;\n\n    // Find the distance to the left:\n    \t\tvec2 coords;\n    \t\tcoords.x = SMAASearchXLeft( edgesTex, searchTex, offset[ 0 ].xy, offset[ 2 ].x );\n    \t\tcoords.y = offset[ 1 ].y; // offset[1].y = texcoord.y - 0.25 * resolution.y (@CROSSING_OFFSET)\n    \t\td.x = coords.x;\n\n    // Now fetch the left crossing edges, two at a time using bilinear\n    // filtering. Sampling at -0.25 (see @CROSSING_OFFSET) enables to\n    // discern what value each edge has:\n    \t\tfloat e1 = texture2D( edgesTex, coords, 0.0 ).r;\n\n    // Find the distance to the right:\n    \t\tcoords.x = SMAASearchXRight( edgesTex, searchTex, offset[ 0 ].zw, offset[ 2 ].y );\n    \t\td.y = coords.x;\n\n    // We want the distances to be in pixel units (doing this here allow to\n    // better interleave arithmetic and memory accesses):\n    \t\td = d / resolution.x - pixcoord.x;\n\n    // SMAAArea below needs a sqrt, as the areas texture is compressed\n    // quadratically:\n    \t\tvec2 sqrt_d = sqrt( abs( d ) );\n\n    // Fetch the right crossing edges:\n    \t\tcoords.y -= 1.0 * resolution.y; // WebGL port note: Added\n    \t\tfloat e2 = SMAASampleLevelZeroOffset( edgesTex, coords, ivec2( 1, 0 ) ).r;\n\n    // Ok, we know how this pattern looks like, now it is time for getting\n    // the actual area:\n    \t\tweights.rg = SMAAArea( areaTex, sqrt_d, e1, e2, float( subsampleIndices.y ) );\n    \t}\n\n    \tif ( e.r > 0.0 ) { // Edge at west\n    \t\tvec2 d;\n\n    // Find the distance to the top:\n    \t\tvec2 coords;\n\n    \t\tcoords.y = SMAASearchYUp( edgesTex, searchTex, offset[ 1 ].xy, offset[ 2 ].z );\n    \t\tcoords.x = offset[ 0 ].x; // offset[1].x = texcoord.x - 0.25 * resolution.x;\n    \t\td.x = coords.y;\n\n    // Fetch the top crossing edges:\n    \t\tfloat e1 = texture2D( edgesTex, coords, 0.0 ).g;\n\n    // Find the distance to the bottom:\n    \t\tcoords.y = SMAASearchYDown( edgesTex, searchTex, offset[ 1 ].zw, offset[ 2 ].w );\n    \t\td.y = coords.y;\n\n    // We want the distances to be in pixel units:\n    \t\td = d / resolution.y - pixcoord.y;\n\n    // SMAAArea below needs a sqrt, as the areas texture is compressed\n    // quadratically:\n    \t\tvec2 sqrt_d = sqrt( abs( d ) );\n\n    // Fetch the bottom crossing edges:\n    \t\tcoords.y -= 1.0 * resolution.y; // WebGL port note: Added\n    \t\tfloat e2 = SMAASampleLevelZeroOffset( edgesTex, coords, ivec2( 0, 1 ) ).g;\n\n    // Get the area for this direction:\n    \t\tweights.ba = SMAAArea( areaTex, sqrt_d, e1, e2, float( subsampleIndices.x ) );\n    \t}\n\n    \treturn weights;\n    }\n\n    void main() {\n\n    \tgl_FragColor = SMAABlendingWeightCalculationPS( vUv, vPixcoord, vOffset, tDiffuse, tArea, tSearch, ivec4( 0.0 ) );\n\n    }\n  `,\n}\n\nexport const SMAABlendShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    tColor: { value: null },\n    resolution: { value: /* @__PURE__ */ new Vector2(1 / 1024, 1 / 512) },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[ 2 ];\n\n    void SMAANeighborhoodBlendingVS( vec2 texcoord ) {\n    \tvOffset[ 0 ] = texcoord.xyxy + resolution.xyxy * vec4( -1.0, 0.0, 0.0, 1.0 ); // WebGL port note: Changed sign in W component\n    \tvOffset[ 1 ] = texcoord.xyxy + resolution.xyxy * vec4( 1.0, 0.0, 0.0, -1.0 ); // WebGL port note: Changed sign in W component\n    }\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tSMAANeighborhoodBlendingVS( vUv );\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform sampler2D tColor;\n    uniform vec2 resolution;\n\n    varying vec2 vUv;\n    varying vec4 vOffset[ 2 ];\n\n    vec4 SMAANeighborhoodBlendingPS( vec2 texcoord, vec4 offset[ 2 ], sampler2D colorTex, sampler2D blendTex ) {\n    // Fetch the blending weights for current pixel:\n    \tvec4 a;\n    \ta.xz = texture2D( blendTex, texcoord ).xz;\n    \ta.y = texture2D( blendTex, offset[ 1 ].zw ).g;\n    \ta.w = texture2D( blendTex, offset[ 1 ].xy ).a;\n\n    // Is there any blending weight with a value greater than 0.0?\n    \tif ( dot(a, vec4( 1.0, 1.0, 1.0, 1.0 )) < 1e-5 ) {\n    \t\treturn texture2D( colorTex, texcoord, 0.0 );\n    \t} else {\n    // Up to 4 lines can be crossing a pixel (one through each edge). We\n    // favor blending by choosing the line with the maximum weight for each\n    // direction:\n    \t\tvec2 offset;\n    \t\toffset.x = a.a > a.b ? a.a : -a.b; // left vs. right\n    \t\toffset.y = a.g > a.r ? -a.g : a.r; // top vs. bottom // WebGL port note: Changed signs\n\n    // Then we go in the direction that has the maximum weight:\n    \t\tif ( abs( offset.x ) > abs( offset.y )) { // horizontal vs. vertical\n    \t\t\toffset.y = 0.0;\n    \t\t} else {\n    \t\t\toffset.x = 0.0;\n    \t\t}\n\n    // Fetch the opposite color and lerp by hand:\n    \t\tvec4 C = texture2D( colorTex, texcoord, 0.0 );\n    \t\ttexcoord += sign( offset ) * resolution;\n    \t\tvec4 Cop = texture2D( colorTex, texcoord, 0.0 );\n    \t\tfloat s = abs( offset.x ) > abs( offset.y ) ? abs( offset.x ) : abs( offset.y );\n\n    // WebGL port note: Added gamma correction\n    \t\tC.xyz = pow(C.xyz, vec3(2.2));\n    \t\tCop.xyz = pow(Cop.xyz, vec3(2.2));\n    \t\tvec4 mixed = mix(C, Cop, s);\n    \t\tmixed.xyz = pow(mixed.xyz, vec3(1.0 / 2.2));\n\n    \t\treturn mixed;\n    \t}\n    }\n\n    void main() {\n\n    \tgl_FragColor = SMAANeighborhoodBlendingPS( vUv, vOffset, tColor, tDiffuse );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AAQO,MAAM,kBAAkB;AAAA,EAC7B,SAAS;AAAA,IACP,gBAAgB;AAAA,EAClB;AAAA,EAEA,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,YAAY,EAAE,OAAuB,oBAAI,QAAQ,IAAI,MAAM,IAAI,GAAG,EAAE;AAAA,EACtE;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgE7B;AAEO,MAAM,oBAAoB;AAAA,EAC/B,SAAS;AAAA,IACP,uBAAuB;AAAA,IACvB,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,EAC5B;AAAA,EAEA,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,OAAO,EAAE,OAAO,KAAK;AAAA,IACrB,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,YAAY,EAAE,OAAuB,oBAAI,QAAQ,IAAI,MAAM,IAAI,GAAG,EAAE;AAAA,EACtE;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyM7B;AAEO,MAAM,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,YAAY,EAAE,OAAuB,oBAAI,QAAQ,IAAI,MAAM,IAAI,GAAG,EAAE;AAAA,EACtE;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuD7B;"}