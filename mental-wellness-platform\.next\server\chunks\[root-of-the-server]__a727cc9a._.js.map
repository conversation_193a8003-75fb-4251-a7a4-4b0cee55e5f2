{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/constants.ts"], "sourcesContent": ["// App configuration\nexport const APP_CONFIG = {\n  name: 'Mental Wellness Platform',\n  description: 'AI-powered mental wellness support for women and children',\n  version: '1.0.0',\n  supportEmail: '<EMAIL>',\n  emergencyNumber: '911',\n} as const\n\n// API endpoints\nexport const API_ENDPOINTS = {\n  chat: '/api/chat',\n  emotions: '/api/emotions',\n  safety: '/api/safety',\n  games: '/api/games',\n  content: '/api/content',\n  resources: '/api/resources',\n  users: '/api/users',\n} as const\n\n// Safety keywords for monitoring\nexport const SAFETY_KEYWORDS = {\n  distress: [\n    'help me', 'scared', 'afraid', 'hurt', 'pain', 'crying',\n    'sad', 'depressed', 'anxious', 'worried', 'stressed'\n  ],\n  abuse: [\n    'abuse', 'hit', 'hurt me', 'touch me', 'secret', 'don\\'t tell',\n    'inappropriate', 'uncomfortable', 'forced', 'threatened'\n  ],\n  selfHarm: [\n    'hurt myself', 'kill myself', 'suicide', 'cut myself', 'die',\n    'end it all', 'not worth living', 'better off dead'\n  ],\n  emergency: [\n    'emergency', 'call 911', 'help now', 'immediate help',\n    'danger', 'unsafe', 'call police'\n  ]\n} as const\n\n// Emotion categories\nexport const EMOTIONS = {\n  positive: ['joy', 'happiness', 'excitement', 'calm', 'peaceful', 'confident'],\n  negative: ['sadness', 'anger', 'fear', 'anxiety', 'stress', 'frustration'],\n  neutral: ['neutral', 'content', 'focused', 'curious']\n} as const\n\n// Age groups\nexport const AGE_GROUPS = {\n  child: { min: 5, max: 12, label: 'Children (5-12)' },\n  teen: { min: 13, max: 17, label: 'Teenagers (13-17)' },\n  adult: { min: 18, max: 100, label: 'Adults (18+)' }\n} as const\n\n// Game categories\nexport const GAME_CATEGORIES = {\n  stress_relief: 'Stress Relief',\n  coping_skills: 'Coping Skills',\n  mindfulness: 'Mindfulness',\n  emotional_regulation: 'Emotional Regulation'\n} as const\n\n// Content categories\nexport const CONTENT_CATEGORIES = {\n  emotional_intelligence: 'Emotional Intelligence',\n  safety: 'Safety & Protection',\n  coping_skills: 'Coping Skills',\n  wellness: 'General Wellness'\n} as const\n\n// Resource types\nexport const RESOURCE_TYPES = {\n  helpline: 'Helpline',\n  ngo: 'NGO/Organization',\n  emergency: 'Emergency Service',\n  educational: 'Educational Resource',\n  therapy: 'Therapy/Counseling'\n} as const\n\n// Risk levels\nexport const RISK_LEVELS = {\n  low: { color: 'green', label: 'Low Risk' },\n  medium: { color: 'yellow', label: 'Medium Risk' },\n  high: { color: 'orange', label: 'High Risk' },\n  critical: { color: 'red', label: 'Critical Risk' }\n} as const\n\n// Default emergency contacts\nexport const EMERGENCY_CONTACTS = [\n  {\n    name: 'National Suicide Prevention Lifeline',\n    phone: '988',\n    description: '24/7 crisis support'\n  },\n  {\n    name: 'Crisis Text Line',\n    phone: 'Text HOME to 741741',\n    description: 'Text-based crisis support'\n  },\n  {\n    name: 'National Child Abuse Hotline',\n    phone: '1-800-4-A-CHILD (**************)',\n    description: '24/7 child abuse prevention and treatment'\n  },\n  {\n    name: 'National Domestic Violence Hotline',\n    phone: '**************',\n    description: '24/7 domestic violence support'\n  }\n] as const\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;AACb,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,cAAc;IACd,iBAAiB;AACnB;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,WAAW;IACX,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,UAAU;QACR;QAAW;QAAU;QAAU;QAAQ;QAAQ;QAC/C;QAAO;QAAa;QAAW;QAAW;KAC3C;IACD,OAAO;QACL;QAAS;QAAO;QAAW;QAAY;QAAU;QACjD;QAAiB;QAAiB;QAAU;KAC7C;IACD,UAAU;QACR;QAAe;QAAe;QAAW;QAAc;QACvD;QAAc;QAAoB;KACnC;IACD,WAAW;QACT;QAAa;QAAY;QAAY;QACrC;QAAU;QAAU;KACrB;AACH;AAGO,MAAM,WAAW;IACtB,UAAU;QAAC;QAAO;QAAa;QAAc;QAAQ;QAAY;KAAY;IAC7E,UAAU;QAAC;QAAW;QAAS;QAAQ;QAAW;QAAU;KAAc;IAC1E,SAAS;QAAC;QAAW;QAAW;QAAW;KAAU;AACvD;AAGO,MAAM,aAAa;IACxB,OAAO;QAAE,KAAK;QAAG,KAAK;QAAI,OAAO;IAAkB;IACnD,MAAM;QAAE,KAAK;QAAI,KAAK;QAAI,OAAO;IAAoB;IACrD,OAAO;QAAE,KAAK;QAAI,KAAK;QAAK,OAAO;IAAe;AACpD;AAGO,MAAM,kBAAkB;IAC7B,eAAe;IACf,eAAe;IACf,aAAa;IACb,sBAAsB;AACxB;AAGO,MAAM,qBAAqB;IAChC,wBAAwB;IACxB,QAAQ;IACR,eAAe;IACf,UAAU;AACZ;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,KAAK;IACL,WAAW;IACX,aAAa;IACb,SAAS;AACX;AAGO,MAAM,cAAc;IACzB,KAAK;QAAE,OAAO;QAAS,OAAO;IAAW;IACzC,QAAQ;QAAE,OAAO;QAAU,OAAO;IAAc;IAChD,MAAM;QAAE,OAAO;QAAU,OAAO;IAAY;IAC5C,UAAU;QAAE,OAAO;QAAO,OAAO;IAAgB;AACnD;AAGO,MAAM,qBAAqB;IAChC;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/[<>]/g, '')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,OAAO,CAAC,uDAAuD,IACzE,OAAO,CAAC,SAAS,IACjB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport OpenAI from 'openai'\nimport { SAFETY_KEYWORDS } from '@/lib/constants'\nimport { sanitizeInput } from '@/lib/utils'\n\n// Initialize OpenAI client (optional - will work without API key)\nconst openai = process.env.OPENAI_API_KEY ? new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n}) : null\n\n// Safety monitoring function\nfunction detectSafetyRisk(message: string): { riskLevel: string; keywords: string[] } {\n  const lowerMessage = message.toLowerCase()\n  const detectedKeywords: string[] = []\n  let riskLevel = 'low'\n\n  // Check for emergency keywords\n  for (const keyword of SAFETY_KEYWORDS.emergency) {\n    if (lowerMessage.includes(keyword)) {\n      detectedKeywords.push(keyword)\n      riskLevel = 'critical'\n    }\n  }\n\n  // Check for self-harm keywords\n  if (riskLevel !== 'critical') {\n    for (const keyword of SAFETY_KEYWORDS.selfHarm) {\n      if (lowerMessage.includes(keyword)) {\n        detectedKeywords.push(keyword)\n        riskLevel = 'high'\n      }\n    }\n  }\n\n  // Check for abuse keywords\n  if (riskLevel === 'low') {\n    for (const keyword of SAFETY_KEYWORDS.abuse) {\n      if (lowerMessage.includes(keyword)) {\n        detectedKeywords.push(keyword)\n        riskLevel = 'high'\n      }\n    }\n  }\n\n  // Check for distress keywords\n  if (riskLevel === 'low') {\n    for (const keyword of SAFETY_KEYWORDS.distress) {\n      if (lowerMessage.includes(keyword)) {\n        detectedKeywords.push(keyword)\n        riskLevel = 'medium'\n      }\n    }\n  }\n\n  return { riskLevel, keywords: detectedKeywords }\n}\n\n// System prompt for the AI counselor\nconst SYSTEM_PROMPT = `You are a compassionate AI mental health counselor specializing in supporting women and children. Your role is to:\n\n1. Provide empathetic, non-judgmental emotional support\n2. Offer practical coping strategies and techniques\n3. Validate feelings and experiences\n4. Encourage professional help when appropriate\n5. Maintain a safe, supportive environment\n\nGuidelines:\n- Always prioritize safety and well-being\n- Use age-appropriate language\n- Be culturally sensitive\n- Never provide medical diagnoses\n- Encourage professional help for serious concerns\n- If someone mentions self-harm or abuse, provide crisis resources immediately\n\nRemember: You are here to listen, support, and guide - not to replace professional therapy or medical care.`\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { message, sessionId, userId } = await request.json()\n\n    if (!message) {\n      return NextResponse.json(\n        { error: 'Message is required' },\n        { status: 400 }\n      )\n    }\n\n    // Sanitize input\n    const sanitizedMessage = sanitizeInput(message)\n\n    // Perform safety monitoring\n    const safetyCheck = detectSafetyRisk(sanitizedMessage)\n\n    // If critical risk detected, provide immediate crisis resources\n    if (safetyCheck.riskLevel === 'critical') {\n      const crisisResponse = {\n        role: 'assistant',\n        content: `I'm very concerned about what you've shared. Your safety is the most important thing right now. Please reach out for immediate help:\n\n🚨 **Emergency Services: Call 911**\n📞 **Crisis Hotline: Call 988** (Suicide & Crisis Lifeline)\n💬 **Crisis Text Line: Text HOME to 741741**\n\nYou don't have to go through this alone. There are people who want to help you right now. Please reach out to one of these resources immediately.\n\nWould you like me to help you find local emergency resources or someone you can talk to?`,\n        timestamp: new Date().toISOString(),\n        safetyAlert: {\n          riskLevel: safetyCheck.riskLevel,\n          keywords: safetyCheck.keywords\n        }\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: crisisResponse\n      })\n    }\n\n    // Generate AI response using OpenAI\n    let aiResponse = ''\n\n    if (openai) {\n      try {\n        const completion = await openai.chat.completions.create({\n          model: 'gpt-4',\n          messages: [\n            { role: 'system', content: SYSTEM_PROMPT },\n            { role: 'user', content: sanitizedMessage }\n          ],\n          max_tokens: 500,\n          temperature: 0.7,\n        })\n\n        aiResponse = completion.choices[0]?.message?.content || 'I understand you\\'re reaching out. Can you tell me more about how you\\'re feeling?'\n      } catch (openaiError) {\n        console.error('OpenAI API error:', openaiError)\n        // Fallback response if OpenAI fails\n        aiResponse = 'I hear you and I\\'m here to listen. Can you tell me more about what\\'s on your mind today?'\n      }\n    } else {\n      // Fallback response when no API key is configured\n      aiResponse = 'Thank you for sharing with me. I\\'m here to support you. Can you tell me more about how you\\'re feeling right now?'\n    }\n\n    // Add safety resources if medium or high risk\n    if (safetyCheck.riskLevel === 'high') {\n      aiResponse += '\\n\\n**If you need immediate support:**\\n📞 Crisis Hotline: 988\\n💬 Text HOME to 741741'\n    } else if (safetyCheck.riskLevel === 'medium') {\n      aiResponse += '\\n\\n**Remember:** If you ever need immediate help, call 988 or text HOME to 741741'\n    }\n\n    const response = {\n      role: 'assistant',\n      content: aiResponse,\n      timestamp: new Date().toISOString(),\n      safetyAlert: safetyCheck.riskLevel !== 'low' ? {\n        riskLevel: safetyCheck.riskLevel,\n        keywords: safetyCheck.keywords\n      } : undefined\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: response\n    })\n\n  } catch (error) {\n    console.error('Chat API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,kEAAkE;AAClE,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;IACrD,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC,KAAK;AAEL,6BAA6B;AAC7B,SAAS,iBAAiB,OAAe;IACvC,MAAM,eAAe,QAAQ,WAAW;IACxC,MAAM,mBAA6B,EAAE;IACrC,IAAI,YAAY;IAEhB,+BAA+B;IAC/B,KAAK,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,SAAS,CAAE;QAC/C,IAAI,aAAa,QAAQ,CAAC,UAAU;YAClC,iBAAiB,IAAI,CAAC;YACtB,YAAY;QACd;IACF;IAEA,+BAA+B;IAC/B,IAAI,cAAc,YAAY;QAC5B,KAAK,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAE;YAC9C,IAAI,aAAa,QAAQ,CAAC,UAAU;gBAClC,iBAAiB,IAAI,CAAC;gBACtB,YAAY;YACd;QACF;IACF;IAEA,2BAA2B;IAC3B,IAAI,cAAc,OAAO;QACvB,KAAK,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,KAAK,CAAE;YAC3C,IAAI,aAAa,QAAQ,CAAC,UAAU;gBAClC,iBAAiB,IAAI,CAAC;gBACtB,YAAY;YACd;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,cAAc,OAAO;QACvB,KAAK,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,QAAQ,CAAE;YAC9C,IAAI,aAAa,QAAQ,CAAC,UAAU;gBAClC,iBAAiB,IAAI,CAAC;gBACtB,YAAY;YACd;QACF;IACF;IAEA,OAAO;QAAE;QAAW,UAAU;IAAiB;AACjD;AAEA,qCAAqC;AACrC,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;2GAgBoF,CAAC;AAErG,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,mBAAmB,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE;QAEvC,4BAA4B;QAC5B,MAAM,cAAc,iBAAiB;QAErC,gEAAgE;QAChE,IAAI,YAAY,SAAS,KAAK,YAAY;YACxC,MAAM,iBAAiB;gBACrB,MAAM;gBACN,SAAS,CAAC;;;;;;;;wFAQsE,CAAC;gBACjF,WAAW,IAAI,OAAO,WAAW;gBACjC,aAAa;oBACX,WAAW,YAAY,SAAS;oBAChC,UAAU,YAAY,QAAQ;gBAChC;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;QAEA,oCAAoC;QACpC,IAAI,aAAa;QAEjB,IAAI,QAAQ;YACV,IAAI;gBACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACtD,OAAO;oBACP,UAAU;wBACR;4BAAE,MAAM;4BAAU,SAAS;wBAAc;wBACzC;4BAAE,MAAM;4BAAQ,SAAS;wBAAiB;qBAC3C;oBACD,YAAY;oBACZ,aAAa;gBACf;gBAEA,aAAa,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAC1D,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,oCAAoC;gBACpC,aAAa;YACf;QACF,OAAO;YACL,kDAAkD;YAClD,aAAa;QACf;QAEA,8CAA8C;QAC9C,IAAI,YAAY,SAAS,KAAK,QAAQ;YACpC,cAAc;QAChB,OAAO,IAAI,YAAY,SAAS,KAAK,UAAU;YAC7C,cAAc;QAChB;QAEA,MAAM,WAAW;YACf,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,aAAa,YAAY,SAAS,KAAK,QAAQ;gBAC7C,WAAW,YAAY,SAAS;gBAChC,UAAU,YAAY,QAAQ;YAChC,IAAI;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}