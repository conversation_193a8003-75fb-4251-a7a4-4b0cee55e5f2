{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/[<>]/g, '')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,OAAO,CAAC,uDAAuD,IACzE,OAAO,CAAC,SAAS,IACjB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/chatbot/VoiceChat.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { MicrophoneIcon, SpeakerWaveIcon, StopIcon, LanguageIcon } from '@heroicons/react/24/outline'\n\ninterface VoiceChatProps {\n  onTranscript: (text: string) => void\n  onSpeakResponse: (text: string, language: 'en' | 'ta') => void\n  isListening: boolean\n  setIsListening: (listening: boolean) => void\n  language: 'en' | 'ta'\n  setLanguage: (lang: 'en' | 'ta') => void\n}\n\nexport default function VoiceChat({\n  onTranscript,\n  onSpeakResponse,\n  isListening,\n  setIsListening,\n  language,\n  setLanguage\n}: VoiceChatProps) {\n  const [isSupported, setIsSupported] = useState(false)\n  const [isSpeaking, setIsSpeaking] = useState(false)\n  const [transcript, setTranscript] = useState('')\n  \n  const recognitionRef = useRef<any>(null)\n  const synthRef = useRef<SpeechSynthesis | null>(null)\n\n  useEffect(() => {\n    // Check for speech recognition support\n    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition\n    const speechSynthesis = window.speechSynthesis\n\n    if (SpeechRecognition && speechSynthesis) {\n      setIsSupported(true)\n      synthRef.current = speechSynthesis\n\n      // Initialize speech recognition\n      const recognition = new SpeechRecognition()\n      recognition.continuous = true\n      recognition.interimResults = true\n      recognition.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n\n      recognition.onstart = () => {\n        console.log('Voice recognition started')\n      }\n\n      recognition.onresult = (event: any) => {\n        let finalTranscript = ''\n        let interimTranscript = ''\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript\n          } else {\n            interimTranscript += transcript\n          }\n        }\n\n        setTranscript(interimTranscript)\n\n        if (finalTranscript) {\n          onTranscript(finalTranscript)\n          setTranscript('')\n          setIsListening(false)\n        }\n      }\n\n      recognition.onerror = (event: any) => {\n        console.error('Speech recognition error:', event.error)\n        setIsListening(false)\n      }\n\n      recognition.onend = () => {\n        setIsListening(false)\n      }\n\n      recognitionRef.current = recognition\n    }\n\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop()\n      }\n    }\n  }, [language, onTranscript, setIsListening])\n\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      recognitionRef.current.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n      recognitionRef.current.start()\n      setIsListening(true)\n    }\n  }\n\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      recognitionRef.current.stop()\n      setIsListening(false)\n    }\n  }\n\n  const speakText = (text: string, lang: 'en' | 'ta' = language) => {\n    if (synthRef.current && text) {\n      // Stop any current speech\n      synthRef.current.cancel()\n\n      const utterance = new SpeechSynthesisUtterance(text)\n      \n      // Set language and voice\n      utterance.lang = lang === 'ta' ? 'ta-IN' : 'en-US'\n      utterance.rate = 0.9\n      utterance.pitch = 1\n      utterance.volume = 1\n\n      // Try to find a suitable voice\n      const voices = synthRef.current.getVoices()\n      const preferredVoice = voices.find(voice => \n        lang === 'ta' \n          ? voice.lang.includes('ta') || voice.lang.includes('Tamil')\n          : voice.lang.includes('en') && voice.lang.includes('US')\n      )\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice\n      }\n\n      utterance.onstart = () => setIsSpeaking(true)\n      utterance.onend = () => setIsSpeaking(false)\n      utterance.onerror = () => setIsSpeaking(false)\n\n      synthRef.current.speak(utterance)\n    }\n  }\n\n  const stopSpeaking = () => {\n    if (synthRef.current) {\n      synthRef.current.cancel()\n      setIsSpeaking(false)\n    }\n  }\n\n  // Expose speakText function to parent\n  useEffect(() => {\n    onSpeakResponse(speakText as any, language)\n  }, [language, onSpeakResponse])\n\n  if (!isSupported) {\n    return (\n      <div className=\"text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n        <p className=\"text-yellow-800\">\n          Voice features are not supported in your browser. Please use Chrome or Edge for voice interaction.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg\">\n      {/* Language Toggle */}\n      <div className=\"flex items-center space-x-2\">\n        <LanguageIcon className=\"h-5 w-5 text-gray-500\" />\n        <select\n          value={language}\n          onChange={(e) => setLanguage(e.target.value as 'en' | 'ta')}\n          className=\"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500\"\n        >\n          <option value=\"en\">English</option>\n          <option value=\"ta\">தமிழ் (Tamil)</option>\n        </select>\n      </div>\n\n      {/* Voice Input */}\n      <div className=\"flex items-center space-x-2\">\n        {!isListening ? (\n          <button\n            onClick={startListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors\"\n          >\n            <MicrophoneIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'பேசுங்கள்' : 'Speak'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'நிறுத்து' : 'Stop'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Speech Output Control */}\n      <div className=\"flex items-center space-x-2\">\n        {!isSpeaking ? (\n          <button\n            onClick={() => speakText(language === 'ta' \n              ? 'வணக்கம்! நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்.' \n              : 'Hello! I am here to help you.'\n            )}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <SpeakerWaveIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'சோதனை' : 'Test Voice'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopSpeaking}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'அமைதி' : 'Stop Voice'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Live Transcript */}\n      {transcript && (\n        <div className=\"flex-1 text-sm text-gray-600 italic\">\n          {language === 'ta' ? 'கேட்கிறது: ' : 'Listening: '}{transcript}\n        </div>\n      )}\n\n      {/* Status Indicators */}\n      <div className=\"flex space-x-2\">\n        {isListening && (\n          <div className=\"flex items-center space-x-1 text-red-600\">\n            <div className=\"w-2 h-2 bg-red-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'கேட்கிறது' : 'Listening'}\n            </span>\n          </div>\n        )}\n        {isSpeaking && (\n          <div className=\"flex items-center space-x-1 text-blue-600\">\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'பேசுகிறது' : 'Speaking'}\n            </span>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAce,SAAS,UAAU,EAChC,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACI;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuC;QACvC,MAAM,oBAAoB,AAAC,OAAe,iBAAiB,IAAI,AAAC,OAAe,uBAAuB;QACtG,MAAM,kBAAkB,OAAO,eAAe;QAE9C,IAAI,qBAAqB,iBAAiB;YACxC,eAAe;YACf,SAAS,OAAO,GAAG;YAEnB,gCAAgC;YAChC,MAAM,cAAc,IAAI;YACxB,YAAY,UAAU,GAAG;YACzB,YAAY,cAAc,GAAG;YAC7B,YAAY,IAAI,GAAG,aAAa,OAAO,UAAU;YAEjD,YAAY,OAAO,GAAG;gBACpB,QAAQ,GAAG,CAAC;YACd;YAEA,YAAY,QAAQ,GAAG,CAAC;gBACtB,IAAI,kBAAkB;gBACtB,IAAI,oBAAoB;gBAExB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;oBAC7D,MAAM,aAAa,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;oBACjD,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;wBAC5B,mBAAmB;oBACrB,OAAO;wBACL,qBAAqB;oBACvB;gBACF;gBAEA,cAAc;gBAEd,IAAI,iBAAiB;oBACnB,aAAa;oBACb,cAAc;oBACd,eAAe;gBACjB;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;gBACtD,eAAe;YACjB;YAEA,YAAY,KAAK,GAAG;gBAClB,eAAe;YACjB;YAEA,eAAe,OAAO,GAAG;QAC3B;QAEA,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,IAAI;YAC7B;QACF;IACF,GAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,iBAAiB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,aAAa;YAC1C,eAAe,OAAO,CAAC,IAAI,GAAG,aAAa,OAAO,UAAU;YAC5D,eAAe,OAAO,CAAC,KAAK;YAC5B,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,OAAO,IAAI,aAAa;YACzC,eAAe,OAAO,CAAC,IAAI;YAC3B,eAAe;QACjB;IACF;IAEA,MAAM,YAAY,CAAC,MAAc,OAAoB,QAAQ;QAC3D,IAAI,SAAS,OAAO,IAAI,MAAM;YAC5B,0BAA0B;YAC1B,SAAS,OAAO,CAAC,MAAM;YAEvB,MAAM,YAAY,IAAI,yBAAyB;YAE/C,yBAAyB;YACzB,UAAU,IAAI,GAAG,SAAS,OAAO,UAAU;YAC3C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,+BAA+B;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,SAAS,OACL,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,WACjD,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC;YAGvD,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,OAAO,GAAG,IAAM,cAAc;YACxC,UAAU,KAAK,GAAG,IAAM,cAAc;YACtC,UAAU,OAAO,GAAG,IAAM,cAAc;YAExC,SAAS,OAAO,CAAC,KAAK,CAAC;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,MAAM;YACvB,cAAc;QAChB;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,WAAkB;IACpC,GAAG;QAAC;QAAU;KAAgB;IAE9B,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAkB;;;;;;;;;;;IAKrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,2NAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;sCAC1B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,cAAc;;;;;;;;;;;yCAIvC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,8OAAC;oBACC,SAAS,IAAM,UAAU,aAAa,OAClC,oDACA;oBAEJ,WAAU;;sCAEV,8OAAC,6NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;yCAInC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;YAOtC,4BACC,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,OAAO,gBAAgB;oBAAe;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;oBAIxC,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/avatar/HumanAvatar.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\nimport { <PERSON>vas, useFrame, useThree } from '@react-three/fiber'\nimport { OrbitControls, Text, Sphere, Box, Cylinder, Cone } from '@react-three/drei'\nimport * as THREE from 'three'\n\ninterface AvatarProps {\n  isListening: boolean\n  isSpeaking: boolean\n  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n  message?: string\n  lipSyncData?: {\n    mouthOpenness: number\n    mouthWidth: number\n    phoneme: string\n  }\n  facialData?: {\n    eyeBlinkLeft: number\n    eyeBlinkRight: number\n    eyebrowLeft: number\n    eyebrowRight: number\n    cheekPuff: number\n    jawOpen: number\n  }\n}\n\n// Advanced Realistic Human Head Component\nfunction RealisticHumanHead({\n  isListening,\n  isSpeaking,\n  emotion,\n  lipSyncData,\n  facialData\n}: Omit<AvatarProps, 'message'>) {\n  // Head and facial feature refs\n  const headRef = useRef<THREE.Group>(null)\n  const faceRef = useRef<THREE.Mesh>(null)\n  const leftEyeRef = useRef<THREE.Group>(null)\n  const rightEyeRef = useRef<THREE.Group>(null)\n  const leftEyeballRef = useRef<THREE.Mesh>(null)\n  const rightEyeballRef = useRef<THREE.Mesh>(null)\n  const leftEyelidRef = useRef<THREE.Mesh>(null)\n  const rightEyelidRef = useRef<THREE.Mesh>(null)\n  const mouthRef = useRef<THREE.Group>(null)\n  const upperLipRef = useRef<THREE.Mesh>(null)\n  const lowerLipRef = useRef<THREE.Mesh>(null)\n  const teethRef = useRef<THREE.Mesh>(null)\n  const tongueRef = useRef<THREE.Mesh>(null)\n  const leftEyebrowRef = useRef<THREE.Mesh>(null)\n  const rightEyebrowRef = useRef<THREE.Mesh>(null)\n  const noseRef = useRef<THREE.Group>(null)\n  const leftCheekRef = useRef<THREE.Mesh>(null)\n  const rightCheekRef = useRef<THREE.Mesh>(null)\n  const jawRef = useRef<THREE.Mesh>(null)\n\n  // Animation states\n  const [blinkTimer, setBlinkTimer] = useState(0)\n  const [mouthAnimation, setMouthAnimation] = useState(0)\n  const [eyeMovement, setEyeMovement] = useState({ x: 0, y: 0 })\n  const [headMovement, setHeadMovement] = useState({ x: 0, y: 0, z: 0 })\n  const [breathingPhase, setBreathingPhase] = useState(0)\n\n  // Advanced Animation Loop\n  useFrame((state, delta) => {\n    if (!headRef.current) return\n\n    const time = state.clock.elapsedTime\n\n    // Natural breathing animation\n    setBreathingPhase(prev => prev + delta * 0.3)\n    const breathingIntensity = 1 + Math.sin(breathingPhase) * 0.015\n    if (headRef.current) {\n      headRef.current.scale.setScalar(breathingIntensity)\n    }\n\n    // Realistic blinking system\n    setBlinkTimer(prev => prev + delta)\n    if (blinkTimer > (2 + Math.random() * 3)) { // Random blink interval 2-5 seconds\n      setBlinkTimer(0)\n\n      // Smooth eyelid animation\n      if (leftEyelidRef.current && rightEyelidRef.current) {\n        const blinkDuration = 0.15\n        const blinkProgress = Math.min((time % blinkDuration) / blinkDuration, 1)\n        const blinkValue = Math.sin(blinkProgress * Math.PI)\n\n        leftEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9)\n        rightEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9)\n      }\n    }\n\n    // Advanced lip-sync animation\n    if (isSpeaking && lipSyncData) {\n      // Upper and lower lip movement based on phonemes\n      if (upperLipRef.current && lowerLipRef.current) {\n        const mouthOpen = lipSyncData.mouthOpenness * 0.8\n        const mouthWidth = lipSyncData.mouthWidth * 0.6\n\n        upperLipRef.current.position.y = 0.1 + mouthOpen * 0.1\n        lowerLipRef.current.position.y = -0.1 - mouthOpen * 0.15\n        upperLipRef.current.scale.x = 1 + mouthWidth * 0.3\n        lowerLipRef.current.scale.x = 1 + mouthWidth * 0.3\n\n        // Jaw movement\n        if (jawRef.current) {\n          jawRef.current.position.y = -mouthOpen * 0.1\n          jawRef.current.rotation.x = mouthOpen * 0.2\n        }\n\n        // Tongue visibility for certain phonemes\n        if (tongueRef.current) {\n          const showTongue = ['L', 'TH', 'R'].includes(lipSyncData.phoneme)\n          tongueRef.current.visible = showTongue\n          if (showTongue) {\n            tongueRef.current.position.z = 0.05 + mouthOpen * 0.1\n          }\n        }\n      }\n    } else {\n      // Return to neutral position\n      if (upperLipRef.current && lowerLipRef.current && jawRef.current) {\n        upperLipRef.current.position.y = THREE.MathUtils.lerp(upperLipRef.current.position.y, 0.1, delta * 8)\n        lowerLipRef.current.position.y = THREE.MathUtils.lerp(lowerLipRef.current.position.y, -0.1, delta * 8)\n        jawRef.current.position.y = THREE.MathUtils.lerp(jawRef.current.position.y, 0, delta * 8)\n        jawRef.current.rotation.x = THREE.MathUtils.lerp(jawRef.current.rotation.x, 0, delta * 8)\n\n        if (tongueRef.current) {\n          tongueRef.current.visible = false\n        }\n      }\n    }\n\n    // Facial expression animation based on facialData\n    if (facialData) {\n      // Eyebrow movement\n      if (leftEyebrowRef.current && rightEyebrowRef.current) {\n        leftEyebrowRef.current.position.y = 0.4 + facialData.eyebrowLeft * 0.1\n        rightEyebrowRef.current.position.y = 0.4 + facialData.eyebrowRight * 0.1\n        leftEyebrowRef.current.rotation.z = facialData.eyebrowLeft * 0.2\n        rightEyebrowRef.current.rotation.z = -facialData.eyebrowRight * 0.2\n      }\n\n      // Cheek movement for smiling/expressions\n      if (leftCheekRef.current && rightCheekRef.current) {\n        const cheekRaise = facialData.cheekPuff\n        leftCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2)\n        rightCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2)\n        leftCheekRef.current.position.y = -0.1 + cheekRaise * 0.05\n        rightCheekRef.current.position.y = -0.1 + cheekRaise * 0.05\n      }\n    }\n\n    // Natural eye movement and gaze\n    setEyeMovement(prev => ({\n      x: prev.x + (Math.random() - 0.5) * 0.001,\n      y: prev.y + (Math.random() - 0.5) * 0.001\n    }))\n\n    if (leftEyeballRef.current && rightEyeballRef.current) {\n      const gazeX = Math.sin(time * 0.3) * 0.1 + eyeMovement.x\n      const gazeY = Math.cos(time * 0.4) * 0.05 + eyeMovement.y\n\n      leftEyeballRef.current.position.x = gazeX\n      leftEyeballRef.current.position.y = gazeY\n      rightEyeballRef.current.position.x = gazeX\n      rightEyeballRef.current.position.y = gazeY\n    }\n\n    // Listening animation - subtle head movement\n    if (isListening) {\n      setHeadMovement(prev => ({\n        x: Math.sin(time * 1.2) * 0.05,\n        y: Math.cos(time * 0.8) * 0.03,\n        z: Math.sin(time * 1.5) * 0.08\n      }))\n    } else {\n      setHeadMovement(prev => ({\n        x: THREE.MathUtils.lerp(prev.x, Math.sin(time * 0.5) * 0.02, delta * 2),\n        y: THREE.MathUtils.lerp(prev.y, Math.cos(time * 0.3) * 0.01, delta * 2),\n        z: THREE.MathUtils.lerp(prev.z, 0, delta * 3)\n      }))\n    }\n\n    if (headRef.current) {\n      headRef.current.rotation.x = headMovement.x\n      headRef.current.rotation.y = headMovement.y\n      headRef.current.rotation.z = headMovement.z\n    }\n  })\n\n  // Emotion-based expressions\n  const getEmotionColors = () => {\n    switch (emotion) {\n      case 'happy':\n        return { skin: '#FFE4C4', cheek: '#FFB6C1' }\n      case 'sad':\n        return { skin: '#F5DEB3', cheek: '#D3D3D3' }\n      case 'concerned':\n        return { skin: '#FAEBD7', cheek: '#DDA0DD' }\n      case 'empathetic':\n        return { skin: '#FFF8DC', cheek: '#F0E68C' }\n      default:\n        return { skin: '#FDBCB4', cheek: '#FFB6C1' }\n    }\n  }\n\n  const colors = getEmotionColors()\n\n  return (\n    <group ref={headRef} position={[0, 0, 0]}>\n      {/* Main Head Structure */}\n      <Sphere ref={faceRef} args={[1.2, 64, 64]} position={[0, 0, 0]}>\n        <meshStandardMaterial\n          color={colors.skin}\n          roughness={0.8}\n          metalness={0.1}\n        />\n      </Sphere>\n\n      {/* Detailed Eye Structure */}\n      <group ref={leftEyeRef} position={[-0.35, 0.25, 0.85]}>\n        {/* Eye socket */}\n        <Sphere args={[0.18, 32, 32]} position={[0, 0, -0.05]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Sphere>\n        {/* Eyeball */}\n        <Sphere ref={leftEyeballRef} args={[0.15, 32, 32]} position={[0, 0, 0]}>\n          <meshStandardMaterial color=\"#FFFFFF\" />\n        </Sphere>\n        {/* Iris */}\n        <Sphere args={[0.08, 32, 32]} position={[0, 0, 0.12]}>\n          <meshStandardMaterial color=\"#4A90E2\" />\n        </Sphere>\n        {/* Pupil */}\n        <Sphere args={[0.04, 32, 32]} position={[0, 0, 0.13]}>\n          <meshStandardMaterial color=\"#000000\" />\n        </Sphere>\n        {/* Eyelid */}\n        <Sphere ref={leftEyelidRef} args={[0.16, 32, 16]} position={[0, 0.05, 0.1]} scale={[1, 1, 0.3]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Sphere>\n      </group>\n\n      <group ref={rightEyeRef} position={[0.35, 0.25, 0.85]}>\n        {/* Eye socket */}\n        <Sphere args={[0.18, 32, 32]} position={[0, 0, -0.05]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Sphere>\n        {/* Eyeball */}\n        <Sphere ref={rightEyeballRef} args={[0.15, 32, 32]} position={[0, 0, 0]}>\n          <meshStandardMaterial color=\"#FFFFFF\" />\n        </Sphere>\n        {/* Iris */}\n        <Sphere args={[0.08, 32, 32]} position={[0, 0, 0.12]}>\n          <meshStandardMaterial color=\"#4A90E2\" />\n        </Sphere>\n        {/* Pupil */}\n        <Sphere args={[0.04, 32, 32]} position={[0, 0, 0.13]}>\n          <meshStandardMaterial color=\"#000000\" />\n        </Sphere>\n        {/* Eyelid */}\n        <Sphere ref={rightEyelidRef} args={[0.16, 32, 16]} position={[0, 0.05, 0.1]} scale={[1, 1, 0.3]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Sphere>\n      </group>\n\n      {/* Detailed Eyebrows */}\n      <Box\n        ref={leftEyebrowRef}\n        args={[0.35, 0.08, 0.12]}\n        position={[-0.35, 0.45, 0.8]}\n        rotation={[0, 0, emotion === 'concerned' ? 0.4 : emotion === 'happy' ? -0.1 : 0]}\n      >\n        <meshStandardMaterial color=\"#654321\" roughness={0.9} />\n      </Box>\n      <Box\n        ref={rightEyebrowRef}\n        args={[0.35, 0.08, 0.12]}\n        position={[0.35, 0.45, 0.8]}\n        rotation={[0, 0, emotion === 'concerned' ? -0.4 : emotion === 'happy' ? 0.1 : 0]}\n      >\n        <meshStandardMaterial color=\"#654321\" roughness={0.9} />\n      </Box>\n\n      {/* Detailed Nose Structure */}\n      <group ref={noseRef} position={[0, 0.05, 0.95]}>\n        {/* Nose bridge */}\n        <Box args={[0.08, 0.3, 0.15]} position={[0, 0.1, 0]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Box>\n        {/* Nose tip */}\n        <Sphere args={[0.06, 16, 16]} position={[0, -0.05, 0.05]}>\n          <meshStandardMaterial color={colors.skin} />\n        </Sphere>\n        {/* Nostrils */}\n        <Sphere args={[0.02, 8, 8]} position={[-0.03, -0.08, 0.02]}>\n          <meshStandardMaterial color=\"#8B4513\" />\n        </Sphere>\n        <Sphere args={[0.02, 8, 8]} position={[0.03, -0.08, 0.02]}>\n          <meshStandardMaterial color=\"#8B4513\" />\n        </Sphere>\n      </group>\n\n      {/* Advanced Mouth Structure */}\n      <group ref={mouthRef} position={[0, -0.25, 0.85]}>\n        {/* Upper Lip */}\n        <Sphere\n          ref={upperLipRef}\n          args={[0.25, 32, 16]}\n          position={[0, 0.1, 0]}\n          scale={[1, 0.4, 0.8]}\n          rotation={[0, 0, emotion === 'happy' ? 0.2 : emotion === 'sad' ? -0.2 : 0]}\n        >\n          <meshStandardMaterial color=\"#CD5C5C\" />\n        </Sphere>\n\n        {/* Lower Lip */}\n        <Sphere\n          ref={lowerLipRef}\n          args={[0.28, 32, 16]}\n          position={[0, -0.1, 0]}\n          scale={[1, 0.5, 0.8]}\n          rotation={[0, 0, emotion === 'happy' ? 0.15 : emotion === 'sad' ? -0.15 : 0]}\n        >\n          <meshStandardMaterial color=\"#B22222\" />\n        </Sphere>\n\n        {/* Teeth */}\n        <Box ref={teethRef} args={[0.3, 0.08, 0.05]} position={[0, 0, 0.05]}>\n          <meshStandardMaterial color=\"#FFFACD\" />\n        </Box>\n\n        {/* Tongue */}\n        <Sphere\n          ref={tongueRef}\n          args={[0.15, 16, 16]}\n          position={[0, -0.05, 0]}\n          scale={[1, 0.6, 1.5]}\n          visible={false}\n        >\n          <meshStandardMaterial color=\"#FF69B4\" />\n        </Sphere>\n      </group>\n\n      {/* Jaw */}\n      <Sphere ref={jawRef} args={[0.8, 32, 32]} position={[0, -0.6, 0.3]} scale={[1.2, 0.8, 1]}>\n        <meshStandardMaterial color={colors.skin} />\n      </Sphere>\n\n      {/* Cheeks */}\n      <Sphere ref={leftCheekRef} args={[0.15, 32, 32]} position={[-0.7, -0.1, 0.6]}>\n        <meshStandardMaterial\n          color={emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin}\n          transparent\n          opacity={emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1}\n        />\n      </Sphere>\n      <Sphere ref={rightCheekRef} args={[0.15, 32, 32]} position={[0.7, -0.1, 0.6]}>\n        <meshStandardMaterial\n          color={emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin}\n          transparent\n          opacity={emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1}\n        />\n      </Sphere>\n\n      {/* Realistic Hair */}\n      <group position={[0, 0.4, -0.1]}>\n        <Sphere args={[1.35, 32, 32]} position={[0, 0, 0]} scale={[1, 1.2, 0.8]}>\n          <meshStandardMaterial color=\"#654321\" roughness={0.8} />\n        </Sphere>\n        {/* Hair strands for detail */}\n        {Array.from({ length: 20 }, (_, i) => (\n          <Cylinder\n            key={i}\n            args={[0.01, 0.01, 0.3]}\n            position={[\n              (Math.random() - 0.5) * 2,\n              Math.random() * 0.5,\n              (Math.random() - 0.5) * 1.5\n            ]}\n            rotation={[\n              Math.random() * 0.5,\n              Math.random() * Math.PI * 2,\n              Math.random() * 0.5\n            ]}\n          >\n            <meshStandardMaterial color=\"#543A2F\" />\n          </Cylinder>\n        ))}\n      </group>\n\n      {/* Neck with realistic proportions */}\n      <Cylinder args={[0.25, 0.3, 0.8]} position={[0, -1.2, 0]}>\n        <meshStandardMaterial color={colors.skin} />\n      </Cylinder>\n\n      {/* Shoulders and clothing */}\n      <group position={[0, -1.8, 0]}>\n        <Box args={[2.2, 0.4, 0.8]}>\n          <meshStandardMaterial color=\"#4A90E2\" />\n        </Box>\n        {/* Collar */}\n        <Box args={[0.6, 0.1, 0.1]} position={[0, 0.2, 0.4]}>\n          <meshStandardMaterial color=\"#2C5282\" />\n        </Box>\n      </group>\n    </group>\n  )\n}\n\n// Advanced Lighting Environment for Realistic Rendering\nfunction Environment() {\n  return (\n    <>\n      {/* Advanced Lighting Setup */}\n      <ambientLight intensity={0.4} color=\"#ffffff\" />\n\n      {/* Key Light - Main illumination */}\n      <directionalLight\n        position={[5, 8, 5]}\n        intensity={1.2}\n        color=\"#ffffff\"\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n        shadow-camera-far={50}\n        shadow-camera-left={-10}\n        shadow-camera-right={10}\n        shadow-camera-top={10}\n        shadow-camera-bottom={-10}\n      />\n\n      {/* Fill Light - Soften shadows */}\n      <directionalLight\n        position={[-3, 4, 2]}\n        intensity={0.6}\n        color=\"#E6F3FF\"\n      />\n\n      {/* Rim Light - Edge definition */}\n      <pointLight\n        position={[0, 2, -3]}\n        intensity={0.8}\n        color=\"#FFE4B5\"\n      />\n\n      {/* Face Light - Enhance facial features */}\n      <spotLight\n        position={[0, 1, 4]}\n        angle={0.3}\n        penumbra={0.5}\n        intensity={0.5}\n        color=\"#FFFACD\"\n        target-position={[0, 0, 0]}\n      />\n\n      {/* Background with gradient effect */}\n      <Sphere args={[50]} position={[0, 0, -25]}>\n        <meshBasicMaterial\n          color=\"#E8F4FD\"\n          side={THREE.BackSide}\n        />\n      </Sphere>\n\n      {/* Subtle floor reflection */}\n      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -3, 0]} receiveShadow>\n        <planeGeometry args={[20, 20]} />\n        <meshStandardMaterial\n          color=\"#F0F8FF\"\n          transparent\n          opacity={0.3}\n          roughness={0.1}\n          metalness={0.1}\n        />\n      </mesh>\n    </>\n  )\n}\n\n// Main Avatar Component with Advanced Features\nexport default function HumanAvatar({\n  isListening,\n  isSpeaking,\n  emotion,\n  message,\n  lipSyncData,\n  facialData\n}: AvatarProps) {\n  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 0, 4.5])\n  const [currentLipSync, setCurrentLipSync] = useState({\n    mouthOpenness: 0,\n    mouthWidth: 0,\n    phoneme: 'silence'\n  })\n  const [currentFacialData, setCurrentFacialData] = useState({\n    eyeBlinkLeft: 1,\n    eyeBlinkRight: 1,\n    eyebrowLeft: 0,\n    eyebrowRight: 0,\n    cheekPuff: 0,\n    jawOpen: 0\n  })\n\n  // Update lip sync data when speaking\n  useEffect(() => {\n    if (isSpeaking && message) {\n      // Simple phoneme extraction from message\n      const words = message.split(' ')\n      let phonemeIndex = 0\n\n      const animateLipSync = () => {\n        if (phonemeIndex < message.length && isSpeaking) {\n          const char = message[phonemeIndex].toLowerCase()\n          let phoneme = 'silence'\n          let mouthOpenness = 0.2\n          let mouthWidth = 0.5\n\n          // Enhanced phoneme mapping\n          if ('aeiou'.includes(char)) {\n            phoneme = char.toUpperCase()\n            mouthOpenness = char === 'a' ? 0.8 : char === 'o' ? 0.9 : char === 'i' ? 0.3 : 0.6\n            mouthWidth = char === 'i' ? 0.9 : char === 'o' ? 0.4 : 0.6\n          } else if ('mbp'.includes(char)) {\n            phoneme = char.toUpperCase()\n            mouthOpenness = 0.1\n            mouthWidth = 0.5\n          } else if ('fv'.includes(char)) {\n            phoneme = char.toUpperCase()\n            mouthOpenness = 0.3\n            mouthWidth = 0.7\n          } else if ('sz'.includes(char)) {\n            phoneme = 'S'\n            mouthOpenness = 0.2\n            mouthWidth = 0.6\n          } else if ('rl'.includes(char)) {\n            phoneme = char.toUpperCase()\n            mouthOpenness = 0.4\n            mouthWidth = 0.6\n          }\n\n          setCurrentLipSync({ mouthOpenness, mouthWidth, phoneme })\n          phonemeIndex++\n\n          setTimeout(animateLipSync, 80 + Math.random() * 40) // Variable speed\n        } else {\n          setCurrentLipSync({ mouthOpenness: 0.1, mouthWidth: 0.5, phoneme: 'silence' })\n        }\n      }\n\n      animateLipSync()\n    } else {\n      setCurrentLipSync({ mouthOpenness: 0.1, mouthWidth: 0.5, phoneme: 'silence' })\n    }\n  }, [isSpeaking, message])\n\n  // Update facial expressions based on emotion\n  useEffect(() => {\n    const emotionMappings = {\n      happy: { eyebrowLeft: 0.2, eyebrowRight: 0.2, cheekPuff: 0.4 },\n      sad: { eyebrowLeft: -0.3, eyebrowRight: -0.3, cheekPuff: 0 },\n      concerned: { eyebrowLeft: -0.2, eyebrowRight: 0.1, cheekPuff: 0 },\n      empathetic: { eyebrowLeft: 0.1, eyebrowRight: 0.1, cheekPuff: 0.2 },\n      neutral: { eyebrowLeft: 0, eyebrowRight: 0, cheekPuff: 0 }\n    }\n\n    const mapping = emotionMappings[emotion]\n    setCurrentFacialData(prev => ({\n      ...prev,\n      ...mapping\n    }))\n  }, [emotion])\n\n  // Dynamic camera positioning\n  useEffect(() => {\n    if (isListening) {\n      setCameraPosition([0.2, 0.1, 3.8]) // Slight angle when listening\n    } else if (isSpeaking) {\n      setCameraPosition([0, 0, 4.2]) // Centered when speaking\n    } else {\n      setCameraPosition([0, 0, 4.5]) // Default position\n    }\n  }, [isListening, isSpeaking])\n\n  return (\n    <div className=\"w-full h-96 relative\">\n      <Canvas\n        camera={{\n          position: cameraPosition,\n          fov: 45,\n          near: 0.1,\n          far: 1000\n        }}\n        shadows\n      >\n        <Environment />\n        <RealisticHumanHead\n          isListening={isListening}\n          isSpeaking={isSpeaking}\n          emotion={emotion}\n          lipSyncData={lipSyncData || currentLipSync}\n          facialData={facialData || currentFacialData}\n        />\n        <OrbitControls\n          enableZoom={true}\n          enablePan={false}\n          maxDistance={8}\n          minDistance={3}\n          maxPolarAngle={Math.PI / 1.8}\n          minPolarAngle={Math.PI / 4}\n          enableDamping={true}\n          dampingFactor={0.05}\n        />\n      </Canvas>\n      \n      {/* Status Indicators */}\n      <div className=\"absolute top-4 left-4 space-y-2\">\n        {isListening && (\n          <div className=\"flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n            <span>Listening...</span>\n          </div>\n        )}\n        {isSpeaking && (\n          <div className=\"flex items-center space-x-2 bg-blue-500 text-white px-3 py-1 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n            <span>Speaking...</span>\n          </div>\n        )}\n      </div>\n\n      {/* Message Display */}\n      {message && (\n        <div className=\"absolute bottom-4 left-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg\">\n          <p className=\"text-sm text-gray-800\">{message}</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AA2BA,0CAA0C;AAC1C,SAAS,mBAAmB,EAC1B,WAAW,EACX,UAAU,EACV,OAAO,EACP,WAAW,EACX,UAAU,EACmB;IAC7B,+BAA+B;IAC/B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACpC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACxC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC3C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC1C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACrC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACxC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACzC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAElC,mBAAmB;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;QAAG,GAAG;IAAE;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,0BAA0B;IAC1B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,OAAO,MAAM,KAAK,CAAC,WAAW;QAEpC,8BAA8B;QAC9B,kBAAkB,CAAA,OAAQ,OAAO,QAAQ;QACzC,MAAM,qBAAqB,IAAI,KAAK,GAAG,CAAC,kBAAkB;QAC1D,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAClC;QAEA,4BAA4B;QAC5B,cAAc,CAAA,OAAQ,OAAO;QAC7B,IAAI,aAAc,IAAI,KAAK,MAAM,KAAK,GAAI;YACxC,cAAc;YAEd,0BAA0B;YAC1B,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,EAAE;gBACnD,MAAM,gBAAgB;gBACtB,MAAM,gBAAgB,KAAK,GAAG,CAAC,AAAC,OAAO,gBAAiB,eAAe;gBACvE,MAAM,aAAa,KAAK,GAAG,CAAC,gBAAgB,KAAK,EAAE;gBAEnD,cAAc,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,aAAa;gBAC/D,eAAe,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,aAAa;YAClE;QACF;QAEA,8BAA8B;QAC9B,IAAI,cAAc,aAAa;YAC7B,iDAAiD;YACjD,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,EAAE;gBAC9C,MAAM,YAAY,YAAY,aAAa,GAAG;gBAC9C,MAAM,aAAa,YAAY,UAAU,GAAG;gBAE5C,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,YAAY;gBACnD,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,YAAY;gBACpD,YAAY,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,aAAa;gBAC/C,YAAY,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,aAAa;gBAE/C,eAAe;gBACf,IAAI,OAAO,OAAO,EAAE;oBAClB,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,YAAY;oBACzC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,YAAY;gBAC1C;gBAEA,yCAAyC;gBACzC,IAAI,UAAU,OAAO,EAAE;oBACrB,MAAM,aAAa;wBAAC;wBAAK;wBAAM;qBAAI,CAAC,QAAQ,CAAC,YAAY,OAAO;oBAChE,UAAU,OAAO,CAAC,OAAO,GAAG;oBAC5B,IAAI,YAAY;wBACd,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,OAAO,YAAY;oBACpD;gBACF;YACF;QACF,OAAO;YACL,6BAA6B;YAC7B,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,IAAI,OAAO,OAAO,EAAE;gBAChE,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ;gBACnG,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,QAAQ;gBACpG,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,QAAQ;gBACvF,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,QAAQ;gBAEvF,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,OAAO,GAAG;gBAC9B;YACF;QACF;QAEA,kDAAkD;QAClD,IAAI,YAAY;YACd,mBAAmB;YACnB,IAAI,eAAe,OAAO,IAAI,gBAAgB,OAAO,EAAE;gBACrD,eAAe,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,WAAW,WAAW,GAAG;gBACnE,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,WAAW,YAAY,GAAG;gBACrE,eAAe,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,WAAW,WAAW,GAAG;gBAC7D,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,WAAW,YAAY,GAAG;YAClE;YAEA,yCAAyC;YACzC,IAAI,aAAa,OAAO,IAAI,cAAc,OAAO,EAAE;gBACjD,MAAM,aAAa,WAAW,SAAS;gBACvC,aAAa,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,aAAa;gBACtD,cAAc,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,aAAa;gBACvD,aAAa,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,aAAa;gBACtD,cAAc,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,aAAa;YACzD;QACF;QAEA,gCAAgC;QAChC,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtC,CAAC;QAED,IAAI,eAAe,OAAO,IAAI,gBAAgB,OAAO,EAAE;YACrD,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,OAAO,MAAM,YAAY,CAAC;YACxD,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,OAAO,OAAO,YAAY,CAAC;YAEzD,eAAe,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;YACpC,eAAe,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;YACpC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;YACrC,gBAAgB,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;QACvC;QAEA,6CAA6C;QAC7C,IAAI,aAAa;YACf,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO;oBAC1B,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO;oBAC1B,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO;gBAC5B,CAAC;QACH,OAAO;YACL,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,OAAO,MAAM,QAAQ;oBACrE,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC,OAAO,OAAO,MAAM,QAAQ;oBACrE,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ;gBAC7C,CAAC;QACH;QAEA,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC;YAC3C,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC;YAC3C,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,aAAa,CAAC;QAC7C;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C;gBACE,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;QAC/C;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAM,KAAK;QAAS,UAAU;YAAC;YAAG;YAAG;SAAE;;0BAEtC,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAS,MAAM;oBAAC;oBAAK;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC5D,cAAA,8OAAC;oBACC,OAAO,OAAO,IAAI;oBAClB,WAAW;oBACX,WAAW;;;;;;;;;;;0BAKf,8OAAC;gBAAM,KAAK;gBAAY,UAAU;oBAAC,CAAC;oBAAM;oBAAM;iBAAK;;kCAEnD,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG,CAAC;yBAAK;kCACnD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;kCAG1C,8OAAC,0JAAA,CAAA,SAAM;wBAAC,KAAK;wBAAgB,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;kCACpE,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;kCAClD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;kCAClD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,KAAK;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAM;yBAAI;wBAAE,OAAO;4BAAC;4BAAG;4BAAG;yBAAI;kCAC5F,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;0BAI5C,8OAAC;gBAAM,KAAK;gBAAa,UAAU;oBAAC;oBAAM;oBAAM;iBAAK;;kCAEnD,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG,CAAC;yBAAK;kCACnD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;kCAG1C,8OAAC,0JAAA,CAAA,SAAM;wBAAC,KAAK;wBAAiB,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;kCACrE,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;kCAClD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;kCAClD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,KAAK;wBAAgB,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAM;yBAAI;wBAAE,OAAO;4BAAC;4BAAG;4BAAG;yBAAI;kCAC7F,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;0BAK5C,8OAAC,0JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAM;oBAAM;iBAAK;gBACxB,UAAU;oBAAC,CAAC;oBAAM;oBAAM;iBAAI;gBAC5B,UAAU;oBAAC;oBAAG;oBAAG,YAAY,cAAc,MAAM,YAAY,UAAU,CAAC,MAAM;iBAAE;0BAEhF,cAAA,8OAAC;oBAAqB,OAAM;oBAAU,WAAW;;;;;;;;;;;0BAEnD,8OAAC,0JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAM;oBAAM;iBAAK;gBACxB,UAAU;oBAAC;oBAAM;oBAAM;iBAAI;gBAC3B,UAAU;oBAAC;oBAAG;oBAAG,YAAY,cAAc,CAAC,MAAM,YAAY,UAAU,MAAM;iBAAE;0BAEhF,cAAA,8OAAC;oBAAqB,OAAM;oBAAU,WAAW;;;;;;;;;;;0BAInD,8OAAC;gBAAM,KAAK;gBAAS,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;;kCAE5C,8OAAC,0JAAA,CAAA,MAAG;wBAAC,MAAM;4BAAC;4BAAM;4BAAK;yBAAK;wBAAE,UAAU;4BAAC;4BAAG;4BAAK;yBAAE;kCACjD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;kCAG1C,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG,CAAC;4BAAM;yBAAK;kCACtD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;kCAG1C,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAG;yBAAE;wBAAE,UAAU;4BAAC,CAAC;4BAAM,CAAC;4BAAM;yBAAK;kCACxD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAE9B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAG;yBAAE;wBAAE,UAAU;4BAAC;4BAAM,CAAC;4BAAM;yBAAK;kCACvD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;;;;;;;0BAKhC,8OAAC;gBAAM,KAAK;gBAAU,UAAU;oBAAC;oBAAG,CAAC;oBAAM;iBAAK;;kCAE9C,8OAAC,0JAAA,CAAA,SAAM;wBACL,KAAK;wBACL,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBACpB,UAAU;4BAAC;4BAAG;4BAAK;yBAAE;wBACrB,OAAO;4BAAC;4BAAG;4BAAK;yBAAI;wBACpB,UAAU;4BAAC;4BAAG;4BAAG,YAAY,UAAU,MAAM,YAAY,QAAQ,CAAC,MAAM;yBAAE;kCAE1E,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAI9B,8OAAC,0JAAA,CAAA,SAAM;wBACL,KAAK;wBACL,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBACpB,UAAU;4BAAC;4BAAG,CAAC;4BAAK;yBAAE;wBACtB,OAAO;4BAAC;4BAAG;4BAAK;yBAAI;wBACpB,UAAU;4BAAC;4BAAG;4BAAG,YAAY,UAAU,OAAO,YAAY,QAAQ,CAAC,OAAO;yBAAE;kCAE5E,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAI9B,8OAAC,0JAAA,CAAA,MAAG;wBAAC,KAAK;wBAAU,MAAM;4BAAC;4BAAK;4BAAM;yBAAK;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAK;kCACjE,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAI9B,8OAAC,0JAAA,CAAA,SAAM;wBACL,KAAK;wBACL,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBACpB,UAAU;4BAAC;4BAAG,CAAC;4BAAM;yBAAE;wBACvB,OAAO;4BAAC;4BAAG;4BAAK;yBAAI;wBACpB,SAAS;kCAET,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;;;;;;;0BAKhC,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAQ,MAAM;oBAAC;oBAAK;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBAAE,OAAO;oBAAC;oBAAK;oBAAK;iBAAE;0BACtF,cAAA,8OAAC;oBAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;0BAI1C,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAc,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAK,CAAC;oBAAK;iBAAI;0BAC1E,cAAA,8OAAC;oBACC,OAAO,YAAY,WAAW,YAAY,eAAe,OAAO,KAAK,GAAG,OAAO,IAAI;oBACnF,WAAW;oBACX,SAAS,YAAY,WAAW,YAAY,eAAe,MAAM;;;;;;;;;;;0BAGrE,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAe,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAK,CAAC;oBAAK;iBAAI;0BAC1E,cAAA,8OAAC;oBACC,OAAO,YAAY,WAAW,YAAY,eAAe,OAAO,KAAK,GAAG,OAAO,IAAI;oBACnF,WAAW;oBACX,SAAS,YAAY,WAAW,YAAY,eAAe,MAAM;;;;;;;;;;;0BAKrE,8OAAC;gBAAM,UAAU;oBAAC;oBAAG;oBAAK,CAAC;iBAAI;;kCAC7B,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,OAAO;4BAAC;4BAAG;4BAAK;yBAAI;kCACrE,cAAA,8OAAC;4BAAqB,OAAM;4BAAU,WAAW;;;;;;;;;;;oBAGlD,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,0JAAA,CAAA,WAAQ;4BAEP,MAAM;gCAAC;gCAAM;gCAAM;6BAAI;4BACvB,UAAU;gCACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxB,KAAK,MAAM,KAAK;gCAChB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;6BACzB;4BACD,UAAU;gCACR,KAAK,MAAM,KAAK;gCAChB,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;gCAC1B,KAAK,MAAM,KAAK;6BACjB;sCAED,cAAA,8OAAC;gCAAqB,OAAM;;;;;;2BAbvB;;;;;;;;;;;0BAmBX,8OAAC,0JAAA,CAAA,WAAQ;gBAAC,MAAM;oBAAC;oBAAM;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BACtD,cAAA,8OAAC;oBAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;0BAI1C,8OAAC;gBAAM,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;;kCAC3B,8OAAC,0JAAA,CAAA,MAAG;wBAAC,MAAM;4BAAC;4BAAK;4BAAK;yBAAI;kCACxB,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;kCAG9B,8OAAC,0JAAA,CAAA,MAAG;wBAAC,MAAM;4BAAC;4BAAK;4BAAK;yBAAI;wBAAE,UAAU;4BAAC;4BAAG;4BAAK;yBAAI;kCACjD,cAAA,8OAAC;4BAAqB,OAAM;;;;;;;;;;;;;;;;;;;;;;;AAKtC;AAEA,wDAAwD;AACxD,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAa,WAAW;gBAAK,OAAM;;;;;;0BAGpC,8OAAC;gBACC,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,WAAW;gBACX,OAAM;gBACN,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;gBACvB,qBAAmB;gBACnB,sBAAoB,CAAC;gBACrB,uBAAqB;gBACrB,qBAAmB;gBACnB,wBAAsB,CAAC;;;;;;0BAIzB,8OAAC;gBACC,UAAU;oBAAC,CAAC;oBAAG;oBAAG;iBAAE;gBACpB,WAAW;gBACX,OAAM;;;;;;0BAIR,8OAAC;gBACC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBACpB,WAAW;gBACX,OAAM;;;;;;0BAIR,8OAAC;gBACC,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBACnB,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,OAAM;gBACN,mBAAiB;oBAAC;oBAAG;oBAAG;iBAAE;;;;;;0BAI5B,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BACvC,cAAA,8OAAC;oBACC,OAAM;oBACN,MAAM,+IAAA,CAAA,WAAc;;;;;;;;;;;0BAKxB,8OAAC;gBAAK,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;gBAAE,aAAa;;kCACvE,8OAAC;wBAAc,MAAM;4BAAC;4BAAI;yBAAG;;;;;;kCAC7B,8OAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,WAAW;;;;;;;;;;;;;;AAKrB;AAGe,SAAS,YAAY,EAClC,WAAW,EACX,UAAU,EACV,OAAO,EACP,OAAO,EACP,WAAW,EACX,UAAU,EACE;IACZ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAAC;QAAG;QAAG;KAAI;IAC1F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,eAAe;QACf,YAAY;QACZ,SAAS;IACX;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzD,cAAc;QACd,eAAe;QACf,aAAa;QACb,cAAc;QACd,WAAW;QACX,SAAS;IACX;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,SAAS;YACzB,yCAAyC;YACzC,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,eAAe;YAEnB,MAAM,iBAAiB;gBACrB,IAAI,eAAe,QAAQ,MAAM,IAAI,YAAY;oBAC/C,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,WAAW;oBAC9C,IAAI,UAAU;oBACd,IAAI,gBAAgB;oBACpB,IAAI,aAAa;oBAEjB,2BAA2B;oBAC3B,IAAI,QAAQ,QAAQ,CAAC,OAAO;wBAC1B,UAAU,KAAK,WAAW;wBAC1B,gBAAgB,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;wBAC/E,aAAa,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;oBACzD,OAAO,IAAI,MAAM,QAAQ,CAAC,OAAO;wBAC/B,UAAU,KAAK,WAAW;wBAC1B,gBAAgB;wBAChB,aAAa;oBACf,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;wBAC9B,UAAU,KAAK,WAAW;wBAC1B,gBAAgB;wBAChB,aAAa;oBACf,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;wBAC9B,UAAU;wBACV,gBAAgB;wBAChB,aAAa;oBACf,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;wBAC9B,UAAU,KAAK,WAAW;wBAC1B,gBAAgB;wBAChB,aAAa;oBACf;oBAEA,kBAAkB;wBAAE;wBAAe;wBAAY;oBAAQ;oBACvD;oBAEA,WAAW,gBAAgB,KAAK,KAAK,MAAM,KAAK,KAAI,iBAAiB;gBACvE,OAAO;oBACL,kBAAkB;wBAAE,eAAe;wBAAK,YAAY;wBAAK,SAAS;oBAAU;gBAC9E;YACF;YAEA;QACF,OAAO;YACL,kBAAkB;gBAAE,eAAe;gBAAK,YAAY;gBAAK,SAAS;YAAU;QAC9E;IACF,GAAG;QAAC;QAAY;KAAQ;IAExB,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,OAAO;gBAAE,aAAa;gBAAK,cAAc;gBAAK,WAAW;YAAI;YAC7D,KAAK;gBAAE,aAAa,CAAC;gBAAK,cAAc,CAAC;gBAAK,WAAW;YAAE;YAC3D,WAAW;gBAAE,aAAa,CAAC;gBAAK,cAAc;gBAAK,WAAW;YAAE;YAChE,YAAY;gBAAE,aAAa;gBAAK,cAAc;gBAAK,WAAW;YAAI;YAClE,SAAS;gBAAE,aAAa;gBAAG,cAAc;gBAAG,WAAW;YAAE;QAC3D;QAEA,MAAM,UAAU,eAAe,CAAC,QAAQ;QACxC,qBAAqB,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,GAAG,OAAO;YACZ,CAAC;IACH,GAAG;QAAC;KAAQ;IAEZ,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,kBAAkB;gBAAC;gBAAK;gBAAK;aAAI,GAAE,8BAA8B;QACnE,OAAO,IAAI,YAAY;YACrB,kBAAkB;gBAAC;gBAAG;gBAAG;aAAI,GAAE,yBAAyB;QAC1D,OAAO;YACL,kBAAkB;gBAAC;gBAAG;gBAAG;aAAI,GAAE,mBAAmB;QACpD;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBACL,QAAQ;oBACN,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,KAAK;gBACP;gBACA,OAAO;;kCAEP,8OAAC;;;;;kCACD,8OAAC;wBACC,aAAa;wBACb,YAAY;wBACZ,SAAS;wBACT,aAAa,eAAe;wBAC5B,YAAY,cAAc;;;;;;kCAE5B,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,YAAY;wBACZ,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe;wBACf,eAAe;;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;oBAGT,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/avatar/FacialAnimationEngine.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\n\ninterface FacialAnimationProps {\n  audioData?: Float32Array\n  text?: string\n  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n  isActive: boolean\n  onAnimationUpdate: (animationData: FacialAnimationData) => void\n}\n\nexport interface FacialAnimationData {\n  mouthOpenness: number\n  eyeBlinkLeft: number\n  eyeBlinkRight: number\n  eyebrowPosition: number\n  headRotation: { x: number; y: number; z: number }\n  emotion: string\n  intensity: number\n}\n\n// Advanced Phoneme to mouth shape mapping for realistic lip-sync\nconst PHONEME_MOUTH_SHAPES = {\n  'A': { openness: 0.85, width: 0.65, lipRounding: 0.2, tonguePosition: 0.1 },\n  'E': { openness: 0.55, width: 0.85, lipRounding: 0.1, tonguePosition: 0.3 },\n  'I': { openness: 0.25, width: 0.95, lipRounding: 0.0, tonguePosition: 0.8 },\n  'O': { openness: 0.9, width: 0.35, lipRounding: 0.9, tonguePosition: 0.2 },\n  'U': { openness: 0.65, width: 0.25, lipRounding: 0.95, tonguePosition: 0.1 },\n  'M': { openness: 0.05, width: 0.5, lipRounding: 0.3, tonguePosition: 0.0 },\n  'B': { openness: 0.08, width: 0.5, lipRounding: 0.2, tonguePosition: 0.0 },\n  'P': { openness: 0.02, width: 0.45, lipRounding: 0.4, tonguePosition: 0.0 },\n  'F': { openness: 0.35, width: 0.7, lipRounding: 0.1, tonguePosition: 0.2 },\n  'V': { openness: 0.32, width: 0.72, lipRounding: 0.1, tonguePosition: 0.2 },\n  'TH': { openness: 0.25, width: 0.8, lipRounding: 0.0, tonguePosition: 0.9 },\n  'S': { openness: 0.18, width: 0.65, lipRounding: 0.0, tonguePosition: 0.7 },\n  'SH': { openness: 0.4, width: 0.5, lipRounding: 0.6, tonguePosition: 0.5 },\n  'R': { openness: 0.45, width: 0.6, lipRounding: 0.3, tonguePosition: 0.6 },\n  'L': { openness: 0.35, width: 0.75, lipRounding: 0.1, tonguePosition: 0.8 },\n  'T': { openness: 0.2, width: 0.6, lipRounding: 0.0, tonguePosition: 0.9 },\n  'D': { openness: 0.25, width: 0.65, lipRounding: 0.0, tonguePosition: 0.85 },\n  'N': { openness: 0.15, width: 0.6, lipRounding: 0.0, tonguePosition: 0.8 },\n  'K': { openness: 0.3, width: 0.5, lipRounding: 0.0, tonguePosition: 0.3 },\n  'G': { openness: 0.35, width: 0.55, lipRounding: 0.0, tonguePosition: 0.3 },\n  'silence': { openness: 0.08, width: 0.5, lipRounding: 0.2, tonguePosition: 0.0 }\n}\n\n// Advanced emotion-based facial expressions with micro-expressions\nconst EMOTION_EXPRESSIONS = {\n  neutral: {\n    eyebrowHeight: 0,\n    eyebrowAngle: 0,\n    mouthCurve: 0,\n    mouthCorners: 0,\n    eyeOpenness: 1,\n    eyeSquint: 0,\n    cheekRaise: 0,\n    nostrilFlare: 0,\n    jawTension: 0,\n    foreheadWrinkle: 0,\n    lipTension: 0\n  },\n  happy: {\n    eyebrowHeight: 0.15,\n    eyebrowAngle: 0.1,\n    mouthCurve: 0.7,\n    mouthCorners: 0.8,\n    eyeOpenness: 0.85,\n    eyeSquint: 0.3,\n    cheekRaise: 0.6,\n    nostrilFlare: 0.1,\n    jawTension: 0,\n    foreheadWrinkle: 0,\n    lipTension: 0.2\n  },\n  sad: {\n    eyebrowHeight: -0.4,\n    eyebrowAngle: -0.3,\n    mouthCurve: -0.5,\n    mouthCorners: -0.6,\n    eyeOpenness: 0.7,\n    eyeSquint: 0.1,\n    cheekRaise: -0.2,\n    nostrilFlare: 0,\n    jawTension: 0.2,\n    foreheadWrinkle: 0.4,\n    lipTension: 0.3\n  },\n  concerned: {\n    eyebrowHeight: -0.25,\n    eyebrowAngle: -0.4,\n    mouthCurve: -0.15,\n    mouthCorners: -0.2,\n    eyeOpenness: 1.1,\n    eyeSquint: 0,\n    cheekRaise: 0,\n    nostrilFlare: 0.2,\n    jawTension: 0.3,\n    foreheadWrinkle: 0.6,\n    lipTension: 0.4\n  },\n  empathetic: {\n    eyebrowHeight: 0.1,\n    eyebrowAngle: 0.2,\n    mouthCurve: 0.3,\n    mouthCorners: 0.4,\n    eyeOpenness: 0.95,\n    eyeSquint: 0.1,\n    cheekRaise: 0.3,\n    nostrilFlare: 0,\n    jawTension: 0,\n    foreheadWrinkle: 0.1,\n    lipTension: 0.1\n  }\n}\n\nexport default function FacialAnimationEngine({\n  audioData,\n  text,\n  emotion,\n  isActive,\n  onAnimationUpdate\n}: FacialAnimationProps) {\n  const [currentPhoneme, setCurrentPhoneme] = useState('silence')\n  const [blinkTimer, setBlinkTimer] = useState(0)\n  const [emotionIntensity, setEmotionIntensity] = useState(1)\n  const animationFrameRef = useRef<number>()\n  const audioContextRef = useRef<AudioContext>()\n  const analyserRef = useRef<AnalyserNode>()\n\n  // Advanced phoneme detection from text with context awareness\n  const detectPhonemeFromText = (text: string, position: number): string => {\n    if (!text || position >= text.length) return 'silence'\n\n    const char = text[position].toLowerCase()\n    const nextChar = position + 1 < text.length ? text[position + 1].toLowerCase() : ''\n    const prevChar = position > 0 ? text[position - 1].toLowerCase() : ''\n\n    // Advanced phoneme mapping with digraphs and context\n    const phonemeMap: { [key: string]: string } = {\n      // Vowels\n      'a': 'A', 'e': 'E', 'i': 'I', 'o': 'O', 'u': 'U',\n\n      // Consonants - Bilabials\n      'm': 'M', 'b': 'B', 'p': 'P',\n\n      // Labiodentals\n      'f': 'F', 'v': 'V',\n\n      // Alveolars\n      't': 'T', 'd': 'D', 'n': 'N', 's': 'S', 'z': 'S', 'l': 'L', 'r': 'R',\n\n      // Velars\n      'k': 'K', 'g': 'G',\n\n      // Special cases\n      'c': nextChar === 'h' ? 'SH' : 'K',\n      'j': 'SH',\n      'w': 'U',\n      'y': 'I',\n      'q': 'K',\n      'x': 'S',\n\n      // Silence\n      ' ': 'silence',\n      '.': 'silence',\n      ',': 'silence',\n      '!': 'silence',\n      '?': 'silence'\n    }\n\n    // Handle digraphs\n    if (char === 't' && nextChar === 'h') return 'TH'\n    if (char === 's' && nextChar === 'h') return 'SH'\n    if (char === 'c' && nextChar === 'h') return 'SH'\n    if (char === 'p' && nextChar === 'h') return 'F'\n    if (char === 'g' && nextChar === 'h') return 'G'\n\n    // Handle silent letters\n    if (char === 'h' && prevChar !== '') return 'silence'\n    if (char === 'w' && prevChar === 's') return 'silence'\n    if (char === 'b' && prevChar === 'm') return 'silence'\n    if (char === 'l' && prevChar === 'a' && nextChar === 'k') return 'silence'\n\n    return phonemeMap[char] || 'silence'\n  }\n\n  // Audio analysis for lip-sync\n  const analyzeAudio = (audioData: Float32Array): number => {\n    if (!audioData) return 0\n    \n    let sum = 0\n    for (let i = 0; i < audioData.length; i++) {\n      sum += Math.abs(audioData[i])\n    }\n    return sum / audioData.length\n  }\n\n  // Generate realistic blinking\n  const generateBlink = (deltaTime: number): { left: number; right: number } => {\n    setBlinkTimer(prev => prev + deltaTime)\n    \n    // Random blinking every 2-4 seconds\n    const blinkInterval = 2000 + Math.random() * 2000\n    \n    if (blinkTimer > blinkInterval) {\n      setBlinkTimer(0)\n      // Quick blink animation\n      const blinkPhase = (Date.now() % 200) / 200\n      const blinkValue = Math.sin(blinkPhase * Math.PI)\n      return { left: blinkValue, right: blinkValue }\n    }\n    \n    return { left: 1, right: 1 }\n  }\n\n  // Main animation loop\n  useEffect(() => {\n    if (!isActive) return\n\n    let textPosition = 0\n    let lastTime = Date.now()\n    \n    const animate = () => {\n      const currentTime = Date.now()\n      const deltaTime = currentTime - lastTime\n      lastTime = currentTime\n\n      // Text-based lip-sync animation\n      if (text && textPosition < text.length) {\n        const phoneme = detectPhonemeFromText(text, Math.floor(textPosition))\n        setCurrentPhoneme(phoneme)\n        \n        // Advance through text at speaking pace (adjust speed as needed)\n        textPosition += deltaTime * 0.01 // Adjust this multiplier for speaking speed\n      } else {\n        setCurrentPhoneme('silence')\n      }\n\n      // Audio-based animation (if available)\n      let audioIntensity = 0\n      if (audioData) {\n        audioIntensity = analyzeAudio(audioData)\n      }\n\n      // Generate advanced facial animation data\n      const mouthShape = PHONEME_MOUTH_SHAPES[currentPhoneme as keyof typeof PHONEME_MOUTH_SHAPES] || PHONEME_MOUTH_SHAPES.silence\n      const expression = EMOTION_EXPRESSIONS[emotion]\n      const blink = generateBlink(deltaTime)\n\n      // Calculate micro-expressions and natural variations\n      const naturalVariation = {\n        eyebrow: Math.sin(currentTime * 0.002) * 0.02,\n        mouth: Math.cos(currentTime * 0.003) * 0.01,\n        eye: Math.sin(currentTime * 0.004) * 0.01\n      }\n\n      const animationData: FacialAnimationData = {\n        mouthOpenness: Math.max(0, Math.min(1,\n          mouthShape.openness +\n          audioIntensity * 0.4 +\n          expression.mouthCurve * 0.2 +\n          naturalVariation.mouth\n        )),\n        eyeBlinkLeft: blink.left * (1 + expression.eyeSquint * 0.3),\n        eyeBlinkRight: blink.right * (1 + expression.eyeSquint * 0.3),\n        eyebrowPosition: expression.eyebrowHeight + naturalVariation.eyebrow,\n        headRotation: {\n          x: Math.sin(currentTime * 0.0008) * 0.04 + expression.jawTension * 0.02,\n          y: Math.sin(currentTime * 0.0006) * 0.025 + (audioIntensity * 0.05),\n          z: Math.sin(currentTime * 0.001) * 0.015 + expression.foreheadWrinkle * 0.01\n        },\n        emotion,\n        intensity: emotionIntensity * (1 + Math.sin(currentTime * 0.005) * 0.1)\n      }\n\n      // Add breathing influence on facial features\n      const breathingInfluence = Math.sin(currentTime * 0.0015) * 0.008\n      animationData.mouthOpenness += breathingInfluence\n      animationData.eyebrowPosition += breathingInfluence * 0.5\n\n      onAnimationUpdate(animationData)\n\n      if (isActive) {\n        animationFrameRef.current = requestAnimationFrame(animate)\n      }\n    }\n\n    animate()\n\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current)\n      }\n    }\n  }, [isActive, text, emotion, audioData, onAnimationUpdate])\n\n  // Setup audio analysis\n  useEffect(() => {\n    if (typeof window !== 'undefined' && window.AudioContext) {\n      audioContextRef.current = new AudioContext()\n      analyserRef.current = audioContextRef.current.createAnalyser()\n      analyserRef.current.fftSize = 256\n    }\n\n    return () => {\n      if (audioContextRef.current) {\n        audioContextRef.current.close()\n      }\n    }\n  }, [])\n\n  return null // This is a logic-only component\n}\n\n// Utility function to convert text to estimated phonemes\nexport function textToPhonemes(text: string): Array<{ phoneme: string; duration: number }> {\n  const words = text.split(' ')\n  const phonemes: Array<{ phoneme: string; duration: number }> = []\n  \n  words.forEach((word, wordIndex) => {\n    for (let i = 0; i < word.length; i++) {\n      const char = word[i].toLowerCase()\n      let phoneme = 'silence'\n      \n      // Simple character to phoneme mapping\n      if ('aeiou'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('mbp'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('fv'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('sz'.includes(char)) {\n        phoneme = 'S'\n      } else if ('rl'.includes(char)) {\n        phoneme = char.toUpperCase()\n      }\n      \n      phonemes.push({\n        phoneme,\n        duration: 100 + Math.random() * 100 // Random duration between 100-200ms\n      })\n    }\n    \n    // Add pause between words\n    if (wordIndex < words.length - 1) {\n      phonemes.push({ phoneme: 'silence', duration: 200 })\n    }\n  })\n  \n  return phonemes\n}\n\n// Advanced emotion detection from text\nexport function detectEmotionFromText(text: string): 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic' {\n  const lowerText = text.toLowerCase()\n  \n  // Happy indicators\n  if (lowerText.includes('happy') || lowerText.includes('joy') || lowerText.includes('great') || \n      lowerText.includes('wonderful') || lowerText.includes('excited') || lowerText.includes('😊') ||\n      lowerText.includes('good') || lowerText.includes('amazing')) {\n    return 'happy'\n  }\n  \n  // Sad indicators\n  if (lowerText.includes('sad') || lowerText.includes('depressed') || lowerText.includes('cry') ||\n      lowerText.includes('hurt') || lowerText.includes('pain') || lowerText.includes('😢') ||\n      lowerText.includes('terrible') || lowerText.includes('awful')) {\n    return 'sad'\n  }\n  \n  // Concerned indicators\n  if (lowerText.includes('worried') || lowerText.includes('anxious') || lowerText.includes('scared') ||\n      lowerText.includes('afraid') || lowerText.includes('nervous') || lowerText.includes('stress') ||\n      lowerText.includes('concerned') || lowerText.includes('help')) {\n    return 'concerned'\n  }\n  \n  // Empathetic indicators\n  if (lowerText.includes('understand') || lowerText.includes('support') || lowerText.includes('care') ||\n      lowerText.includes('here for you') || lowerText.includes('listen') || lowerText.includes('comfort')) {\n    return 'empathetic'\n  }\n  \n  return 'neutral'\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAsBA,iEAAiE;AACjE,MAAM,uBAAuB;IAC3B,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAK,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAM,gBAAgB;IAAI;IAC3E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,MAAM;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,MAAM;QAAE,UAAU;QAAK,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,KAAK;QAAE,UAAU;QAAK,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACxE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAK;IAC3E,KAAK;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACzE,KAAK;QAAE,UAAU;QAAK,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;IACxE,KAAK;QAAE,UAAU;QAAM,OAAO;QAAM,aAAa;QAAK,gBAAgB;IAAI;IAC1E,WAAW;QAAE,UAAU;QAAM,OAAO;QAAK,aAAa;QAAK,gBAAgB;IAAI;AACjF;AAEA,mEAAmE;AACnE,MAAM,sBAAsB;IAC1B,SAAS;QACP,eAAe;QACf,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,iBAAiB;QACjB,YAAY;IACd;IACA,OAAO;QACL,eAAe;QACf,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,iBAAiB;QACjB,YAAY;IACd;IACA,KAAK;QACH,eAAe,CAAC;QAChB,cAAc,CAAC;QACf,YAAY,CAAC;QACb,cAAc,CAAC;QACf,aAAa;QACb,WAAW;QACX,YAAY,CAAC;QACb,cAAc;QACd,YAAY;QACZ,iBAAiB;QACjB,YAAY;IACd;IACA,WAAW;QACT,eAAe,CAAC;QAChB,cAAc,CAAC;QACf,YAAY,CAAC;QACb,cAAc,CAAC;QACf,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,iBAAiB;QACjB,YAAY;IACd;IACA,YAAY;QACV,eAAe;QACf,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,iBAAiB;QACjB,YAAY;IACd;AACF;AAEe,SAAS,sBAAsB,EAC5C,SAAS,EACT,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,iBAAiB,EACI;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEzB,8DAA8D;IAC9D,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,QAAQ,YAAY,KAAK,MAAM,EAAE,OAAO;QAE7C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;QACvC,MAAM,WAAW,WAAW,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,KAAK;QACjF,MAAM,WAAW,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,KAAK;QAEnE,qDAAqD;QACrD,MAAM,aAAwC;YAC5C,SAAS;YACT,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAE7C,yBAAyB;YACzB,KAAK;YAAK,KAAK;YAAK,KAAK;YAEzB,eAAe;YACf,KAAK;YAAK,KAAK;YAEf,YAAY;YACZ,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAEjE,SAAS;YACT,KAAK;YAAK,KAAK;YAEf,gBAAgB;YAChB,KAAK,aAAa,MAAM,OAAO;YAC/B,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YAEL,UAAU;YACV,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,kBAAkB;QAClB,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAE7C,wBAAwB;QACxB,IAAI,SAAS,OAAO,aAAa,IAAI,OAAO;QAC5C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,aAAa,OAAO,aAAa,KAAK,OAAO;QAEjE,OAAO,UAAU,CAAC,KAAK,IAAI;IAC7B;IAEA,8BAA8B;IAC9B,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,OAAO;QAEvB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,OAAO,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE;QAC9B;QACA,OAAO,MAAM,UAAU,MAAM;IAC/B;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC;QACrB,cAAc,CAAA,OAAQ,OAAO;QAE7B,oCAAoC;QACpC,MAAM,gBAAgB,OAAO,KAAK,MAAM,KAAK;QAE7C,IAAI,aAAa,eAAe;YAC9B,cAAc;YACd,wBAAwB;YACxB,MAAM,aAAa,AAAC,KAAK,GAAG,KAAK,MAAO;YACxC,MAAM,aAAa,KAAK,GAAG,CAAC,aAAa,KAAK,EAAE;YAChD,OAAO;gBAAE,MAAM;gBAAY,OAAO;YAAW;QAC/C;QAEA,OAAO;YAAE,MAAM;YAAG,OAAO;QAAE;IAC7B;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,IAAI,eAAe;QACnB,IAAI,WAAW,KAAK,GAAG;QAEvB,MAAM,UAAU;YACd,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,YAAY,cAAc;YAChC,WAAW;YAEX,gCAAgC;YAChC,IAAI,QAAQ,eAAe,KAAK,MAAM,EAAE;gBACtC,MAAM,UAAU,sBAAsB,MAAM,KAAK,KAAK,CAAC;gBACvD,kBAAkB;gBAElB,iEAAiE;gBACjE,gBAAgB,YAAY,MAAK,4CAA4C;YAC/E,OAAO;gBACL,kBAAkB;YACpB;YAEA,uCAAuC;YACvC,IAAI,iBAAiB;YACrB,IAAI,WAAW;gBACb,iBAAiB,aAAa;YAChC;YAEA,0CAA0C;YAC1C,MAAM,aAAa,oBAAoB,CAAC,eAAoD,IAAI,qBAAqB,OAAO;YAC5H,MAAM,aAAa,mBAAmB,CAAC,QAAQ;YAC/C,MAAM,QAAQ,cAAc;YAE5B,qDAAqD;YACrD,MAAM,mBAAmB;gBACvB,SAAS,KAAK,GAAG,CAAC,cAAc,SAAS;gBACzC,OAAO,KAAK,GAAG,CAAC,cAAc,SAAS;gBACvC,KAAK,KAAK,GAAG,CAAC,cAAc,SAAS;YACvC;YAEA,MAAM,gBAAqC;gBACzC,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAClC,WAAW,QAAQ,GACnB,iBAAiB,MACjB,WAAW,UAAU,GAAG,MACxB,iBAAiB,KAAK;gBAExB,cAAc,MAAM,IAAI,GAAG,CAAC,IAAI,WAAW,SAAS,GAAG,GAAG;gBAC1D,eAAe,MAAM,KAAK,GAAG,CAAC,IAAI,WAAW,SAAS,GAAG,GAAG;gBAC5D,iBAAiB,WAAW,aAAa,GAAG,iBAAiB,OAAO;gBACpE,cAAc;oBACZ,GAAG,KAAK,GAAG,CAAC,cAAc,UAAU,OAAO,WAAW,UAAU,GAAG;oBACnE,GAAG,KAAK,GAAG,CAAC,cAAc,UAAU,QAAS,iBAAiB;oBAC9D,GAAG,KAAK,GAAG,CAAC,cAAc,SAAS,QAAQ,WAAW,eAAe,GAAG;gBAC1E;gBACA;gBACA,WAAW,mBAAmB,CAAC,IAAI,KAAK,GAAG,CAAC,cAAc,SAAS,GAAG;YACxE;YAEA,6CAA6C;YAC7C,MAAM,qBAAqB,KAAK,GAAG,CAAC,cAAc,UAAU;YAC5D,cAAc,aAAa,IAAI;YAC/B,cAAc,eAAe,IAAI,qBAAqB;YAEtD,kBAAkB;YAElB,IAAI,UAAU;gBACZ,kBAAkB,OAAO,GAAG,sBAAsB;YACpD;QACF;QAEA;QAEA,OAAO;YACL,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;QACF;IACF,GAAG;QAAC;QAAU;QAAM;QAAS;QAAW;KAAkB;IAE1D,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;QAMA,OAAO;YACL,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,KAAK;YAC/B;QACF;IACF,GAAG,EAAE;IAEL,OAAO,KAAK,iCAAiC;;AAC/C;AAGO,SAAS,eAAe,IAAY;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,WAAyD,EAAE;IAEjE,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW;YAChC,IAAI,UAAU;YAEd,sCAAsC;YACtC,IAAI,QAAQ,QAAQ,CAAC,OAAO;gBAC1B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,MAAM,QAAQ,CAAC,OAAO;gBAC/B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU;YACZ,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU,KAAK,WAAW;YAC5B;YAEA,SAAS,IAAI,CAAC;gBACZ;gBACA,UAAU,MAAM,KAAK,MAAM,KAAK,IAAI,oCAAoC;YAC1E;QACF;QAEA,0BAA0B;QAC1B,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;YAChC,SAAS,IAAI,CAAC;gBAAE,SAAS;gBAAW,UAAU;YAAI;QACpD;IACF;IAEA,OAAO;AACT;AAGO,SAAS,sBAAsB,IAAY;IAChD,MAAM,YAAY,KAAK,WAAW;IAElC,mBAAmB;IACnB,IAAI,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,YAC/E,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,SACvF,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,YAAY;QAC/D,OAAO;IACT;IAEA,iBAAiB;IACjB,IAAI,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,UACnF,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,SAC/E,UAAU,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,UAAU;QACjE,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,aACrF,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,aACpF,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,SAAS;QACjE,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,UAAU,QAAQ,CAAC,iBAAiB,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,WACxF,UAAU,QAAQ,CAAC,mBAAmB,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,YAAY;QACvG,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/avatar-chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, MicrophoneIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline'\nimport { generateId } from '@/lib/utils'\nimport VoiceChat from '@/components/chatbot/VoiceChat'\nimport HumanAvatar from '@/components/avatar/HumanAvatar'\nimport FacialAnimationEngine, { FacialAnimationData, detectEmotionFromText } from '@/components/avatar/FacialAnimationEngine'\nimport AudioAnalyzer, { generateLipSyncFromAudio } from '@/components/avatar/AudioAnalyzer'\n\ninterface Message {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n  isTyping?: boolean\n  typedContent?: string\n  safetyAlert?: {\n    riskLevel: string\n    keywords: string[]\n  }\n  language?: 'en' | 'ta'\n  emotion?: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n}\n\nexport default function AvatarChatPage() {\n  const [messages, setMessages] = useState<Message[]>([])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [language, setLanguage] = useState<'en' | 'ta'>('en')\n  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)\n  \n  // Avatar states\n  const [avatarEmotion, setAvatarEmotion] = useState<'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'>('neutral')\n  const [avatarIsSpeaking, setAvatarIsSpeaking] = useState(false)\n  const [avatarIsListening, setAvatarIsListening] = useState(false)\n  const [currentSpeechText, setCurrentSpeechText] = useState('')\n  const [facialAnimationData, setFacialAnimationData] = useState<FacialAnimationData | null>(null)\n  const [realTimeLipSync, setRealTimeLipSync] = useState({\n    mouthOpenness: 0,\n    mouthWidth: 0,\n    phoneme: 'silence'\n  })\n  const [realTimeFacialData, setRealTimeFacialData] = useState({\n    eyeBlinkLeft: 1,\n    eyeBlinkRight: 1,\n    eyebrowLeft: 0,\n    eyebrowRight: 0,\n    cheekPuff: 0,\n    jawOpen: 0\n  })\n  const [audioAnalysisActive, setAudioAnalysisActive] = useState(false)\n  \n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const typingTimeoutRef = useRef<NodeJS.Timeout>()\n  const speechTimeoutRef = useRef<NodeJS.Timeout>()\n\n  // Initialize with welcome message\n  useEffect(() => {\n    const welcomeMessage = {\n      id: generateId(),\n      role: 'assistant' as const,\n      content: language === 'ta' \n        ? 'வணக்கம்! நான் உங்கள் AI மனநல ஆலோசகர். நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்?'\n        : 'Hello! I\\'m your AI mental health counselor. I\\'m here to provide emotional support and guidance. How are you feeling today?',\n      timestamp: new Date().toISOString(),\n      language,\n      emotion: 'empathetic' as const\n    }\n    \n    setMessages([welcomeMessage])\n    setAvatarEmotion('empathetic')\n    \n    // Auto-speak welcome message\n    setTimeout(() => {\n      if (speakResponse) {\n        speakResponse(welcomeMessage.content, language)\n        setAvatarIsSpeaking(true)\n        setCurrentSpeechText(welcomeMessage.content)\n        \n        // Stop speaking after estimated duration\n        const estimatedDuration = welcomeMessage.content.length * 80 // ~80ms per character\n        speechTimeoutRef.current = setTimeout(() => {\n          setAvatarIsSpeaking(false)\n          setCurrentSpeechText('')\n        }, estimatedDuration)\n      }\n    }, 1000)\n  }, [language, speakResponse])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  // Handle facial animation updates\n  const handleFacialAnimationUpdate = useCallback((animationData: FacialAnimationData) => {\n    setFacialAnimationData(animationData)\n  }, [])\n\n  // Typing effect for assistant messages\n  const typeMessage = useCallback((message: Message) => {\n    const words = message.content.split(' ')\n    let currentIndex = 0\n    \n    const typeNextWord = () => {\n      if (currentIndex < words.length) {\n        const typedContent = words.slice(0, currentIndex + 1).join(' ')\n        \n        setMessages(prev => prev.map(msg => \n          msg.id === message.id \n            ? { ...msg, typedContent, isTyping: true }\n            : msg\n        ))\n        \n        currentIndex++\n        typingTimeoutRef.current = setTimeout(typeNextWord, 150 + Math.random() * 100)\n      } else {\n        setMessages(prev => prev.map(msg => \n          msg.id === message.id \n            ? { ...msg, isTyping: false, typedContent: message.content }\n            : msg\n        ))\n        \n        // Start avatar speaking\n        if (message.role === 'assistant') {\n          setAvatarIsSpeaking(true)\n          setCurrentSpeechText(message.content)\n          setAvatarEmotion(message.emotion || 'neutral')\n          \n          // Auto-speak the response\n          if (speakResponse) {\n            setTimeout(() => {\n              speakResponse(message.content, message.language || language)\n            }, 500)\n          }\n          \n          // Stop speaking after estimated duration\n          const estimatedDuration = message.content.length * 80\n          speechTimeoutRef.current = setTimeout(() => {\n            setAvatarIsSpeaking(false)\n            setCurrentSpeechText('')\n            setAvatarEmotion('neutral')\n          }, estimatedDuration)\n        }\n      }\n    }\n    \n    typeNextWord()\n  }, [speakResponse, language])\n\n  // Handle voice transcript\n  const handleVoiceTranscript = useCallback((transcript: string) => {\n    setInputMessage(transcript)\n    setAvatarIsListening(false)\n    // Auto-send voice messages\n    setTimeout(() => {\n      sendMessage(transcript)\n    }, 500)\n  }, [])\n\n  // Handle voice response setup\n  const handleSpeakResponse = useCallback((speakFn: any) => {\n    setSpeakResponse(() => speakFn)\n  }, [])\n\n  // Update avatar listening state\n  useEffect(() => {\n    setAvatarIsListening(isListening)\n  }, [isListening])\n\n  const sendMessage = async (messageText?: string) => {\n    const textToSend = messageText || inputMessage.trim()\n    if (!textToSend || isLoading) return\n\n    const userMessage: Message = {\n      id: generateId(),\n      role: 'user',\n      content: textToSend,\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    // Show typing indicator\n    const typingMessage: Message = {\n      id: generateId(),\n      role: 'assistant',\n      content: language === 'ta' ? 'சிந்திக்கிறேன்...' : 'Thinking...',\n      timestamp: new Date().toISOString(),\n      isTyping: true,\n      language,\n      emotion: 'neutral'\n    }\n    \n    setMessages(prev => [...prev, typingMessage])\n    setAvatarEmotion('concerned') // Show thinking expression\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: textToSend,\n          sessionId: generateId(),\n          userId: 'anonymous',\n          language\n        }),\n      })\n\n      const data = await response.json()\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      if (data.success) {\n        let responseContent = data.data.content\n        \n        // Detect emotion from response\n        const detectedEmotion = detectEmotionFromText(responseContent)\n        \n        // Basic Tamil translation for common responses\n        if (language === 'ta' && !responseContent.includes('தமிழ்')) {\n          const translations: { [key: string]: string } = {\n            'I understand': 'நான் புரிந்துகொள்கிறேன்',\n            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',\n            'I\\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',\n            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',\n            'That sounds difficult': 'அது கடினமாக இருக்கும்',\n            'You\\'re not alone': 'நீங்கள் தனியாக இல்லை',\n            'I\\'m sorry to hear': 'கேட்டு வருந்துகிறேன்',\n            'That\\'s wonderful': 'அது அருமை',\n            'How can I help': 'நான் எப்படி உதவ முடியும்'\n          }\n          \n          Object.entries(translations).forEach(([en, ta]) => {\n            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)\n          })\n        }\n\n        const assistantMessage: Message = {\n          id: generateId(),\n          role: 'assistant',\n          content: responseContent,\n          timestamp: data.data.timestamp,\n          safetyAlert: data.data.safetyAlert,\n          language,\n          emotion: detectedEmotion,\n          isTyping: true,\n          typedContent: ''\n        }\n        \n        setMessages(prev => [...prev, assistantMessage])\n        \n        // Start typing effect\n        setTimeout(() => {\n          typeMessage(assistantMessage)\n        }, 500)\n        \n      } else {\n        throw new Error(data.error || 'Failed to get response')\n      }\n    } catch (error) {\n      console.error('Error sending message:', error)\n      \n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n      \n      const errorMessage: Message = {\n        id: generateId(),\n        role: 'assistant',\n        content: language === 'ta' \n          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும்.'\n          : 'I apologize, but I\\'m having trouble responding right now. Please try again.',\n        timestamp: new Date().toISOString(),\n        language,\n        emotion: 'concerned'\n      }\n      setMessages(prev => [...prev, errorMessage])\n      typeMessage(errorMessage)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto p-4 h-[calc(100vh-100px)]\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 h-full\">\n\n        {/* Avatar Section */}\n        <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4\">\n            <h2 className=\"text-xl font-semibold\">\n              {language === 'ta' ? 'AI ஆலோசகர்' : 'AI Counselor'}\n            </h2>\n            <p className=\"text-purple-100 text-sm\">\n              {language === 'ta'\n                ? 'உங்கள் மனநல ஆதரவு நண்பர்'\n                : 'Your Mental Health Support Companion'\n              }\n            </p>\n          </div>\n\n          <HumanAvatar\n            isListening={avatarIsListening}\n            isSpeaking={avatarIsSpeaking}\n            emotion={avatarEmotion}\n            message={avatarIsSpeaking ? currentSpeechText : undefined}\n          />\n\n          {/* Facial Animation Engine */}\n          <FacialAnimationEngine\n            text={avatarIsSpeaking ? currentSpeechText : undefined}\n            emotion={avatarEmotion}\n            isActive={avatarIsSpeaking}\n            onAnimationUpdate={handleFacialAnimationUpdate}\n          />\n        </div>\n\n        {/* Chat Section */}\n        <div className=\"bg-white rounded-lg shadow-lg flex flex-col\">\n          {/* Chat Header */}\n          <div className=\"bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-xl font-semibold\">\n                  {language === 'ta' ? 'உரையாடல்' : 'Conversation'}\n                </h1>\n                <p className=\"text-pink-100 text-sm\">\n                  {language === 'ta'\n                    ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு'\n                    : 'Safe, confidential emotional support'\n                  }\n                </p>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm\">\n                  {language === 'ta' ? 'ஆன்லைன்' : 'Online'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Voice Chat Component */}\n          <VoiceChat\n            onTranscript={handleVoiceTranscript}\n            onSpeakResponse={handleSpeakResponse}\n            isListening={isListening}\n            setIsListening={setIsListening}\n            language={language}\n            setLanguage={setLanguage}\n          />\n\n          {/* Emergency Notice */}\n          <div className=\"bg-red-50 border-l-4 border-red-400 p-3\">\n            <div className=\"flex\">\n              <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700\">\n                  <strong>{language === 'ta' ? 'அவசரநிலை:' : 'Emergency:'}</strong>{' '}\n                  {language === 'ta'\n                    ? 'உடனடி ஆபத்தில் இருந்தால், 911 அழைக்கவும். நெருக்கடி ஆதரவுக்கு 988 அழைக்கவும்.'\n                    : 'If in immediate danger, call 911. For crisis support, call 988.'\n                  }\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Simple Chat Display for now */}\n          <div className=\"flex-1 p-4 bg-gray-50\">\n            <div className=\"text-center text-gray-600\">\n              <p className=\"text-lg font-semibold mb-2\">\n                {language === 'ta' ? '3D அவதார் சாட்' : '3D Avatar Chat'}\n              </p>\n              <p className=\"text-sm\">\n                {language === 'ta'\n                  ? 'இடதுபுறத்தில் உள்ள 3D அவதாருடன் பேசுங்கள்!'\n                  : 'Interact with the 3D avatar on the left!'\n                }\n              </p>\n              <div className=\"mt-4 space-y-2\">\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'வணக்கம்!' : 'Hello!')}\n                  className=\"block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors\"\n                >\n                  {language === 'ta' ? '👋 வணக்கம் சொல்லுங்கள்' : '👋 Say Hello'}\n                </button>\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious')}\n                  className=\"block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\"\n                >\n                  {language === 'ta' ? '😰 கவலையை பகிர்ந்து கொள்ளுங்கள்' : '😰 Share Anxiety'}\n                </button>\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help')}\n                  className=\"block w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors\"\n                >\n                  {language === 'ta' ? '🆘 உதவி கேளுங்கள்' : '🆘 Ask for Help'}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAEvG,gBAAgB;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4D;IAC7G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IAC3F,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,eAAe;QACf,YAAY;QACZ,SAAS;IACX;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,cAAc;QACd,eAAe;QACf,aAAa;QACb,cAAc;QACd,WAAW;QACX,SAAS;IACX;IACA,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC9B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE9B,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAClB,2JACA;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC;YACA,SAAS;QACX;QAEA,YAAY;YAAC;SAAe;QAC5B,iBAAiB;QAEjB,6BAA6B;QAC7B,WAAW;YACT,IAAI,eAAe;gBACjB,cAAc,eAAe,OAAO,EAAE;gBACtC,oBAAoB;gBACpB,qBAAqB,eAAe,OAAO;gBAE3C,yCAAyC;gBACzC,MAAM,oBAAoB,eAAe,OAAO,CAAC,MAAM,GAAG,GAAG,sBAAsB;;gBACnF,iBAAiB,OAAO,GAAG,WAAW;oBACpC,oBAAoB;oBACpB,qBAAqB;gBACvB,GAAG;YACL;QACF,GAAG;IACL,GAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,kCAAkC;IAClC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/C,uBAAuB;IACzB,GAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,CAAC;QACpC,IAAI,eAAe;QAEnB,MAAM,eAAe;YACnB,IAAI,eAAe,MAAM,MAAM,EAAE;gBAC/B,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,eAAe,GAAG,IAAI,CAAC;gBAE3D,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE;4BAAc,UAAU;wBAAK,IACvC;gBAGN;gBACA,iBAAiB,OAAO,GAAG,WAAW,cAAc,MAAM,KAAK,MAAM,KAAK;YAC5E,OAAO;gBACL,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE,UAAU;4BAAO,cAAc,QAAQ,OAAO;wBAAC,IACzD;gBAGN,wBAAwB;gBACxB,IAAI,QAAQ,IAAI,KAAK,aAAa;oBAChC,oBAAoB;oBACpB,qBAAqB,QAAQ,OAAO;oBACpC,iBAAiB,QAAQ,OAAO,IAAI;oBAEpC,0BAA0B;oBAC1B,IAAI,eAAe;wBACjB,WAAW;4BACT,cAAc,QAAQ,OAAO,EAAE,QAAQ,QAAQ,IAAI;wBACrD,GAAG;oBACL;oBAEA,yCAAyC;oBACzC,MAAM,oBAAoB,QAAQ,OAAO,CAAC,MAAM,GAAG;oBACnD,iBAAiB,OAAO,GAAG,WAAW;wBACpC,oBAAoB;wBACpB,qBAAqB;wBACrB,iBAAiB;oBACnB,GAAG;gBACL;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,WAAW;YACT,YAAY;QACd,GAAG;IACL,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,iBAAiB,IAAM;IACzB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;IACvB,GAAG;QAAC;KAAY;IAEhB,MAAM,cAAc,OAAO;QACzB,MAAM,aAAa,eAAe,aAAa,IAAI;QACnD,IAAI,CAAC,cAAc,WAAW;QAE9B,MAAM,cAAuB;YAC3B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,wBAAwB;QACxB,MAAM,gBAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAAO,sBAAsB;YACnD,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV;YACA,SAAS;QACX;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAC5C,iBAAiB,cAAa,2BAA2B;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACpB,QAAQ;oBACR;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBAEvC,+BAA+B;gBAC/B,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE;gBAE9C,+CAA+C;gBAC/C,IAAI,aAAa,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,UAAU;oBAC3D,MAAM,eAA0C;wBAC9C,gBAAgB;wBAChB,uBAAuB;wBACvB,qBAAqB;wBACrB,yBAAyB;wBACzB,yBAAyB;wBACzB,qBAAqB;wBACrB,sBAAsB;wBACtB,qBAAqB;wBACrB,kBAAkB;oBACpB;oBAEA,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;wBAC5C,kBAAkB,gBAAgB,OAAO,CAAC,IAAI,OAAO,IAAI,OAAO;oBAClE;gBACF;gBAEA,MAAM,mBAA4B;oBAChC,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,SAAS;oBACT,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC;oBACA,SAAS;oBACT,UAAU;oBACV,cAAc;gBAChB;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE/C,sBAAsB;gBACtB,WAAW;oBACT,YAAY;gBACd,GAAG;YAEL,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,MAAM,eAAwB;gBAC5B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,MAAM;gBACN,SAAS,aAAa,OAClB,iFACA;gBACJ,WAAW,IAAI,OAAO,WAAW;gBACjC;gBACA,SAAS;YACX;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC3C,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,aAAa,OAAO,eAAe;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,aAAa,OACV,6BACA;;;;;;;;;;;;sCAKR,8OAAC,2IAAA,CAAA,UAAW;4BACV,aAAa;4BACb,YAAY;4BACZ,SAAS;4BACT,SAAS,mBAAmB,oBAAoB;;;;;;sCAIlD,8OAAC,qJAAA,CAAA,UAAqB;4BACpB,MAAM,mBAAmB,oBAAoB;4BAC7C,SAAS;4BACT,UAAU;4BACV,mBAAmB;;;;;;;;;;;;8BAKvB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,aAAa;;;;;;0DAEpC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OACV,sCACA;;;;;;;;;;;;kDAIR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DACb,aAAa,OAAO,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC,0IAAA,CAAA,UAAS;4BACR,cAAc;4BACd,iBAAiB;4BACjB,aAAa;4BACb,gBAAgB;4BAChB,UAAU;4BACV,aAAa;;;;;;sCAIf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAQ,aAAa,OAAO,cAAc;;;;;;gDAAuB;gDACjE,aAAa,OACV,kFACA;;;;;;;;;;;;;;;;;;;;;;;sCAQZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,aAAa,OAAO,mBAAmB;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,+CACA;;;;;;kDAGN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,aAAa;gDAC5D,WAAU;0DAET,aAAa,OAAO,2BAA2B;;;;;;0DAElD,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,4BAA4B;gDAC3E,WAAU;0DAET,aAAa,OAAO,oCAAoC;;;;;;0DAE3D,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,qBAAqB;gDACpE,WAAU;0DAET,aAAa,OAAO,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D", "debugId": null}}]}