{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/[<>]/g, '')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,OAAO,CAAC,uDAAuD,IACzE,OAAO,CAAC,SAAS,IACjB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/chatbot/VoiceChat.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { MicrophoneIcon, SpeakerWaveIcon, StopIcon, LanguageIcon } from '@heroicons/react/24/outline'\n\ninterface VoiceChatProps {\n  onTranscript: (text: string) => void\n  onSpeakResponse: (text: string, language: 'en' | 'ta') => void\n  isListening: boolean\n  setIsListening: (listening: boolean) => void\n  language: 'en' | 'ta'\n  setLanguage: (lang: 'en' | 'ta') => void\n}\n\nexport default function VoiceChat({\n  onTranscript,\n  onSpeakResponse,\n  isListening,\n  setIsListening,\n  language,\n  setLanguage\n}: VoiceChatProps) {\n  const [isSupported, setIsSupported] = useState(false)\n  const [isSpeaking, setIsSpeaking] = useState(false)\n  const [transcript, setTranscript] = useState('')\n  \n  const recognitionRef = useRef<any>(null)\n  const synthRef = useRef<SpeechSynthesis | null>(null)\n\n  useEffect(() => {\n    // Check for speech recognition support\n    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition\n    const speechSynthesis = window.speechSynthesis\n\n    if (SpeechRecognition && speechSynthesis) {\n      setIsSupported(true)\n      synthRef.current = speechSynthesis\n\n      // Initialize speech recognition\n      const recognition = new SpeechRecognition()\n      recognition.continuous = true\n      recognition.interimResults = true\n      recognition.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n\n      recognition.onstart = () => {\n        console.log('Voice recognition started')\n      }\n\n      recognition.onresult = (event: any) => {\n        let finalTranscript = ''\n        let interimTranscript = ''\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript\n          } else {\n            interimTranscript += transcript\n          }\n        }\n\n        setTranscript(interimTranscript)\n\n        if (finalTranscript) {\n          onTranscript(finalTranscript)\n          setTranscript('')\n          setIsListening(false)\n        }\n      }\n\n      recognition.onerror = (event: any) => {\n        console.error('Speech recognition error:', event.error)\n        setIsListening(false)\n      }\n\n      recognition.onend = () => {\n        setIsListening(false)\n      }\n\n      recognitionRef.current = recognition\n    }\n\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop()\n      }\n    }\n  }, [language, onTranscript, setIsListening])\n\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      recognitionRef.current.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n      recognitionRef.current.start()\n      setIsListening(true)\n    }\n  }\n\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      recognitionRef.current.stop()\n      setIsListening(false)\n    }\n  }\n\n  const speakText = (text: string, lang: 'en' | 'ta' = language) => {\n    if (synthRef.current && text) {\n      // Stop any current speech\n      synthRef.current.cancel()\n\n      const utterance = new SpeechSynthesisUtterance(text)\n      \n      // Set language and voice\n      utterance.lang = lang === 'ta' ? 'ta-IN' : 'en-US'\n      utterance.rate = 0.9\n      utterance.pitch = 1\n      utterance.volume = 1\n\n      // Try to find a suitable voice\n      const voices = synthRef.current.getVoices()\n      const preferredVoice = voices.find(voice => \n        lang === 'ta' \n          ? voice.lang.includes('ta') || voice.lang.includes('Tamil')\n          : voice.lang.includes('en') && voice.lang.includes('US')\n      )\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice\n      }\n\n      utterance.onstart = () => setIsSpeaking(true)\n      utterance.onend = () => setIsSpeaking(false)\n      utterance.onerror = () => setIsSpeaking(false)\n\n      synthRef.current.speak(utterance)\n    }\n  }\n\n  const stopSpeaking = () => {\n    if (synthRef.current) {\n      synthRef.current.cancel()\n      setIsSpeaking(false)\n    }\n  }\n\n  // Expose speakText function to parent\n  useEffect(() => {\n    onSpeakResponse(speakText as any, language)\n  }, [language, onSpeakResponse])\n\n  if (!isSupported) {\n    return (\n      <div className=\"text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n        <p className=\"text-yellow-800\">\n          Voice features are not supported in your browser. Please use Chrome or Edge for voice interaction.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg\">\n      {/* Language Toggle */}\n      <div className=\"flex items-center space-x-2\">\n        <LanguageIcon className=\"h-5 w-5 text-gray-500\" />\n        <select\n          value={language}\n          onChange={(e) => setLanguage(e.target.value as 'en' | 'ta')}\n          className=\"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500\"\n        >\n          <option value=\"en\">English</option>\n          <option value=\"ta\">தமிழ் (Tamil)</option>\n        </select>\n      </div>\n\n      {/* Voice Input */}\n      <div className=\"flex items-center space-x-2\">\n        {!isListening ? (\n          <button\n            onClick={startListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors\"\n          >\n            <MicrophoneIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'பேசுங்கள்' : 'Speak'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'நிறுத்து' : 'Stop'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Speech Output Control */}\n      <div className=\"flex items-center space-x-2\">\n        {!isSpeaking ? (\n          <button\n            onClick={() => speakText(language === 'ta' \n              ? 'வணக்கம்! நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்.' \n              : 'Hello! I am here to help you.'\n            )}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <SpeakerWaveIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'சோதனை' : 'Test Voice'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopSpeaking}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'அமைதி' : 'Stop Voice'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Live Transcript */}\n      {transcript && (\n        <div className=\"flex-1 text-sm text-gray-600 italic\">\n          {language === 'ta' ? 'கேட்கிறது: ' : 'Listening: '}{transcript}\n        </div>\n      )}\n\n      {/* Status Indicators */}\n      <div className=\"flex space-x-2\">\n        {isListening && (\n          <div className=\"flex items-center space-x-1 text-red-600\">\n            <div className=\"w-2 h-2 bg-red-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'கேட்கிறது' : 'Listening'}\n            </span>\n          </div>\n        )}\n        {isSpeaking && (\n          <div className=\"flex items-center space-x-1 text-blue-600\">\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'பேசுகிறது' : 'Speaking'}\n            </span>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAce,SAAS,UAAU,EAChC,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACI;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuC;QACvC,MAAM,oBAAoB,AAAC,OAAe,iBAAiB,IAAI,AAAC,OAAe,uBAAuB;QACtG,MAAM,kBAAkB,OAAO,eAAe;QAE9C,IAAI,qBAAqB,iBAAiB;YACxC,eAAe;YACf,SAAS,OAAO,GAAG;YAEnB,gCAAgC;YAChC,MAAM,cAAc,IAAI;YACxB,YAAY,UAAU,GAAG;YACzB,YAAY,cAAc,GAAG;YAC7B,YAAY,IAAI,GAAG,aAAa,OAAO,UAAU;YAEjD,YAAY,OAAO,GAAG;gBACpB,QAAQ,GAAG,CAAC;YACd;YAEA,YAAY,QAAQ,GAAG,CAAC;gBACtB,IAAI,kBAAkB;gBACtB,IAAI,oBAAoB;gBAExB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;oBAC7D,MAAM,aAAa,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;oBACjD,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;wBAC5B,mBAAmB;oBACrB,OAAO;wBACL,qBAAqB;oBACvB;gBACF;gBAEA,cAAc;gBAEd,IAAI,iBAAiB;oBACnB,aAAa;oBACb,cAAc;oBACd,eAAe;gBACjB;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;gBACtD,eAAe;YACjB;YAEA,YAAY,KAAK,GAAG;gBAClB,eAAe;YACjB;YAEA,eAAe,OAAO,GAAG;QAC3B;QAEA,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,IAAI;YAC7B;QACF;IACF,GAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,iBAAiB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,aAAa;YAC1C,eAAe,OAAO,CAAC,IAAI,GAAG,aAAa,OAAO,UAAU;YAC5D,eAAe,OAAO,CAAC,KAAK;YAC5B,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,OAAO,IAAI,aAAa;YACzC,eAAe,OAAO,CAAC,IAAI;YAC3B,eAAe;QACjB;IACF;IAEA,MAAM,YAAY,CAAC,MAAc,OAAoB,QAAQ;QAC3D,IAAI,SAAS,OAAO,IAAI,MAAM;YAC5B,0BAA0B;YAC1B,SAAS,OAAO,CAAC,MAAM;YAEvB,MAAM,YAAY,IAAI,yBAAyB;YAE/C,yBAAyB;YACzB,UAAU,IAAI,GAAG,SAAS,OAAO,UAAU;YAC3C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,+BAA+B;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,SAAS,OACL,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,WACjD,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC;YAGvD,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,OAAO,GAAG,IAAM,cAAc;YACxC,UAAU,KAAK,GAAG,IAAM,cAAc;YACtC,UAAU,OAAO,GAAG,IAAM,cAAc;YAExC,SAAS,OAAO,CAAC,KAAK,CAAC;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,MAAM;YACvB,cAAc;QAChB;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,WAAkB;IACpC,GAAG;QAAC;QAAU;KAAgB;IAE9B,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAkB;;;;;;;;;;;IAKrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,2NAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;sCAC1B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,cAAc;;;;;;;;;;;yCAIvC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,8OAAC;oBACC,SAAS,IAAM,UAAU,aAAa,OAClC,oDACA;oBAEJ,WAAU;;sCAEV,8OAAC,6NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;yCAInC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;YAOtC,4BACC,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,OAAO,gBAAgB;oBAAe;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;oBAIxC,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/avatar/HumanAvatar.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\nimport { <PERSON>vas, useFrame, useThree } from '@react-three/fiber'\nimport { OrbitControls, Text, Sphere, Box } from '@react-three/drei'\nimport * as THREE from 'three'\n\ninterface AvatarProps {\n  isListening: boolean\n  isSpeaking: boolean\n  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n  message?: string\n}\n\n// Custom 3D Human Head Component\nfunction HumanHead({ isListening, isSpeaking, emotion }: Omit<AvatarProps, 'message'>) {\n  const headRef = useRef<THREE.Group>(null)\n  const leftEyeRef = useRef<THREE.Mesh>(null)\n  const rightEyeRef = useRef<THREE.Mesh>(null)\n  const mouthRef = useRef<THREE.Mesh>(null)\n  const eyebrowLeftRef = useRef<THREE.Mesh>(null)\n  const eyebrowRightRef = useRef<THREE.Mesh>(null)\n  \n  const [blinkTimer, setBlinkTimer] = useState(0)\n  const [mouthAnimation, setMouthAnimation] = useState(0)\n\n  // Animation loop\n  useFrame((state, delta) => {\n    if (!headRef.current) return\n\n    // Breathing animation\n    const breathingScale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02\n    headRef.current.scale.setScalar(breathingScale)\n\n    // Blinking animation\n    setBlinkTimer(prev => prev + delta)\n    if (blinkTimer > 3) {\n      setBlinkTimer(0)\n      // Blink animation\n      if (leftEyeRef.current && rightEyeRef.current) {\n        const blinkScale = Math.max(0.1, Math.sin(state.clock.elapsedTime * 20))\n        leftEyeRef.current.scale.y = blinkScale\n        rightEyeRef.current.scale.y = blinkScale\n      }\n    }\n\n    // Speaking animation\n    if (isSpeaking && mouthRef.current) {\n      const mouthMovement = Math.sin(state.clock.elapsedTime * 8) * 0.3 + 0.7\n      mouthRef.current.scale.y = mouthMovement\n      setMouthAnimation(mouthMovement)\n    } else if (mouthRef.current) {\n      mouthRef.current.scale.y = THREE.MathUtils.lerp(mouthRef.current.scale.y, 0.3, delta * 5)\n    }\n\n    // Listening animation - slight head tilt\n    if (isListening && headRef.current) {\n      const tilt = Math.sin(state.clock.elapsedTime * 1.5) * 0.1\n      headRef.current.rotation.z = tilt\n    } else if (headRef.current) {\n      headRef.current.rotation.z = THREE.MathUtils.lerp(headRef.current.rotation.z, 0, delta * 3)\n    }\n  })\n\n  // Emotion-based expressions\n  const getEmotionColors = () => {\n    switch (emotion) {\n      case 'happy':\n        return { skin: '#FFE4C4', cheek: '#FFB6C1' }\n      case 'sad':\n        return { skin: '#F5DEB3', cheek: '#D3D3D3' }\n      case 'concerned':\n        return { skin: '#FAEBD7', cheek: '#DDA0DD' }\n      case 'empathetic':\n        return { skin: '#FFF8DC', cheek: '#F0E68C' }\n      default:\n        return { skin: '#FDBCB4', cheek: '#FFB6C1' }\n    }\n  }\n\n  const colors = getEmotionColors()\n\n  return (\n    <group ref={headRef} position={[0, 0, 0]}>\n      {/* Head */}\n      <Sphere args={[1.2, 32, 32]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={colors.skin} />\n      </Sphere>\n\n      {/* Eyes */}\n      <Sphere ref={leftEyeRef} args={[0.15, 16, 16]} position={[-0.3, 0.2, 0.8]}>\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Sphere>\n      <Sphere ref={rightEyeRef} args={[0.15, 16, 16]} position={[0.3, 0.2, 0.8]}>\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Sphere>\n\n      {/* Pupils */}\n      <Sphere args={[0.08, 16, 16]} position={[-0.3, 0.2, 0.9]}>\n        <meshStandardMaterial color=\"#2C3E50\" />\n      </Sphere>\n      <Sphere args={[0.08, 16, 16]} position={[0.3, 0.2, 0.9]}>\n        <meshStandardMaterial color=\"#2C3E50\" />\n      </Sphere>\n\n      {/* Eyebrows */}\n      <Box \n        ref={eyebrowLeftRef} \n        args={[0.3, 0.05, 0.1]} \n        position={[-0.3, 0.4, 0.8]}\n        rotation={[0, 0, emotion === 'concerned' ? 0.3 : 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Box>\n      <Box \n        ref={eyebrowRightRef} \n        args={[0.3, 0.05, 0.1]} \n        position={[0.3, 0.4, 0.8]}\n        rotation={[0, 0, emotion === 'concerned' ? -0.3 : 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Box>\n\n      {/* Nose */}\n      <Sphere args={[0.08, 16, 16]} position={[0, 0, 0.9]}>\n        <meshStandardMaterial color={colors.skin} />\n      </Sphere>\n\n      {/* Mouth */}\n      <Box \n        ref={mouthRef} \n        args={[0.3, 0.1, 0.05]} \n        position={[0, -0.3, 0.8]}\n        rotation={[0, 0, emotion === 'happy' ? 0.3 : emotion === 'sad' ? -0.3 : 0]}\n      >\n        <meshStandardMaterial color=\"#CD5C5C\" />\n      </Box>\n\n      {/* Cheeks (for emotion) */}\n      {(emotion === 'happy' || emotion === 'empathetic') && (\n        <>\n          <Sphere args={[0.1, 16, 16]} position={[-0.6, -0.1, 0.6]}>\n            <meshStandardMaterial color={colors.cheek} transparent opacity={0.6} />\n          </Sphere>\n          <Sphere args={[0.1, 16, 16]} position={[0.6, -0.1, 0.6]}>\n            <meshStandardMaterial color={colors.cheek} transparent opacity={0.6} />\n          </Sphere>\n        </>\n      )}\n\n      {/* Hair */}\n      <Sphere args={[1.3, 32, 32]} position={[0, 0.3, -0.2]}>\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Sphere>\n\n      {/* Neck */}\n      <Box args={[0.4, 0.8, 0.4]} position={[0, -1.2, 0]}>\n        <meshStandardMaterial color={colors.skin} />\n      </Box>\n\n      {/* Shoulders */}\n      <Box args={[2, 0.3, 0.8]} position={[0, -1.8, 0]}>\n        <meshStandardMaterial color=\"#4A90E2\" />\n      </Box>\n    </group>\n  )\n}\n\n// Background Environment\nfunction Environment() {\n  return (\n    <>\n      {/* Lighting */}\n      <ambientLight intensity={0.6} />\n      <directionalLight position={[10, 10, 5]} intensity={1} />\n      <pointLight position={[-10, -10, -5]} intensity={0.3} />\n      \n      {/* Background */}\n      <Sphere args={[50]} position={[0, 0, -20]}>\n        <meshBasicMaterial color=\"#E8F4FD\" side={THREE.BackSide} />\n      </Sphere>\n    </>\n  )\n}\n\n// Main Avatar Component\nexport default function HumanAvatar({ isListening, isSpeaking, emotion, message }: AvatarProps) {\n  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 0, 5])\n\n  useEffect(() => {\n    // Adjust camera based on interaction state\n    if (isListening) {\n      setCameraPosition([0, 0, 4]) // Zoom in when listening\n    } else if (isSpeaking) {\n      setCameraPosition([0, 0, 4.5]) // Slight zoom when speaking\n    } else {\n      setCameraPosition([0, 0, 5]) // Default position\n    }\n  }, [isListening, isSpeaking])\n\n  return (\n    <div className=\"w-full h-96 relative\">\n      <Canvas camera={{ position: cameraPosition, fov: 50 }}>\n        <Environment />\n        <HumanHead \n          isListening={isListening} \n          isSpeaking={isSpeaking} \n          emotion={emotion} \n        />\n        <OrbitControls \n          enableZoom={false} \n          enablePan={false}\n          maxPolarAngle={Math.PI / 2}\n          minPolarAngle={Math.PI / 3}\n        />\n      </Canvas>\n      \n      {/* Status Indicators */}\n      <div className=\"absolute top-4 left-4 space-y-2\">\n        {isListening && (\n          <div className=\"flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n            <span>Listening...</span>\n          </div>\n        )}\n        {isSpeaking && (\n          <div className=\"flex items-center space-x-2 bg-blue-500 text-white px-3 py-1 rounded-full text-sm\">\n            <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n            <span>Speaking...</span>\n          </div>\n        )}\n      </div>\n\n      {/* Message Display */}\n      {message && (\n        <div className=\"absolute bottom-4 left-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg\">\n          <p className=\"text-sm text-gray-800\">{message}</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAcA,iCAAiC;AACjC,SAAS,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAgC;IACnF,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACpC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACpC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAE3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,iBAAiB;IACjB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO;QACf,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,sBAAsB;QACtB,MAAM,iBAAiB,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;QACnE,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;QAEhC,qBAAqB;QACrB,cAAc,CAAA,OAAQ,OAAO;QAC7B,IAAI,aAAa,GAAG;YAClB,cAAc;YACd,kBAAkB;YAClB,IAAI,WAAW,OAAO,IAAI,YAAY,OAAO,EAAE;gBAC7C,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG;gBACpE,WAAW,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;gBAC7B,YAAY,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;YAChC;QACF;QAEA,qBAAqB;QACrB,IAAI,cAAc,SAAS,OAAO,EAAE;YAClC,MAAM,gBAAgB,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK,MAAM;YACpE,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG;YAC3B,kBAAkB;QACpB,OAAO,IAAI,SAAS,OAAO,EAAE;YAC3B,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,QAAQ;QACzF;QAEA,yCAAyC;QACzC,IAAI,eAAe,QAAQ,OAAO,EAAE;YAClC,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YACvD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG;QAC/B,OAAO,IAAI,QAAQ,OAAO,EAAE;YAC1B,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,+IAAA,CAAA,YAAe,CAAC,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,QAAQ;QAC3F;IACF;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;YAC7C;gBACE,OAAO;oBAAE,MAAM;oBAAW,OAAO;gBAAU;QAC/C;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QAAM,KAAK;QAAS,UAAU;YAAC;YAAG;YAAG;SAAE;;0BAEtC,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;oBAAK;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC9C,cAAA,8OAAC;oBAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;0BAI1C,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAY,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAK;iBAAI;0BACvE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAE9B,8OAAC,0JAAA,CAAA,SAAM;gBAAC,KAAK;gBAAa,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAK;oBAAK;iBAAI;0BACvE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAK;iBAAI;0BACtD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAE9B,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAK;oBAAK;iBAAI;0BACrD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,UAAU;oBAAC,CAAC;oBAAK;oBAAK;iBAAI;gBAC1B,UAAU;oBAAC;oBAAG;oBAAG,YAAY,cAAc,MAAM;iBAAE;0BAEnD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAE9B,8OAAC,0JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBACtB,UAAU;oBAAC;oBAAK;oBAAK;iBAAI;gBACzB,UAAU;oBAAC;oBAAG;oBAAG,YAAY,cAAc,CAAC,MAAM;iBAAE;0BAEpD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;oBAAM;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;0BACjD,cAAA,8OAAC;oBAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;0BAI1C,8OAAC,0JAAA,CAAA,MAAG;gBACF,KAAK;gBACL,MAAM;oBAAC;oBAAK;oBAAK;iBAAK;gBACtB,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAI;gBACxB,UAAU;oBAAC;oBAAG;oBAAG,YAAY,UAAU,MAAM,YAAY,QAAQ,CAAC,MAAM;iBAAE;0BAE1E,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;YAI7B,CAAC,YAAY,WAAW,YAAY,YAAY,mBAC/C;;kCACE,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAK;4BAAI;yBAAG;wBAAE,UAAU;4BAAC,CAAC;4BAAK,CAAC;4BAAK;yBAAI;kCACtD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,KAAK;4BAAE,WAAW;4BAAC,SAAS;;;;;;;;;;;kCAElE,8OAAC,0JAAA,CAAA,SAAM;wBAAC,MAAM;4BAAC;4BAAK;4BAAI;yBAAG;wBAAE,UAAU;4BAAC;4BAAK,CAAC;4BAAK;yBAAI;kCACrD,cAAA,8OAAC;4BAAqB,OAAO,OAAO,KAAK;4BAAE,WAAW;4BAAC,SAAS;;;;;;;;;;;;;0BAMtE,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;oBAAK;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAK,CAAC;iBAAI;0BACnD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAChD,cAAA,8OAAC;oBAAqB,OAAO,OAAO,IAAI;;;;;;;;;;;0BAI1C,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAC9C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;;;;;;;AAIpC;AAEA,yBAAyB;AACzB,SAAS;IACP,qBACE;;0BAEE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBAAiB,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBAAE,WAAW;;;;;;0BACpD,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAE;gBAAE,WAAW;;;;;;0BAGjD,8OAAC,0JAAA,CAAA,SAAM;gBAAC,MAAM;oBAAC;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BACvC,cAAA,8OAAC;oBAAkB,OAAM;oBAAU,MAAM,+IAAA,CAAA,WAAc;;;;;;;;;;;;;AAI/D;AAGe,SAAS,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAe;IAC5F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAAC;QAAG;QAAG;KAAE;IAExF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,IAAI,aAAa;YACf,kBAAkB;gBAAC;gBAAG;gBAAG;aAAE,GAAE,yBAAyB;QACxD,OAAO,IAAI,YAAY;YACrB,kBAAkB;gBAAC;gBAAG;gBAAG;aAAI,GAAE,4BAA4B;QAC7D,OAAO;YACL,kBAAkB;gBAAC;gBAAG;gBAAG;aAAE,GAAE,mBAAmB;QAClD;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBAAC,QAAQ;oBAAE,UAAU;oBAAgB,KAAK;gBAAG;;kCAClD,8OAAC;;;;;kCACD,8OAAC;wBACC,aAAa;wBACb,YAAY;wBACZ,SAAS;;;;;;kCAEX,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,YAAY;wBACZ,WAAW;wBACX,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe,KAAK,EAAE,GAAG;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;oBAGT,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/avatar/FacialAnimationEngine.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\n\ninterface FacialAnimationProps {\n  audioData?: Float32Array\n  text?: string\n  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n  isActive: boolean\n  onAnimationUpdate: (animationData: FacialAnimationData) => void\n}\n\nexport interface FacialAnimationData {\n  mouthOpenness: number\n  eyeBlinkLeft: number\n  eyeBlinkRight: number\n  eyebrowPosition: number\n  headRotation: { x: number; y: number; z: number }\n  emotion: string\n  intensity: number\n}\n\n// Phoneme to mouth shape mapping for lip-sync\nconst PHONEME_MOUTH_SHAPES = {\n  'A': { openness: 0.8, width: 0.6 },\n  'E': { openness: 0.6, width: 0.8 },\n  'I': { openness: 0.3, width: 0.9 },\n  'O': { openness: 0.9, width: 0.4 },\n  'U': { openness: 0.7, width: 0.3 },\n  'M': { openness: 0.1, width: 0.5 },\n  'B': { openness: 0.1, width: 0.5 },\n  'P': { openness: 0.1, width: 0.5 },\n  'F': { openness: 0.3, width: 0.7 },\n  'V': { openness: 0.3, width: 0.7 },\n  'TH': { openness: 0.2, width: 0.8 },\n  'S': { openness: 0.2, width: 0.6 },\n  'SH': { openness: 0.4, width: 0.5 },\n  'R': { openness: 0.4, width: 0.6 },\n  'L': { openness: 0.3, width: 0.7 },\n  'silence': { openness: 0.1, width: 0.5 }\n}\n\n// Emotion-based facial expressions\nconst EMOTION_EXPRESSIONS = {\n  neutral: {\n    eyebrowHeight: 0,\n    mouthCurve: 0,\n    eyeOpenness: 1,\n    cheekRaise: 0\n  },\n  happy: {\n    eyebrowHeight: 0.2,\n    mouthCurve: 0.6,\n    eyeOpenness: 0.8,\n    cheekRaise: 0.4\n  },\n  sad: {\n    eyebrowHeight: -0.3,\n    mouthCurve: -0.4,\n    eyeOpenness: 0.6,\n    cheekRaise: 0\n  },\n  concerned: {\n    eyebrowHeight: -0.2,\n    mouthCurve: -0.2,\n    eyeOpenness: 1.2,\n    cheekRaise: 0\n  },\n  empathetic: {\n    eyebrowHeight: 0.1,\n    mouthCurve: 0.2,\n    eyeOpenness: 0.9,\n    cheekRaise: 0.2\n  }\n}\n\nexport default function FacialAnimationEngine({\n  audioData,\n  text,\n  emotion,\n  isActive,\n  onAnimationUpdate\n}: FacialAnimationProps) {\n  const [currentPhoneme, setCurrentPhoneme] = useState('silence')\n  const [blinkTimer, setBlinkTimer] = useState(0)\n  const [emotionIntensity, setEmotionIntensity] = useState(1)\n  const animationFrameRef = useRef<number>()\n  const audioContextRef = useRef<AudioContext>()\n  const analyserRef = useRef<AnalyserNode>()\n\n  // Simple phoneme detection from text\n  const detectPhonemeFromText = (text: string, position: number): string => {\n    if (!text || position >= text.length) return 'silence'\n    \n    const char = text[position].toLowerCase()\n    \n    // Simple mapping - in a real implementation, you'd use a proper phoneme library\n    const phonemeMap: { [key: string]: string } = {\n      'a': 'A', 'e': 'E', 'i': 'I', 'o': 'O', 'u': 'U',\n      'm': 'M', 'b': 'B', 'p': 'P',\n      'f': 'F', 'v': 'V',\n      's': 'S', 'z': 'S',\n      'r': 'R', 'l': 'L',\n      ' ': 'silence'\n    }\n    \n    return phonemeMap[char] || 'silence'\n  }\n\n  // Audio analysis for lip-sync\n  const analyzeAudio = (audioData: Float32Array): number => {\n    if (!audioData) return 0\n    \n    let sum = 0\n    for (let i = 0; i < audioData.length; i++) {\n      sum += Math.abs(audioData[i])\n    }\n    return sum / audioData.length\n  }\n\n  // Generate realistic blinking\n  const generateBlink = (deltaTime: number): { left: number; right: number } => {\n    setBlinkTimer(prev => prev + deltaTime)\n    \n    // Random blinking every 2-4 seconds\n    const blinkInterval = 2000 + Math.random() * 2000\n    \n    if (blinkTimer > blinkInterval) {\n      setBlinkTimer(0)\n      // Quick blink animation\n      const blinkPhase = (Date.now() % 200) / 200\n      const blinkValue = Math.sin(blinkPhase * Math.PI)\n      return { left: blinkValue, right: blinkValue }\n    }\n    \n    return { left: 1, right: 1 }\n  }\n\n  // Main animation loop\n  useEffect(() => {\n    if (!isActive) return\n\n    let textPosition = 0\n    let lastTime = Date.now()\n    \n    const animate = () => {\n      const currentTime = Date.now()\n      const deltaTime = currentTime - lastTime\n      lastTime = currentTime\n\n      // Text-based lip-sync animation\n      if (text && textPosition < text.length) {\n        const phoneme = detectPhonemeFromText(text, Math.floor(textPosition))\n        setCurrentPhoneme(phoneme)\n        \n        // Advance through text at speaking pace (adjust speed as needed)\n        textPosition += deltaTime * 0.01 // Adjust this multiplier for speaking speed\n      } else {\n        setCurrentPhoneme('silence')\n      }\n\n      // Audio-based animation (if available)\n      let audioIntensity = 0\n      if (audioData) {\n        audioIntensity = analyzeAudio(audioData)\n      }\n\n      // Generate facial animation data\n      const mouthShape = PHONEME_MOUTH_SHAPES[currentPhoneme as keyof typeof PHONEME_MOUTH_SHAPES] || PHONEME_MOUTH_SHAPES.silence\n      const expression = EMOTION_EXPRESSIONS[emotion]\n      const blink = generateBlink(deltaTime)\n\n      const animationData: FacialAnimationData = {\n        mouthOpenness: mouthShape.openness + audioIntensity * 0.3,\n        eyeBlinkLeft: blink.left,\n        eyeBlinkRight: blink.right,\n        eyebrowPosition: expression.eyebrowHeight,\n        headRotation: {\n          x: Math.sin(currentTime * 0.001) * 0.05, // Subtle head movement\n          y: Math.sin(currentTime * 0.0008) * 0.03,\n          z: Math.sin(currentTime * 0.0012) * 0.02\n        },\n        emotion,\n        intensity: emotionIntensity\n      }\n\n      onAnimationUpdate(animationData)\n\n      if (isActive) {\n        animationFrameRef.current = requestAnimationFrame(animate)\n      }\n    }\n\n    animate()\n\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current)\n      }\n    }\n  }, [isActive, text, emotion, audioData, onAnimationUpdate])\n\n  // Setup audio analysis\n  useEffect(() => {\n    if (typeof window !== 'undefined' && window.AudioContext) {\n      audioContextRef.current = new AudioContext()\n      analyserRef.current = audioContextRef.current.createAnalyser()\n      analyserRef.current.fftSize = 256\n    }\n\n    return () => {\n      if (audioContextRef.current) {\n        audioContextRef.current.close()\n      }\n    }\n  }, [])\n\n  return null // This is a logic-only component\n}\n\n// Utility function to convert text to estimated phonemes\nexport function textToPhonemes(text: string): Array<{ phoneme: string; duration: number }> {\n  const words = text.split(' ')\n  const phonemes: Array<{ phoneme: string; duration: number }> = []\n  \n  words.forEach((word, wordIndex) => {\n    for (let i = 0; i < word.length; i++) {\n      const char = word[i].toLowerCase()\n      let phoneme = 'silence'\n      \n      // Simple character to phoneme mapping\n      if ('aeiou'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('mbp'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('fv'.includes(char)) {\n        phoneme = char.toUpperCase()\n      } else if ('sz'.includes(char)) {\n        phoneme = 'S'\n      } else if ('rl'.includes(char)) {\n        phoneme = char.toUpperCase()\n      }\n      \n      phonemes.push({\n        phoneme,\n        duration: 100 + Math.random() * 100 // Random duration between 100-200ms\n      })\n    }\n    \n    // Add pause between words\n    if (wordIndex < words.length - 1) {\n      phonemes.push({ phoneme: 'silence', duration: 200 })\n    }\n  })\n  \n  return phonemes\n}\n\n// Advanced emotion detection from text\nexport function detectEmotionFromText(text: string): 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic' {\n  const lowerText = text.toLowerCase()\n  \n  // Happy indicators\n  if (lowerText.includes('happy') || lowerText.includes('joy') || lowerText.includes('great') || \n      lowerText.includes('wonderful') || lowerText.includes('excited') || lowerText.includes('😊') ||\n      lowerText.includes('good') || lowerText.includes('amazing')) {\n    return 'happy'\n  }\n  \n  // Sad indicators\n  if (lowerText.includes('sad') || lowerText.includes('depressed') || lowerText.includes('cry') ||\n      lowerText.includes('hurt') || lowerText.includes('pain') || lowerText.includes('😢') ||\n      lowerText.includes('terrible') || lowerText.includes('awful')) {\n    return 'sad'\n  }\n  \n  // Concerned indicators\n  if (lowerText.includes('worried') || lowerText.includes('anxious') || lowerText.includes('scared') ||\n      lowerText.includes('afraid') || lowerText.includes('nervous') || lowerText.includes('stress') ||\n      lowerText.includes('concerned') || lowerText.includes('help')) {\n    return 'concerned'\n  }\n  \n  // Empathetic indicators\n  if (lowerText.includes('understand') || lowerText.includes('support') || lowerText.includes('care') ||\n      lowerText.includes('here for you') || lowerText.includes('listen') || lowerText.includes('comfort')) {\n    return 'empathetic'\n  }\n  \n  return 'neutral'\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAsBA,8CAA8C;AAC9C,MAAM,uBAAuB;IAC3B,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,MAAM;QAAE,UAAU;QAAK,OAAO;IAAI;IAClC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,MAAM;QAAE,UAAU;QAAK,OAAO;IAAI;IAClC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,KAAK;QAAE,UAAU;QAAK,OAAO;IAAI;IACjC,WAAW;QAAE,UAAU;QAAK,OAAO;IAAI;AACzC;AAEA,mCAAmC;AACnC,MAAM,sBAAsB;IAC1B,SAAS;QACP,eAAe;QACf,YAAY;QACZ,aAAa;QACb,YAAY;IACd;IACA,OAAO;QACL,eAAe;QACf,YAAY;QACZ,aAAa;QACb,YAAY;IACd;IACA,KAAK;QACH,eAAe,CAAC;QAChB,YAAY,CAAC;QACb,aAAa;QACb,YAAY;IACd;IACA,WAAW;QACT,eAAe,CAAC;QAChB,YAAY,CAAC;QACb,aAAa;QACb,YAAY;IACd;IACA,YAAY;QACV,eAAe;QACf,YAAY;QACZ,aAAa;QACb,YAAY;IACd;AACF;AAEe,SAAS,sBAAsB,EAC5C,SAAS,EACT,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,iBAAiB,EACI;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEzB,qCAAqC;IACrC,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,QAAQ,YAAY,KAAK,MAAM,EAAE,OAAO;QAE7C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;QAEvC,gFAAgF;QAChF,MAAM,aAAwC;YAC5C,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAAK,KAAK;YAC7C,KAAK;YAAK,KAAK;YAAK,KAAK;YACzB,KAAK;YAAK,KAAK;YACf,KAAK;YAAK,KAAK;YACf,KAAK;YAAK,KAAK;YACf,KAAK;QACP;QAEA,OAAO,UAAU,CAAC,KAAK,IAAI;IAC7B;IAEA,8BAA8B;IAC9B,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,OAAO;QAEvB,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,OAAO,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE;QAC9B;QACA,OAAO,MAAM,UAAU,MAAM;IAC/B;IAEA,8BAA8B;IAC9B,MAAM,gBAAgB,CAAC;QACrB,cAAc,CAAA,OAAQ,OAAO;QAE7B,oCAAoC;QACpC,MAAM,gBAAgB,OAAO,KAAK,MAAM,KAAK;QAE7C,IAAI,aAAa,eAAe;YAC9B,cAAc;YACd,wBAAwB;YACxB,MAAM,aAAa,AAAC,KAAK,GAAG,KAAK,MAAO;YACxC,MAAM,aAAa,KAAK,GAAG,CAAC,aAAa,KAAK,EAAE;YAChD,OAAO;gBAAE,MAAM;gBAAY,OAAO;YAAW;QAC/C;QAEA,OAAO;YAAE,MAAM;YAAG,OAAO;QAAE;IAC7B;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,IAAI,eAAe;QACnB,IAAI,WAAW,KAAK,GAAG;QAEvB,MAAM,UAAU;YACd,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,YAAY,cAAc;YAChC,WAAW;YAEX,gCAAgC;YAChC,IAAI,QAAQ,eAAe,KAAK,MAAM,EAAE;gBACtC,MAAM,UAAU,sBAAsB,MAAM,KAAK,KAAK,CAAC;gBACvD,kBAAkB;gBAElB,iEAAiE;gBACjE,gBAAgB,YAAY,MAAK,4CAA4C;YAC/E,OAAO;gBACL,kBAAkB;YACpB;YAEA,uCAAuC;YACvC,IAAI,iBAAiB;YACrB,IAAI,WAAW;gBACb,iBAAiB,aAAa;YAChC;YAEA,iCAAiC;YACjC,MAAM,aAAa,oBAAoB,CAAC,eAAoD,IAAI,qBAAqB,OAAO;YAC5H,MAAM,aAAa,mBAAmB,CAAC,QAAQ;YAC/C,MAAM,QAAQ,cAAc;YAE5B,MAAM,gBAAqC;gBACzC,eAAe,WAAW,QAAQ,GAAG,iBAAiB;gBACtD,cAAc,MAAM,IAAI;gBACxB,eAAe,MAAM,KAAK;gBAC1B,iBAAiB,WAAW,aAAa;gBACzC,cAAc;oBACZ,GAAG,KAAK,GAAG,CAAC,cAAc,SAAS;oBACnC,GAAG,KAAK,GAAG,CAAC,cAAc,UAAU;oBACpC,GAAG,KAAK,GAAG,CAAC,cAAc,UAAU;gBACtC;gBACA;gBACA,WAAW;YACb;YAEA,kBAAkB;YAElB,IAAI,UAAU;gBACZ,kBAAkB,OAAO,GAAG,sBAAsB;YACpD;QACF;QAEA;QAEA,OAAO;YACL,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;QACF;IACF,GAAG;QAAC;QAAU;QAAM;QAAS;QAAW;KAAkB;IAE1D,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;QAMA,OAAO;YACL,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,KAAK;YAC/B;QACF;IACF,GAAG,EAAE;IAEL,OAAO,KAAK,iCAAiC;;AAC/C;AAGO,SAAS,eAAe,IAAY;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,WAAyD,EAAE;IAEjE,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW;YAChC,IAAI,UAAU;YAEd,sCAAsC;YACtC,IAAI,QAAQ,QAAQ,CAAC,OAAO;gBAC1B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,MAAM,QAAQ,CAAC,OAAO;gBAC/B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU,KAAK,WAAW;YAC5B,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU;YACZ,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO;gBAC9B,UAAU,KAAK,WAAW;YAC5B;YAEA,SAAS,IAAI,CAAC;gBACZ;gBACA,UAAU,MAAM,KAAK,MAAM,KAAK,IAAI,oCAAoC;YAC1E;QACF;QAEA,0BAA0B;QAC1B,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;YAChC,SAAS,IAAI,CAAC;gBAAE,SAAS;gBAAW,UAAU;YAAI;QACpD;IACF;IAEA,OAAO;AACT;AAGO,SAAS,sBAAsB,IAAY;IAChD,MAAM,YAAY,KAAK,WAAW;IAElC,mBAAmB;IACnB,IAAI,UAAU,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,YAC/E,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,SACvF,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,YAAY;QAC/D,OAAO;IACT;IAEA,iBAAiB;IACjB,IAAI,UAAU,QAAQ,CAAC,UAAU,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,UACnF,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,SAC/E,UAAU,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,UAAU;QACjE,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,aACrF,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,aACpF,UAAU,QAAQ,CAAC,gBAAgB,UAAU,QAAQ,CAAC,SAAS;QACjE,OAAO;IACT;IAEA,wBAAwB;IACxB,IAAI,UAAU,QAAQ,CAAC,iBAAiB,UAAU,QAAQ,CAAC,cAAc,UAAU,QAAQ,CAAC,WACxF,UAAU,QAAQ,CAAC,mBAAmB,UAAU,QAAQ,CAAC,aAAa,UAAU,QAAQ,CAAC,YAAY;QACvG,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/avatar-chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, MicrophoneIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline'\nimport { generateId } from '@/lib/utils'\nimport VoiceChat from '@/components/chatbot/VoiceChat'\nimport HumanAvatar from '@/components/avatar/HumanAvatar'\nimport FacialAnimationEngine, { FacialAnimationData, detectEmotionFromText } from '@/components/avatar/FacialAnimationEngine'\n\ninterface Message {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n  isTyping?: boolean\n  typedContent?: string\n  safetyAlert?: {\n    riskLevel: string\n    keywords: string[]\n  }\n  language?: 'en' | 'ta'\n  emotion?: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'\n}\n\nexport default function AvatarChatPage() {\n  const [messages, setMessages] = useState<Message[]>([])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [language, setLanguage] = useState<'en' | 'ta'>('en')\n  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)\n  \n  // Avatar states\n  const [avatarEmotion, setAvatarEmotion] = useState<'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'>('neutral')\n  const [avatarIsSpeaking, setAvatarIsSpeaking] = useState(false)\n  const [avatarIsListening, setAvatarIsListening] = useState(false)\n  const [currentSpeechText, setCurrentSpeechText] = useState('')\n  const [facialAnimationData, setFacialAnimationData] = useState<FacialAnimationData | null>(null)\n  \n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const typingTimeoutRef = useRef<NodeJS.Timeout>()\n  const speechTimeoutRef = useRef<NodeJS.Timeout>()\n\n  // Initialize with welcome message\n  useEffect(() => {\n    const welcomeMessage = {\n      id: generateId(),\n      role: 'assistant' as const,\n      content: language === 'ta' \n        ? 'வணக்கம்! நான் உங்கள் AI மனநல ஆலோசகர். நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்?'\n        : 'Hello! I\\'m your AI mental health counselor. I\\'m here to provide emotional support and guidance. How are you feeling today?',\n      timestamp: new Date().toISOString(),\n      language,\n      emotion: 'empathetic' as const\n    }\n    \n    setMessages([welcomeMessage])\n    setAvatarEmotion('empathetic')\n    \n    // Auto-speak welcome message\n    setTimeout(() => {\n      if (speakResponse) {\n        speakResponse(welcomeMessage.content, language)\n        setAvatarIsSpeaking(true)\n        setCurrentSpeechText(welcomeMessage.content)\n        \n        // Stop speaking after estimated duration\n        const estimatedDuration = welcomeMessage.content.length * 80 // ~80ms per character\n        speechTimeoutRef.current = setTimeout(() => {\n          setAvatarIsSpeaking(false)\n          setCurrentSpeechText('')\n        }, estimatedDuration)\n      }\n    }, 1000)\n  }, [language, speakResponse])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  // Handle facial animation updates\n  const handleFacialAnimationUpdate = useCallback((animationData: FacialAnimationData) => {\n    setFacialAnimationData(animationData)\n  }, [])\n\n  // Typing effect for assistant messages\n  const typeMessage = useCallback((message: Message) => {\n    const words = message.content.split(' ')\n    let currentIndex = 0\n    \n    const typeNextWord = () => {\n      if (currentIndex < words.length) {\n        const typedContent = words.slice(0, currentIndex + 1).join(' ')\n        \n        setMessages(prev => prev.map(msg => \n          msg.id === message.id \n            ? { ...msg, typedContent, isTyping: true }\n            : msg\n        ))\n        \n        currentIndex++\n        typingTimeoutRef.current = setTimeout(typeNextWord, 150 + Math.random() * 100)\n      } else {\n        setMessages(prev => prev.map(msg => \n          msg.id === message.id \n            ? { ...msg, isTyping: false, typedContent: message.content }\n            : msg\n        ))\n        \n        // Start avatar speaking\n        if (message.role === 'assistant') {\n          setAvatarIsSpeaking(true)\n          setCurrentSpeechText(message.content)\n          setAvatarEmotion(message.emotion || 'neutral')\n          \n          // Auto-speak the response\n          if (speakResponse) {\n            setTimeout(() => {\n              speakResponse(message.content, message.language || language)\n            }, 500)\n          }\n          \n          // Stop speaking after estimated duration\n          const estimatedDuration = message.content.length * 80\n          speechTimeoutRef.current = setTimeout(() => {\n            setAvatarIsSpeaking(false)\n            setCurrentSpeechText('')\n            setAvatarEmotion('neutral')\n          }, estimatedDuration)\n        }\n      }\n    }\n    \n    typeNextWord()\n  }, [speakResponse, language])\n\n  // Handle voice transcript\n  const handleVoiceTranscript = useCallback((transcript: string) => {\n    setInputMessage(transcript)\n    setAvatarIsListening(false)\n    // Auto-send voice messages\n    setTimeout(() => {\n      sendMessage(transcript)\n    }, 500)\n  }, [])\n\n  // Handle voice response setup\n  const handleSpeakResponse = useCallback((speakFn: any) => {\n    setSpeakResponse(() => speakFn)\n  }, [])\n\n  // Update avatar listening state\n  useEffect(() => {\n    setAvatarIsListening(isListening)\n  }, [isListening])\n\n  const sendMessage = async (messageText?: string) => {\n    const textToSend = messageText || inputMessage.trim()\n    if (!textToSend || isLoading) return\n\n    const userMessage: Message = {\n      id: generateId(),\n      role: 'user',\n      content: textToSend,\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    // Show typing indicator\n    const typingMessage: Message = {\n      id: generateId(),\n      role: 'assistant',\n      content: language === 'ta' ? 'சிந்திக்கிறேன்...' : 'Thinking...',\n      timestamp: new Date().toISOString(),\n      isTyping: true,\n      language,\n      emotion: 'neutral'\n    }\n    \n    setMessages(prev => [...prev, typingMessage])\n    setAvatarEmotion('concerned') // Show thinking expression\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: textToSend,\n          sessionId: generateId(),\n          userId: 'anonymous',\n          language\n        }),\n      })\n\n      const data = await response.json()\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      if (data.success) {\n        let responseContent = data.data.content\n        \n        // Detect emotion from response\n        const detectedEmotion = detectEmotionFromText(responseContent)\n        \n        // Basic Tamil translation for common responses\n        if (language === 'ta' && !responseContent.includes('தமிழ்')) {\n          const translations: { [key: string]: string } = {\n            'I understand': 'நான் புரிந்துகொள்கிறேன்',\n            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',\n            'I\\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',\n            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',\n            'That sounds difficult': 'அது கடினமாக இருக்கும்',\n            'You\\'re not alone': 'நீங்கள் தனியாக இல்லை',\n            'I\\'m sorry to hear': 'கேட்டு வருந்துகிறேன்',\n            'That\\'s wonderful': 'அது அருமை',\n            'How can I help': 'நான் எப்படி உதவ முடியும்'\n          }\n          \n          Object.entries(translations).forEach(([en, ta]) => {\n            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)\n          })\n        }\n\n        const assistantMessage: Message = {\n          id: generateId(),\n          role: 'assistant',\n          content: responseContent,\n          timestamp: data.data.timestamp,\n          safetyAlert: data.data.safetyAlert,\n          language,\n          emotion: detectedEmotion,\n          isTyping: true,\n          typedContent: ''\n        }\n        \n        setMessages(prev => [...prev, assistantMessage])\n        \n        // Start typing effect\n        setTimeout(() => {\n          typeMessage(assistantMessage)\n        }, 500)\n        \n      } else {\n        throw new Error(data.error || 'Failed to get response')\n      }\n    } catch (error) {\n      console.error('Error sending message:', error)\n      \n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n      \n      const errorMessage: Message = {\n        id: generateId(),\n        role: 'assistant',\n        content: language === 'ta' \n          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும்.'\n          : 'I apologize, but I\\'m having trouble responding right now. Please try again.',\n        timestamp: new Date().toISOString(),\n        language,\n        emotion: 'concerned'\n      }\n      setMessages(prev => [...prev, errorMessage])\n      typeMessage(errorMessage)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto p-4 h-[calc(100vh-100px)]\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 h-full\">\n\n        {/* Avatar Section */}\n        <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4\">\n            <h2 className=\"text-xl font-semibold\">\n              {language === 'ta' ? 'AI ஆலோசகர்' : 'AI Counselor'}\n            </h2>\n            <p className=\"text-purple-100 text-sm\">\n              {language === 'ta'\n                ? 'உங்கள் மனநல ஆதரவு நண்பர்'\n                : 'Your Mental Health Support Companion'\n              }\n            </p>\n          </div>\n\n          <HumanAvatar\n            isListening={avatarIsListening}\n            isSpeaking={avatarIsSpeaking}\n            emotion={avatarEmotion}\n            message={avatarIsSpeaking ? currentSpeechText : undefined}\n          />\n\n          {/* Facial Animation Engine */}\n          <FacialAnimationEngine\n            text={avatarIsSpeaking ? currentSpeechText : undefined}\n            emotion={avatarEmotion}\n            isActive={avatarIsSpeaking}\n            onAnimationUpdate={handleFacialAnimationUpdate}\n          />\n        </div>\n\n        {/* Chat Section */}\n        <div className=\"bg-white rounded-lg shadow-lg flex flex-col\">\n          {/* Chat Header */}\n          <div className=\"bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-xl font-semibold\">\n                  {language === 'ta' ? 'உரையாடல்' : 'Conversation'}\n                </h1>\n                <p className=\"text-pink-100 text-sm\">\n                  {language === 'ta'\n                    ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு'\n                    : 'Safe, confidential emotional support'\n                  }\n                </p>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm\">\n                  {language === 'ta' ? 'ஆன்லைன்' : 'Online'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Voice Chat Component */}\n          <VoiceChat\n            onTranscript={handleVoiceTranscript}\n            onSpeakResponse={handleSpeakResponse}\n            isListening={isListening}\n            setIsListening={setIsListening}\n            language={language}\n            setLanguage={setLanguage}\n          />\n\n          {/* Emergency Notice */}\n          <div className=\"bg-red-50 border-l-4 border-red-400 p-3\">\n            <div className=\"flex\">\n              <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-700\">\n                  <strong>{language === 'ta' ? 'அவசரநிலை:' : 'Emergency:'}</strong>{' '}\n                  {language === 'ta'\n                    ? 'உடனடி ஆபத்தில் இருந்தால், 911 அழைக்கவும். நெருக்கடி ஆதரவுக்கு 988 அழைக்கவும்.'\n                    : 'If in immediate danger, call 911. For crisis support, call 988.'\n                  }\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Simple Chat Display for now */}\n          <div className=\"flex-1 p-4 bg-gray-50\">\n            <div className=\"text-center text-gray-600\">\n              <p className=\"text-lg font-semibold mb-2\">\n                {language === 'ta' ? '3D அவதார் சாட்' : '3D Avatar Chat'}\n              </p>\n              <p className=\"text-sm\">\n                {language === 'ta'\n                  ? 'இடதுபுறத்தில் உள்ள 3D அவதாருடன் பேசுங்கள்!'\n                  : 'Interact with the 3D avatar on the left!'\n                }\n              </p>\n              <div className=\"mt-4 space-y-2\">\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'வணக்கம்!' : 'Hello!')}\n                  className=\"block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors\"\n                >\n                  {language === 'ta' ? '👋 வணக்கம் சொல்லுங்கள்' : '👋 Say Hello'}\n                </button>\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious')}\n                  className=\"block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\"\n                >\n                  {language === 'ta' ? '😰 கவலையை பகிர்ந்து கொள்ளுங்கள்' : '😰 Share Anxiety'}\n                </button>\n                <button\n                  onClick={() => sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help')}\n                  className=\"block w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors\"\n                >\n                  {language === 'ta' ? '🆘 உதவி கேளுங்கள்' : '🆘 Ask for Help'}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAEvG,gBAAgB;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4D;IAC7G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IAE3F,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC9B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE9B,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAClB,2JACA;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC;YACA,SAAS;QACX;QAEA,YAAY;YAAC;SAAe;QAC5B,iBAAiB;QAEjB,6BAA6B;QAC7B,WAAW;YACT,IAAI,eAAe;gBACjB,cAAc,eAAe,OAAO,EAAE;gBACtC,oBAAoB;gBACpB,qBAAqB,eAAe,OAAO;gBAE3C,yCAAyC;gBACzC,MAAM,oBAAoB,eAAe,OAAO,CAAC,MAAM,GAAG,GAAG,sBAAsB;;gBACnF,iBAAiB,OAAO,GAAG,WAAW;oBACpC,oBAAoB;oBACpB,qBAAqB;gBACvB,GAAG;YACL;QACF,GAAG;IACL,GAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,kCAAkC;IAClC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/C,uBAAuB;IACzB,GAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,CAAC;QACpC,IAAI,eAAe;QAEnB,MAAM,eAAe;YACnB,IAAI,eAAe,MAAM,MAAM,EAAE;gBAC/B,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,eAAe,GAAG,IAAI,CAAC;gBAE3D,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE;4BAAc,UAAU;wBAAK,IACvC;gBAGN;gBACA,iBAAiB,OAAO,GAAG,WAAW,cAAc,MAAM,KAAK,MAAM,KAAK;YAC5E,OAAO;gBACL,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE,UAAU;4BAAO,cAAc,QAAQ,OAAO;wBAAC,IACzD;gBAGN,wBAAwB;gBACxB,IAAI,QAAQ,IAAI,KAAK,aAAa;oBAChC,oBAAoB;oBACpB,qBAAqB,QAAQ,OAAO;oBACpC,iBAAiB,QAAQ,OAAO,IAAI;oBAEpC,0BAA0B;oBAC1B,IAAI,eAAe;wBACjB,WAAW;4BACT,cAAc,QAAQ,OAAO,EAAE,QAAQ,QAAQ,IAAI;wBACrD,GAAG;oBACL;oBAEA,yCAAyC;oBACzC,MAAM,oBAAoB,QAAQ,OAAO,CAAC,MAAM,GAAG;oBACnD,iBAAiB,OAAO,GAAG,WAAW;wBACpC,oBAAoB;wBACpB,qBAAqB;wBACrB,iBAAiB;oBACnB,GAAG;gBACL;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,gBAAgB;QAChB,qBAAqB;QACrB,2BAA2B;QAC3B,WAAW;YACT,YAAY;QACd,GAAG;IACL,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,iBAAiB,IAAM;IACzB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;IACvB,GAAG;QAAC;KAAY;IAEhB,MAAM,cAAc,OAAO;QACzB,MAAM,aAAa,eAAe,aAAa,IAAI;QACnD,IAAI,CAAC,cAAc,WAAW;QAE9B,MAAM,cAAuB;YAC3B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,wBAAwB;QACxB,MAAM,gBAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAAO,sBAAsB;YACnD,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV;YACA,SAAS;QACX;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAC5C,iBAAiB,cAAa,2BAA2B;QAEzD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACpB,QAAQ;oBACR;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBAEvC,+BAA+B;gBAC/B,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE;gBAE9C,+CAA+C;gBAC/C,IAAI,aAAa,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,UAAU;oBAC3D,MAAM,eAA0C;wBAC9C,gBAAgB;wBAChB,uBAAuB;wBACvB,qBAAqB;wBACrB,yBAAyB;wBACzB,yBAAyB;wBACzB,qBAAqB;wBACrB,sBAAsB;wBACtB,qBAAqB;wBACrB,kBAAkB;oBACpB;oBAEA,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;wBAC5C,kBAAkB,gBAAgB,OAAO,CAAC,IAAI,OAAO,IAAI,OAAO;oBAClE;gBACF;gBAEA,MAAM,mBAA4B;oBAChC,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,SAAS;oBACT,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC;oBACA,SAAS;oBACT,UAAU;oBACV,cAAc;gBAChB;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE/C,sBAAsB;gBACtB,WAAW;oBACT,YAAY;gBACd,GAAG;YAEL,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,MAAM,eAAwB;gBAC5B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,MAAM;gBACN,SAAS,aAAa,OAClB,iFACA;gBACJ,WAAW,IAAI,OAAO,WAAW;gBACjC;gBACA,SAAS;YACX;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC3C,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,aAAa,OAAO,eAAe;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,aAAa,OACV,6BACA;;;;;;;;;;;;sCAKR,8OAAC,2IAAA,CAAA,UAAW;4BACV,aAAa;4BACb,YAAY;4BACZ,SAAS;4BACT,SAAS,mBAAmB,oBAAoB;;;;;;sCAIlD,8OAAC,qJAAA,CAAA,UAAqB;4BACpB,MAAM,mBAAmB,oBAAoB;4BAC7C,SAAS;4BACT,UAAU;4BACV,mBAAmB;;;;;;;;;;;;8BAKvB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,aAAa,OAAO,aAAa;;;;;;0DAEpC,8OAAC;gDAAE,WAAU;0DACV,aAAa,OACV,sCACA;;;;;;;;;;;;kDAIR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DACb,aAAa,OAAO,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAOzC,8OAAC,0IAAA,CAAA,UAAS;4BACR,cAAc;4BACd,iBAAiB;4BACjB,aAAa;4BACb,gBAAgB;4BAChB,UAAU;4BACV,aAAa;;;;;;sCAIf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6OAAA,CAAA,0BAAuB;wCAAC,WAAU;;;;;;kDACnC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAQ,aAAa,OAAO,cAAc;;;;;;gDAAuB;gDACjE,aAAa,OACV,kFACA;;;;;;;;;;;;;;;;;;;;;;;sCAQZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,aAAa,OAAO,mBAAmB;;;;;;kDAE1C,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,+CACA;;;;;;kDAGN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,aAAa;gDAC5D,WAAU;0DAET,aAAa,OAAO,2BAA2B;;;;;;0DAElD,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,4BAA4B;gDAC3E,WAAU;0DAET,aAAa,OAAO,oCAAoC;;;;;;0DAE3D,8OAAC;gDACC,SAAS,IAAM,YAAY,aAAa,OAAO,qBAAqB;gDACpE,WAAU;0DAET,aAAa,OAAO,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D", "debugId": null}}]}