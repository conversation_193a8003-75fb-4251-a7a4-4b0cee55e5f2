{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/games/breathing-garden/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { PlayIcon, PauseIcon, ArrowLeftIcon, SpeakerWaveIcon, SpeakerXMarkIcon } from '@heroicons/react/24/outline'\nimport Link from 'next/link'\n\ninterface Flower {\n  id: number\n  x: number\n  y: number\n  size: number\n  color: string\n  bloomed: boolean\n  bloomProgress: number\n}\n\nexport default function BreathingGardenGame() {\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [breathPhase, setBreathPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause'>('inhale')\n  const [cycleCount, setCycleCount] = useState(0)\n  const [flowers, setFlowers] = useState<Flower[]>([])\n  const [score, setScore] = useState(0)\n  const [timeRemaining, setTimeRemaining] = useState(300) // 5 minutes\n  const [soundEnabled, setSoundEnabled] = useState(true)\n  const [breathProgress, setBreathProgress] = useState(0)\n  \n  const canvasRef = useRef<HTMLCanvasElement>(null)\n  const animationRef = useRef<number>()\n  const phaseTimerRef = useRef<number>()\n  const gameTimerRef = useRef<number>()\n\n  // Breathing pattern: 4-4-4-4 (inhale-hold-exhale-pause)\n  const breathingPattern = {\n    inhale: 4000,\n    hold: 4000,\n    exhale: 4000,\n    pause: 4000\n  }\n\n  // Initialize flowers\n  useEffect(() => {\n    const initialFlowers: Flower[] = []\n    for (let i = 0; i < 12; i++) {\n      initialFlowers.push({\n        id: i,\n        x: Math.random() * 600 + 50,\n        y: Math.random() * 400 + 100,\n        size: Math.random() * 30 + 20,\n        color: ['#FF6B9D', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'][Math.floor(Math.random() * 6)],\n        bloomed: false,\n        bloomProgress: 0\n      })\n    }\n    setFlowers(initialFlowers)\n  }, [])\n\n  // Game timer\n  useEffect(() => {\n    if (isPlaying && timeRemaining > 0) {\n      gameTimerRef.current = window.setInterval(() => {\n        setTimeRemaining(prev => {\n          if (prev <= 1) {\n            setIsPlaying(false)\n            return 0\n          }\n          return prev - 1\n        })\n      }, 1000)\n    } else {\n      if (gameTimerRef.current) {\n        clearInterval(gameTimerRef.current)\n      }\n    }\n\n    return () => {\n      if (gameTimerRef.current) {\n        clearInterval(gameTimerRef.current)\n      }\n    }\n  }, [isPlaying, timeRemaining])\n\n  // Breathing cycle management\n  useEffect(() => {\n    if (isPlaying) {\n      const duration = breathingPattern[breathPhase]\n      let startTime = Date.now()\n\n      const updateProgress = () => {\n        const elapsed = Date.now() - startTime\n        const progress = Math.min(elapsed / duration, 1)\n        setBreathProgress(progress)\n\n        if (progress < 1) {\n          animationRef.current = requestAnimationFrame(updateProgress)\n        }\n      }\n\n      updateProgress()\n\n      phaseTimerRef.current = window.setTimeout(() => {\n        const phases: Array<'inhale' | 'hold' | 'exhale' | 'pause'> = ['inhale', 'hold', 'exhale', 'pause']\n        const currentIndex = phases.indexOf(breathPhase)\n        const nextPhase = phases[(currentIndex + 1) % phases.length]\n        \n        setBreathPhase(nextPhase)\n        setBreathProgress(0)\n\n        if (nextPhase === 'inhale') {\n          setCycleCount(prev => prev + 1)\n          // Bloom a flower every 2 cycles\n          if ((cycleCount + 1) % 2 === 0) {\n            bloomRandomFlower()\n          }\n        }\n      }, duration)\n    }\n\n    return () => {\n      if (phaseTimerRef.current) {\n        clearTimeout(phaseTimerRef.current)\n      }\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current)\n      }\n    }\n  }, [isPlaying, breathPhase, cycleCount])\n\n  const bloomRandomFlower = () => {\n    setFlowers(prev => {\n      const unbloomedFlowers = prev.filter(f => !f.bloomed)\n      if (unbloomedFlowers.length === 0) return prev\n\n      const randomFlower = unbloomedFlowers[Math.floor(Math.random() * unbloomedFlowers.length)]\n      setScore(prevScore => prevScore + 10)\n\n      return prev.map(flower => \n        flower.id === randomFlower.id \n          ? { ...flower, bloomed: true, bloomProgress: 1 }\n          : flower\n      )\n    })\n  }\n\n  // Canvas drawing\n  useEffect(() => {\n    const canvas = canvasRef.current\n    if (!canvas) return\n\n    const ctx = canvas.getContext('2d')\n    if (!ctx) return\n\n    // Clear canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height)\n\n    // Draw background gradient\n    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)\n    gradient.addColorStop(0, '#87CEEB')\n    gradient.addColorStop(1, '#98FB98')\n    ctx.fillStyle = gradient\n    ctx.fillRect(0, 0, canvas.width, canvas.height)\n\n    // Draw flowers\n    flowers.forEach(flower => {\n      const { x, y, size, color, bloomed, bloomProgress } = flower\n      \n      // Draw stem\n      ctx.strokeStyle = '#228B22'\n      ctx.lineWidth = 3\n      ctx.beginPath()\n      ctx.moveTo(x, y + size)\n      ctx.lineTo(x, y + size + 30)\n      ctx.stroke()\n\n      if (bloomed || bloomProgress > 0) {\n        const currentSize = size * (bloomed ? 1 : bloomProgress)\n        \n        // Draw petals\n        ctx.fillStyle = color\n        for (let i = 0; i < 6; i++) {\n          const angle = (i * Math.PI * 2) / 6\n          const petalX = x + Math.cos(angle) * (currentSize * 0.6)\n          const petalY = y + Math.sin(angle) * (currentSize * 0.6)\n          \n          ctx.beginPath()\n          ctx.ellipse(petalX, petalY, currentSize * 0.3, currentSize * 0.5, angle, 0, Math.PI * 2)\n          ctx.fill()\n        }\n\n        // Draw center\n        ctx.fillStyle = '#FFD700'\n        ctx.beginPath()\n        ctx.arc(x, y, currentSize * 0.2, 0, Math.PI * 2)\n        ctx.fill()\n      } else {\n        // Draw bud\n        ctx.fillStyle = '#90EE90'\n        ctx.beginPath()\n        ctx.arc(x, y, size * 0.3, 0, Math.PI * 2)\n        ctx.fill()\n      }\n    })\n\n    // Draw breathing circle\n    const centerX = canvas.width / 2\n    const centerY = 100\n    const baseRadius = 40\n    \n    let currentRadius = baseRadius\n    if (breathPhase === 'inhale') {\n      currentRadius = baseRadius + (breathProgress * 20)\n    } else if (breathPhase === 'exhale') {\n      currentRadius = baseRadius + 20 - (breathProgress * 20)\n    } else {\n      currentRadius = baseRadius + 20\n    }\n\n    // Breathing circle\n    ctx.fillStyle = `rgba(100, 149, 237, ${0.3 + breathProgress * 0.4})`\n    ctx.beginPath()\n    ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2)\n    ctx.fill()\n\n    ctx.strokeStyle = '#4169E1'\n    ctx.lineWidth = 3\n    ctx.beginPath()\n    ctx.arc(centerX, centerY, currentRadius, 0, Math.PI * 2)\n    ctx.stroke()\n\n  }, [flowers, breathPhase, breathProgress])\n\n  const toggleGame = () => {\n    setIsPlaying(!isPlaying)\n  }\n\n  const resetGame = () => {\n    setIsPlaying(false)\n    setCycleCount(0)\n    setScore(0)\n    setTimeRemaining(300)\n    setBreathPhase('inhale')\n    setBreathProgress(0)\n    setFlowers(prev => prev.map(f => ({ ...f, bloomed: false, bloomProgress: 0 })))\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getBreathInstruction = () => {\n    switch (breathPhase) {\n      case 'inhale': return 'Breathe In Slowly'\n      case 'hold': return 'Hold Your Breath'\n      case 'exhale': return 'Breathe Out Slowly'\n      case 'pause': return 'Pause and Relax'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <Link href=\"/games\" className=\"flex items-center text-green-600 hover:text-green-700\">\n            <ArrowLeftIcon className=\"h-5 w-5 mr-2\" />\n            Back to Games\n          </Link>\n          <h1 className=\"text-3xl font-bold text-gray-900\">🌸 Breathing Garden</h1>\n          <button\n            onClick={() => setSoundEnabled(!soundEnabled)}\n            className=\"p-2 text-gray-600 hover:text-gray-800\"\n          >\n            {soundEnabled ? <SpeakerWaveIcon className=\"h-6 w-6\" /> : <SpeakerXMarkIcon className=\"h-6 w-6\" />}\n          </button>\n        </div>\n\n        {/* Game Stats */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-white rounded-lg p-4 text-center shadow-md\">\n            <div className=\"text-2xl font-bold text-green-600\">{score}</div>\n            <div className=\"text-sm text-gray-600\">Score</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-4 text-center shadow-md\">\n            <div className=\"text-2xl font-bold text-blue-600\">{cycleCount}</div>\n            <div className=\"text-sm text-gray-600\">Breath Cycles</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-4 text-center shadow-md\">\n            <div className=\"text-2xl font-bold text-purple-600\">{flowers.filter(f => f.bloomed).length}</div>\n            <div className=\"text-sm text-gray-600\">Flowers Bloomed</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-4 text-center shadow-md\">\n            <div className=\"text-2xl font-bold text-orange-600\">{formatTime(timeRemaining)}</div>\n            <div className=\"text-sm text-gray-600\">Time Left</div>\n          </div>\n        </div>\n\n        {/* Breathing Instruction */}\n        <div className=\"bg-white rounded-lg p-6 mb-6 text-center shadow-md\">\n          <div className=\"text-2xl font-semibold text-gray-800 mb-2\">\n            {getBreathInstruction()}\n          </div>\n          <div className=\"text-lg text-gray-600\">\n            Follow the circle and breathe deeply to help flowers bloom\n          </div>\n          <div className=\"mt-4 bg-gray-200 rounded-full h-2\">\n            <div \n              className=\"bg-blue-500 h-2 rounded-full transition-all duration-100\"\n              style={{ width: `${breathProgress * 100}%` }}\n            />\n          </div>\n        </div>\n\n        {/* Game Canvas */}\n        <div className=\"bg-white rounded-lg p-4 mb-6 shadow-md\">\n          <canvas\n            ref={canvasRef}\n            width={700}\n            height={500}\n            className=\"w-full h-auto border border-gray-200 rounded\"\n          />\n        </div>\n\n        {/* Game Controls */}\n        <div className=\"flex justify-center space-x-4\">\n          <button\n            onClick={toggleGame}\n            className=\"flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n          >\n            {isPlaying ? <PauseIcon className=\"h-5 w-5 mr-2\" /> : <PlayIcon className=\"h-5 w-5 mr-2\" />}\n            {isPlaying ? 'Pause' : 'Start'}\n          </button>\n          <button\n            onClick={resetGame}\n            className=\"px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            Reset Game\n          </button>\n        </div>\n\n        {/* Game Instructions */}\n        <div className=\"mt-8 bg-blue-50 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">How to Play:</h3>\n          <ul className=\"text-blue-800 space-y-2\">\n            <li>• Follow the breathing circle and instructions</li>\n            <li>• Complete breathing cycles to make flowers bloom</li>\n            <li>• Each flower that blooms gives you 10 points</li>\n            <li>• Practice for 5 minutes to complete the session</li>\n            <li>• Focus on slow, deep breathing for maximum relaxation</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0C;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,YAAY;;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE1B,wDAAwD;IACxD,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAA2B,EAAE;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,eAAe,IAAI,CAAC;gBAClB,IAAI;gBACJ,GAAG,KAAK,MAAM,KAAK,MAAM;gBACzB,GAAG,KAAK,MAAM,KAAK,MAAM;gBACzB,MAAM,KAAK,MAAM,KAAK,KAAK;gBAC3B,OAAO;oBAAC;oBAAW;oBAAW;oBAAW;oBAAW;oBAAW;iBAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBACxG,SAAS;gBACT,eAAe;YACjB;QACF;QACA,WAAW;IACb,GAAG,EAAE;IAEL,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,gBAAgB,GAAG;YAClC,aAAa,OAAO,GAAG,OAAO,WAAW,CAAC;gBACxC,iBAAiB,CAAA;oBACf,IAAI,QAAQ,GAAG;wBACb,aAAa;wBACb,OAAO;oBACT;oBACA,OAAO,OAAO;gBAChB;YACF,GAAG;QACL,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,cAAc,aAAa,OAAO;YACpC;QACF;QAEA,OAAO;YACL,IAAI,aAAa,OAAO,EAAE;gBACxB,cAAc,aAAa,OAAO;YACpC;QACF;IACF,GAAG;QAAC;QAAW;KAAc;IAE7B,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,MAAM,WAAW,gBAAgB,CAAC,YAAY;YAC9C,IAAI,YAAY,KAAK,GAAG;YAExB,MAAM,iBAAiB;gBACrB,MAAM,UAAU,KAAK,GAAG,KAAK;gBAC7B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,UAAU;gBAC9C,kBAAkB;gBAElB,IAAI,WAAW,GAAG;oBAChB,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;YACF;YAEA;YAEA,cAAc,OAAO,GAAG,OAAO,UAAU,CAAC;gBACxC,MAAM,SAAwD;oBAAC;oBAAU;oBAAQ;oBAAU;iBAAQ;gBACnG,MAAM,eAAe,OAAO,OAAO,CAAC;gBACpC,MAAM,YAAY,MAAM,CAAC,CAAC,eAAe,CAAC,IAAI,OAAO,MAAM,CAAC;gBAE5D,eAAe;gBACf,kBAAkB;gBAElB,IAAI,cAAc,UAAU;oBAC1B,cAAc,CAAA,OAAQ,OAAO;oBAC7B,gCAAgC;oBAChC,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM,GAAG;wBAC9B;oBACF;gBACF;YACF,GAAG;QACL;QAEA,OAAO;YACL,IAAI,cAAc,OAAO,EAAE;gBACzB,aAAa,cAAc,OAAO;YACpC;YACA,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG;QAAC;QAAW;QAAa;KAAW;IAEvC,MAAM,oBAAoB;QACxB,WAAW,CAAA;YACT,MAAM,mBAAmB,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO;YACpD,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;YAE1C,MAAM,eAAe,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;YAC1F,SAAS,CAAA,YAAa,YAAY;YAElC,OAAO,KAAK,GAAG,CAAC,CAAA,SACd,OAAO,EAAE,KAAK,aAAa,EAAE,GACzB;oBAAE,GAAG,MAAM;oBAAE,SAAS;oBAAM,eAAe;gBAAE,IAC7C;QAER;IACF;IAEA,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,eAAe;QACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE/C,2BAA2B;QAC3B,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG,OAAO,MAAM;QAChE,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,GAAG;QACzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAE9C,eAAe;QACf,QAAQ,OAAO,CAAC,CAAA;YACd,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG;YAEtD,YAAY;YACZ,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,GAAG,IAAI;YAClB,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO;YACzB,IAAI,MAAM;YAEV,IAAI,WAAW,gBAAgB,GAAG;gBAChC,MAAM,cAAc,OAAO,CAAC,UAAU,IAAI,aAAa;gBAEvD,cAAc;gBACd,IAAI,SAAS,GAAG;gBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,MAAM,QAAQ,AAAC,IAAI,KAAK,EAAE,GAAG,IAAK;oBAClC,MAAM,SAAS,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG;oBACvD,MAAM,SAAS,IAAI,KAAK,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG;oBAEvD,IAAI,SAAS;oBACb,IAAI,OAAO,CAAC,QAAQ,QAAQ,cAAc,KAAK,cAAc,KAAK,OAAO,GAAG,KAAK,EAAE,GAAG;oBACtF,IAAI,IAAI;gBACV;gBAEA,cAAc;gBACd,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,GAAG,GAAG,cAAc,KAAK,GAAG,KAAK,EAAE,GAAG;gBAC9C,IAAI,IAAI;YACV,OAAO;gBACL,WAAW;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,GAAG,GAAG,OAAO,KAAK,GAAG,KAAK,EAAE,GAAG;gBACvC,IAAI,IAAI;YACV;QACF;QAEA,wBAAwB;QACxB,MAAM,UAAU,OAAO,KAAK,GAAG;QAC/B,MAAM,UAAU;QAChB,MAAM,aAAa;QAEnB,IAAI,gBAAgB;QACpB,IAAI,gBAAgB,UAAU;YAC5B,gBAAgB,aAAc,iBAAiB;QACjD,OAAO,IAAI,gBAAgB,UAAU;YACnC,gBAAgB,aAAa,KAAM,iBAAiB;QACtD,OAAO;YACL,gBAAgB,aAAa;QAC/B;QAEA,mBAAmB;QACnB,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,MAAM,iBAAiB,IAAI,CAAC,CAAC;QACpE,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,SAAS,SAAS,eAAe,GAAG,KAAK,EAAE,GAAG;QACtD,IAAI,IAAI;QAER,IAAI,WAAW,GAAG;QAClB,IAAI,SAAS,GAAG;QAChB,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,SAAS,SAAS,eAAe,GAAG,KAAK,EAAE,GAAG;QACtD,IAAI,MAAM;IAEZ,GAAG;QAAC;QAAS;QAAa;KAAe;IAEzC,MAAM,aAAa;QACjB,aAAa,CAAC;IAChB;IAEA,MAAM,YAAY;QAChB,aAAa;QACb,cAAc;QACd,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,kBAAkB;QAClB,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;oBAAE,GAAG,CAAC;oBAAE,SAAS;oBAAO,eAAe;gBAAE,CAAC;IAC9E;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;;8CAC5B,8OAAC,yNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG5C,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAU;sCAET,6BAAe,8OAAC,6NAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;qDAAe,8OAAC,+NAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK1F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;;;;;;8CAC1F,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsC,WAAW;;;;;;8CAChE,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAK3C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAEH,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,iBAAiB,IAAI,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;8BAMjD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;gCAET,0BAAY,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAAoB,8OAAC,+MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACzE,YAAY,UAAU;;;;;;;sCAEzB,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/PlayIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlayIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/PauseIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PauseIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 5.25v13.5m-7.5-13.5v13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PauseIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/ArrowLeftIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowLeftIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowLeftIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,EACrB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/SpeakerWaveIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction SpeakerWaveIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(SpeakerWaveIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 760, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/node_modules/%40heroicons/react/24/outline/esm/SpeakerXMarkIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction SpeakerXMarkIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M17.25 9.75 19.5 12m0 0 2.25 2.25M19.5 12l2.25-2.25M19.5 12l-2.25 2.25m-10.5-6 4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(SpeakerXMarkIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,qMAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}