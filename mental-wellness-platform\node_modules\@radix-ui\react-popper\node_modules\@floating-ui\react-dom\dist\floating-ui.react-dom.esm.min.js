import{computePosition as e,arrow as r}from"@floating-ui/dom";export*from"@floating-ui/dom";import*as t from"react";import{useLayoutEffect as n,useEffect as u}from"react";import*as o from"react-dom";var c="undefined"!=typeof document?n:u;function l(e,r){if(e===r)return!0;if(typeof e!=typeof r)return!1;if("function"==typeof e&&e.toString()===r.toString())return!0;let t,n,u;if(e&&r&&"object"==typeof e){if(Array.isArray(e)){if(t=e.length,t!=r.length)return!1;for(n=t;0!=n--;)if(!l(e[n],r[n]))return!1;return!0}if(u=Object.keys(e),t=u.length,t!==Object.keys(r).length)return!1;for(n=t;0!=n--;)if(!Object.prototype.hasOwnProperty.call(r,u[n]))return!1;for(n=t;0!=n--;){const t=u[n];if(("_owner"!==t||!e.$$typeof)&&!l(e[t],r[t]))return!1}return!0}return e!=e&&r!=r}function a(r){let{middleware:n,placement:u="bottom",strategy:a="absolute",whileElementsMounted:f}=void 0===r?{}:r;const i=t.useRef(null),s=t.useRef(null),p=function(e){const r=t.useRef(e);return c((()=>{r.current=e})),r}(f),m=t.useRef(null),[d,y]=t.useState({x:null,y:null,strategy:a,placement:u,middlewareData:{}}),[g,b]=t.useState(n);l(null==g?void 0:g.map((e=>{let{options:r}=e;return r})),null==n?void 0:n.map((e=>{let{options:r}=e;return r})))||b(n);const h=t.useCallback((()=>{i.current&&s.current&&e(i.current,s.current,{middleware:g,placement:u,strategy:a}).then((e=>{w.current&&o.flushSync((()=>{y(e)}))}))}),[g,u,a]);c((()=>{w.current&&h()}),[h]);const w=t.useRef(!1);c((()=>(w.current=!0,()=>{w.current=!1})),[]);const k=t.useCallback((()=>{if("function"==typeof m.current&&(m.current(),m.current=null),i.current&&s.current)if(p.current){const e=p.current(i.current,s.current,h);m.current=e}else h()}),[h,p]),O=t.useCallback((e=>{i.current=e,k()}),[k]),j=t.useCallback((e=>{s.current=e,k()}),[k]),v=t.useMemo((()=>({reference:i,floating:s})),[]);return t.useMemo((()=>({...d,update:h,refs:v,reference:O,floating:j})),[d,h,v,O,j])}const f=e=>{const{element:t,padding:n}=e;return{name:"arrow",options:e,fn(e){return u=t,Object.prototype.hasOwnProperty.call(u,"current")?null!=t.current?r({element:t.current,padding:n}).fn(e):{}:t?r({element:t,padding:n}).fn(e):{};var u}}};export{f as arrow,a as useFloating};
