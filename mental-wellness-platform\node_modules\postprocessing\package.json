{"name": "postprocessing", "version": "6.37.6", "description": "A post processing library for three.js.", "homepage": "https://github.com/pmndrs/postprocessing", "license": "<PERSON><PERSON><PERSON>", "type": "module", "sideEffects": false, "main": "./build/index.cjs", "module": "./build/index.js", "types": "./build/types/index.d.cts", "exports": {".": {"import": {"types": "./build/types/index.d.ts", "default": "./build/index.js"}, "require": {"types": "./build/types/index.d.cts", "default": "./build/index.cjs"}}}, "keywords": ["rendering", "image", "filter", "effect", "composer", "pass", "post", "processing", "gpgpu", "rtt", "rendertexture", "rendertarget", "three"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pmndrs/postprocessing.git"}, "bugs": {"url": "https://github.com/pmndrs/postprocessing/issues"}, "files": ["./build"], "ava": {"failFast": true, "files": ["./test/**/*"]}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "core-js", "esbuild", "hugo-bin"]}, "scripts": {"ava": "ava", "build": "npm run clean && run-p build:css build:js:min build:dts", "build:css": "sass --no-source-map -I manual/assets/css/src/values manual/assets/css/src:manual/assets/css/dist", "build:js": "node esbuild", "build:js:min": "node esbuild -m", "build:dts": "cpy \"types/*\" build/types && cpy \"types/*\" build/types --rename=index.d.cts", "clean": "del-cli build temp manual/resources \"manual/assets/**/dist\" public", "copy": "cpy \"demo/static/**/*\" public/demo", "deploy": "run-s copy postcss hugo gzip", "doc": "esdoc", "gzip": "gzipper c \"public\"", "hugo": "hugo -s manual --minify", "lint": "run-p lint:*", "lint:css": "stylelint --fix manual/assets/css/src", "lint:js": "eslint --fix src demo/src manual/assets/js/src", "lint:dts": "tsc types/index.d.ts --noEmit", "postcss": "postcss manual/assets/css/dist/index.css -o manual/assets/css/dist/index.css -c manual", "prepublishOnly": "npm test", "prewatch": "run-s clean copy build:css build:js", "test": "run-s lint build ava doc", "start": "hugo server -s manual -e development", "watch": "run-p watch:* start", "watch:css": "sass --no-source-map -I manual/assets/css/src/values manual/assets/css/src:manual/assets/css/dist -w", "watch:js": "node esbuild -w"}, "peerDependencies": {"three": ">= 0.157.0 < 0.179.0"}, "devDependencies": {"@tweakpane/core": "2.x.x", "@types/node": "24.x.x", "@types/three": "0.x.x", "@typescript-eslint/eslint-plugin": "8.x.x", "@typescript-eslint/parser": "8.x.x", "autoprefixer": "10.x.x", "ava": "6.x.x", "cpy-cli": "5.x.x", "cssnano": "7.x.x", "dat.gui": "0.x.x", "del-cli": "6.x.x", "esbuild": "0.25.x", "esbuild-plugin-glsl": "1.x.x", "esdoc": "1.x.x", "esdoc-importpath-plugin": "1.x.x", "esdoc-standard-plugin": "1.x.x", "eslint": "9.x.x", "eslint-config-aether": "2.x.x", "gzipper": "8.x.x", "hugo-bin": "0.x.x", "npm-run-all": "4.x.x", "postcss": "8.x.x", "postcss-cli": "11.x.x", "postcss-preset-env": "10.x.x", "sass": "1.x.x", "spatial-controls": "6.x.x", "stylelint": "16.x.x", "stylelint-config-standard-scss": "15.x.x", "stylelint-order": "7.x.x", "three": "0.x.x", "three-demo": "5.x.x", "tiny-glob": "0.x.x", "tslib": "2.x.x", "tweakpane": "4.x.x", "typescript": "5.8.x"}}