'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline'
import { generateId } from '@/lib/utils'
import VoiceChat from '@/components/chatbot/VoiceChat'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  isTyping?: boolean
  typedContent?: string
  safetyAlert?: {
    riskLevel: string
    keywords: string[]
  }
  language?: 'en' | 'ta'
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [language, setLanguage] = useState<'en' | 'ta'>('en')
  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage = {
      id: generateId(),
      role: 'assistant' as const,
      content: language === 'ta'
        ? 'வணக்கம்! நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்? இது ஒரு பாதுகாப்பான இடம், இங்கே நீங்கள் உங்கள் மனதில் உள்ளதை பகிர்ந்து கொள்ளலாம்.'
        : 'Hello! I\'m here to provide emotional support and guidance. How are you feeling today? Remember, this is a safe space where you can share what\'s on your mind.',
      timestamp: new Date().toISOString(),
      language
    }

    setMessages([welcomeMessage])

    // Auto-speak welcome message
    setTimeout(() => {
      if (speakResponse) {
        speakResponse(welcomeMessage.content, language)
      }
    }, 1000)
  }, [language, speakResponse])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Typing effect for assistant messages
  const typeMessage = useCallback((message: Message) => {
    const words = message.content.split(' ')
    let currentIndex = 0

    const typeNextWord = () => {
      if (currentIndex < words.length) {
        const typedContent = words.slice(0, currentIndex + 1).join(' ')

        setMessages(prev => prev.map(msg =>
          msg.id === message.id
            ? { ...msg, typedContent, isTyping: true }
            : msg
        ))

        currentIndex++
        typingTimeoutRef.current = setTimeout(typeNextWord, 100 + Math.random() * 100)
      } else {
        setMessages(prev => prev.map(msg =>
          msg.id === message.id
            ? { ...msg, isTyping: false, typedContent: message.content }
            : msg
        ))

        // Auto-speak the response
        if (speakResponse && message.role === 'assistant') {
          setTimeout(() => {
            speakResponse(message.content, message.language || language)
          }, 500)
        }
      }
    }

    typeNextWord()
  }, [speakResponse, language])

  // Handle voice transcript
  const handleVoiceTranscript = useCallback((transcript: string) => {
    setInputMessage(transcript)
    // Auto-send voice messages
    setTimeout(() => {
      sendMessage(transcript)
    }, 500)
  }, [])

  // Handle voice response setup
  const handleSpeakResponse = useCallback((speakFn: any) => {
    setSpeakResponse(() => speakFn)
  }, [])

  const sendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputMessage.trim()
    if (!textToSend || isLoading) return

    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: textToSend,
      timestamp: new Date().toISOString(),
      language
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    // Show typing indicator
    const typingMessage: Message = {
      id: generateId(),
      role: 'assistant',
      content: language === 'ta' ? 'தட்டச்சு செய்கிறது...' : 'Typing...',
      timestamp: new Date().toISOString(),
      isTyping: true,
      language
    }

    setMessages(prev => [...prev, typingMessage])

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: textToSend,
          sessionId: generateId(),
          userId: 'anonymous',
          language
        }),
      })

      const data = await response.json()

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))

      if (data.success) {
        let responseContent = data.data.content

        // Translate response if needed (basic translation for demo)
        if (language === 'ta' && !responseContent.includes('தமிழ்')) {
          // Add Tamil translation for common responses
          const translations: { [key: string]: string } = {
            'I understand': 'நான் புரிந்துகொள்கிறேன்',
            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',
            'I\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',
            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',
            'That sounds difficult': 'அது கடினமாக இருக்கும்',
            'You\'re not alone': 'நீங்கள் தனியாக இல்லை'
          }

          Object.entries(translations).forEach(([en, ta]) => {
            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)
          })
        }

        const assistantMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: responseContent,
          timestamp: data.data.timestamp,
          safetyAlert: data.data.safetyAlert,
          language,
          isTyping: true,
          typedContent: ''
        }

        setMessages(prev => [...prev, assistantMessage])

        // Start typing effect
        setTimeout(() => {
          typeMessage(assistantMessage)
        }, 500)

      } else {
        throw new Error(data.error || 'Failed to get response')
      }
    } catch (error) {
      console.error('Error sending message:', error)

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))

      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: language === 'ta'
          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும், அல்லது இது அவசரநிலை என்றால், 911 அல்லது நெருக்கடி ஹாட்லைனை தொடர்பு கொள்ளவும்.'
          : 'I apologize, but I\'m having trouble responding right now. Please try again, or if this is an emergency, please call 911 or contact a crisis hotline.',
        timestamp: new Date().toISOString(),
        language
      }
      setMessages(prev => [...prev, errorMessage])
      typeMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="max-w-5xl mx-auto p-4 h-[calc(100vh-200px)]">
      <div className="bg-white rounded-lg shadow-lg h-full flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold flex items-center">
                <ChatBubbleLeftRightIcon className="h-6 w-6 mr-2" />
                {language === 'ta' ? 'AI மனநல ஆதரவு' : 'AI Mental Health Support'}
              </h1>
              <p className="text-pink-100 text-sm">
                {language === 'ta'
                  ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு • 24/7 கிடைக்கும்'
                  : 'Safe, confidential emotional support • Available 24/7'
                }
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">
                {language === 'ta' ? 'ஆன்லைன்' : 'Online'}
              </span>
            </div>
          </div>
        </div>

        {/* Voice Chat Component */}
        <VoiceChat
          onTranscript={handleVoiceTranscript}
          onSpeakResponse={handleSpeakResponse}
          isListening={isListening}
          setIsListening={setIsListening}
          language={language}
          setLanguage={setLanguage}
        />

        {/* Emergency Notice */}
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">
                <strong>Emergency:</strong> If you're in immediate danger, call 911. 
                For crisis support, call 988 or text HOME to 741741.
              </p>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} items-end space-x-2`}
            >
              {/* Avatar */}
              {message.role === 'assistant' && (
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center text-white text-sm font-bold mb-1">
                  AI
                </div>
              )}

              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-br-md'
                    : 'bg-white text-gray-900 rounded-bl-md border border-gray-200'
                }`}
              >
                {message.safetyAlert && (
                  <div className={`mb-2 p-2 rounded-lg text-xs ${
                    message.safetyAlert.riskLevel === 'critical'
                      ? 'bg-red-100 text-red-800 border border-red-200'
                      : message.safetyAlert.riskLevel === 'high'
                      ? 'bg-orange-100 text-orange-800 border border-orange-200'
                      : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                  }`}>
                    ⚠️ {language === 'ta' ? 'பாதுகாப்பு எச்சரிக்கை' : 'Safety alert detected'} - {message.safetyAlert.riskLevel} risk
                  </div>
                )}

                <div className="whitespace-pre-wrap">
                  {message.isTyping ? (
                    <div>
                      <span>{message.typedContent || ''}</span>
                      {message.typedContent !== message.content && (
                        <span className="inline-block w-2 h-5 bg-gray-400 ml-1 animate-pulse"></span>
                      )}
                    </div>
                  ) : (
                    message.content
                  )}
                </div>

                <div className={`flex items-center justify-between mt-2 text-xs ${
                  message.role === 'user' ? 'text-pink-200' : 'text-gray-500'
                }`}>
                  <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
                  {message.language && (
                    <span className="ml-2 px-1 py-0.5 bg-gray-200 text-gray-600 rounded text-xs">
                      {message.language === 'ta' ? 'தமிழ்' : 'EN'}
                    </span>
                  )}
                </div>
              </div>

              {/* User Avatar */}
              {message.role === 'user' && (
                <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mb-1">
                  <UserIcon className="h-5 w-5" />
                </div>
              )}
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="border-t bg-white p-4">
          <div className="flex space-x-3">
            <div className="flex-1">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={language === 'ta'
                  ? 'உங்கள் மனதில் உள்ளதை பகிர்ந்து கொள்ளுங்கள்... (அனுப்ப Enter அழுத்தவும்)'
                  : 'Share what\'s on your mind... (Press Enter to send)'
                }
                className="w-full border border-gray-300 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none text-sm"
                rows={2}
                disabled={isLoading || isListening}
              />
              {isListening && (
                <div className="mt-2 text-sm text-red-600 flex items-center">
                  <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse mr-2"></div>
                  {language === 'ta' ? 'குரல் கேட்கப்படுகிறது...' : 'Listening for voice input...'}
                </div>
              )}
            </div>
            <button
              onClick={() => sendMessage()}
              disabled={!inputMessage.trim() || isLoading || isListening}
              className="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-6 py-3 rounded-xl hover:from-pink-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md"
            >
              {isLoading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <PaperAirplaneIcon className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Quick Actions */}
          <div className="mt-3 flex flex-wrap gap-2">
            <button
              onClick={() => sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious')}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              disabled={isLoading}
            >
              {language === 'ta' ? '😰 கவலை' : '😰 Anxious'}
            </button>
            <button
              onClick={() => sendMessage(language === 'ta' ? 'நான் சோகமாக உணர்கிறேன்' : 'I feel sad')}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              disabled={isLoading}
            >
              {language === 'ta' ? '😢 சோகம்' : '😢 Sad'}
            </button>
            <button
              onClick={() => sendMessage(language === 'ta' ? 'நான் மன அழுத்தத்தில் இருக்கிறேன்' : 'I feel stressed')}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              disabled={isLoading}
            >
              {language === 'ta' ? '😤 மன அழுத்தம்' : '😤 Stressed'}
            </button>
            <button
              onClick={() => sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help')}
              className="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm hover:bg-red-200 transition-colors"
              disabled={isLoading}
            >
              {language === 'ta' ? '🆘 உதவி' : '🆘 Help'}
            </button>
          </div>

          <p className="text-xs text-gray-500 mt-3 text-center">
            {language === 'ta'
              ? 'இந்த AI ஆதரவு வழங்குகிறது ஆனால் தொழில்முறை சிகிச்சை அல்லது மருத்துவ பராமரிப்புக்கு மாற்றாக இல்லை.'
              : 'This AI provides support but is not a substitute for professional therapy or medical care.'
            }
          </p>
        </div>
      </div>
    </div>
  )
}
