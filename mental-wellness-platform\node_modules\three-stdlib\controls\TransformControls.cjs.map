{"version": 3, "file": "TransformControls.cjs", "sources": ["../../src/controls/TransformControls.ts"], "sourcesContent": ["import {\n  BoxGeometry,\n  BufferGeometry,\n  Color,\n  CylinderGeometry,\n  DoubleSide,\n  Euler,\n  Float32BufferAttribute,\n  Line,\n  LineBasicMaterial,\n  Material,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  OctahedronGeometry,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PlaneGeometry,\n  Quaternion,\n  Raycaster,\n  SphereGeometry,\n  Intersection,\n  TorusGeometry,\n  Vector3,\n  Camera,\n  Vector2,\n} from 'three'\n\nexport interface TransformControlsPointerObject {\n  x: number\n  y: number\n  button: number\n}\n\nclass TransformControls<TCamera extends Camera = Camera> extends Object3D {\n  public readonly isTransformControls = true\n\n  public visible = false\n\n  private domElement: HTMLElement | undefined\n\n  private raycaster = new Raycaster()\n\n  private gizmo: TransformControlsGizmo\n  private plane: TransformControlsPlane\n\n  private tempVector = new Vector3()\n  private tempVector2 = new Vector3()\n  private tempQuaternion = new Quaternion()\n  private unit = {\n    X: new Vector3(1, 0, 0),\n    Y: new Vector3(0, 1, 0),\n    Z: new Vector3(0, 0, 1),\n  }\n\n  private pointStart = new Vector3()\n  private pointEnd = new Vector3()\n  private offset = new Vector3()\n  private rotationAxis = new Vector3()\n  private startNorm = new Vector3()\n  private endNorm = new Vector3()\n  private rotationAngle = 0\n\n  private cameraPosition = new Vector3()\n  private cameraQuaternion = new Quaternion()\n  private cameraScale = new Vector3()\n\n  private parentPosition = new Vector3()\n  private parentQuaternion = new Quaternion()\n  private parentQuaternionInv = new Quaternion()\n  private parentScale = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n  private worldScaleStart = new Vector3()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n  private worldQuaternionInv = new Quaternion()\n  private worldScale = new Vector3()\n\n  private eye = new Vector3()\n\n  private positionStart = new Vector3()\n  private quaternionStart = new Quaternion()\n  private scaleStart = new Vector3()\n\n  private camera: TCamera\n  private object: Object3D | undefined\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private translationSnap: number | null = null\n  private rotationSnap: number | null = null\n  private scaleSnap: number | null = null\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  // events\n  private changeEvent = { type: 'change' }\n  private mouseDownEvent = { type: 'mouseDown', mode: this.mode }\n  private mouseUpEvent = { type: 'mouseUp', mode: this.mode }\n  private objectChangeEvent = { type: 'objectChange' }\n\n  constructor(camera: TCamera, domElement: HTMLElement | undefined) {\n    super()\n\n    this.domElement = domElement\n    this.camera = camera\n\n    this.gizmo = new TransformControlsGizmo()\n    this.add(this.gizmo)\n\n    this.plane = new TransformControlsPlane()\n    this.add(this.plane)\n\n    // Defined getter, setter and store for a property\n    const defineProperty = <TValue>(propName: string, defaultValue: TValue): void => {\n      let propValue = defaultValue\n\n      Object.defineProperty(this, propName, {\n        get: function () {\n          return propValue !== undefined ? propValue : defaultValue\n        },\n\n        set: function (value) {\n          if (propValue !== value) {\n            propValue = value\n            this.plane[propName] = value\n            this.gizmo[propName] = value\n\n            this.dispatchEvent({ type: propName + '-changed', value: value })\n            this.dispatchEvent(this.changeEvent)\n          }\n        },\n      })\n\n      //@ts-ignore\n      this[propName] = defaultValue\n      // @ts-ignore\n      this.plane[propName] = defaultValue\n      // @ts-ignore\n      this.gizmo[propName] = defaultValue\n    }\n\n    defineProperty('camera', this.camera)\n    defineProperty('object', this.object)\n    defineProperty('enabled', this.enabled)\n    defineProperty('axis', this.axis)\n    defineProperty('mode', this.mode)\n    defineProperty('translationSnap', this.translationSnap)\n    defineProperty('rotationSnap', this.rotationSnap)\n    defineProperty('scaleSnap', this.scaleSnap)\n    defineProperty('space', this.space)\n    defineProperty('size', this.size)\n    defineProperty('dragging', this.dragging)\n    defineProperty('showX', this.showX)\n    defineProperty('showY', this.showY)\n    defineProperty('showZ', this.showZ)\n    defineProperty('worldPosition', this.worldPosition)\n    defineProperty('worldPositionStart', this.worldPositionStart)\n    defineProperty('worldQuaternion', this.worldQuaternion)\n    defineProperty('worldQuaternionStart', this.worldQuaternionStart)\n    defineProperty('cameraPosition', this.cameraPosition)\n    defineProperty('cameraQuaternion', this.cameraQuaternion)\n    defineProperty('pointStart', this.pointStart)\n    defineProperty('pointEnd', this.pointEnd)\n    defineProperty('rotationAxis', this.rotationAxis)\n    defineProperty('rotationAngle', this.rotationAngle)\n    defineProperty('eye', this.eye)\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n  }\n\n  private intersectObjectWithRay = (\n    object: Object3D,\n    raycaster: Raycaster,\n    includeInvisible?: boolean,\n  ): false | Intersection => {\n    const allIntersections = raycaster.intersectObject(object, true)\n\n    for (let i = 0; i < allIntersections.length; i++) {\n      if (allIntersections[i].object.visible || includeInvisible) {\n        return allIntersections[i]\n      }\n    }\n\n    return false\n  }\n\n  // Set current object\n  public attach = (object: Object3D): this => {\n    this.object = object\n    this.visible = true\n\n    return this\n  }\n\n  // Detatch from object\n  public detach = (): this => {\n    this.object = undefined\n    this.visible = false\n    this.axis = null\n\n    return this\n  }\n\n  // Reset\n  public reset = (): this => {\n    if (!this.enabled) return this\n\n    if (this.dragging) {\n      if (this.object !== undefined) {\n        this.object.position.copy(this.positionStart)\n        this.object.quaternion.copy(this.quaternionStart)\n        this.object.scale.copy(this.scaleStart)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n        // @ts-ignore\n        this.dispatchEvent(this.objectChangeEvent)\n        this.pointStart.copy(this.pointEnd)\n      }\n    }\n\n    return this\n  }\n\n  public updateMatrixWorld = (): void => {\n    if (this.object !== undefined) {\n      this.object.updateMatrixWorld()\n\n      if (this.object.parent === null) {\n        console.error('TransformControls: The attached 3D object must be a part of the scene graph.')\n      } else {\n        this.object.parent.matrixWorld.decompose(this.parentPosition, this.parentQuaternion, this.parentScale)\n      }\n\n      this.object.matrixWorld.decompose(this.worldPosition, this.worldQuaternion, this.worldScale)\n\n      this.parentQuaternionInv.copy(this.parentQuaternion).invert()\n      this.worldQuaternionInv.copy(this.worldQuaternion).invert()\n    }\n\n    this.camera.updateMatrixWorld()\n    this.camera.matrixWorld.decompose(this.cameraPosition, this.cameraQuaternion, this.cameraScale)\n\n    this.eye.copy(this.cameraPosition).sub(this.worldPosition).normalize()\n\n    super.updateMatrixWorld()\n  }\n\n  private pointerHover = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const intersect = this.intersectObjectWithRay(this.gizmo.picker[this.mode], this.raycaster)\n\n    if (intersect) {\n      this.axis = intersect.object.name\n    } else {\n      this.axis = null\n    }\n  }\n\n  private pointerDown = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true || pointer.button !== 0) return\n\n    if (this.axis !== null) {\n      this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n      const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n      if (planeIntersect) {\n        let space = this.space\n\n        if (this.mode === 'scale') {\n          space = 'local'\n        } else if (this.axis === 'E' || this.axis === 'XYZE' || this.axis === 'XYZ') {\n          space = 'world'\n        }\n\n        if (space === 'local' && this.mode === 'rotate') {\n          const snap = this.rotationSnap\n\n          if (this.axis === 'X' && snap) this.object.rotation.x = Math.round(this.object.rotation.x / snap) * snap\n          if (this.axis === 'Y' && snap) this.object.rotation.y = Math.round(this.object.rotation.y / snap) * snap\n          if (this.axis === 'Z' && snap) this.object.rotation.z = Math.round(this.object.rotation.z / snap) * snap\n        }\n\n        this.object.updateMatrixWorld()\n\n        if (this.object.parent) {\n          this.object.parent.updateMatrixWorld()\n        }\n\n        this.positionStart.copy(this.object.position)\n        this.quaternionStart.copy(this.object.quaternion)\n        this.scaleStart.copy(this.object.scale)\n\n        this.object.matrixWorld.decompose(this.worldPositionStart, this.worldQuaternionStart, this.worldScaleStart)\n\n        this.pointStart.copy(planeIntersect.point).sub(this.worldPositionStart)\n      }\n\n      this.dragging = true\n      this.mouseDownEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseDownEvent)\n    }\n  }\n\n  private pointerMove = (pointer: TransformControlsPointerObject): void => {\n    const axis = this.axis\n    const mode = this.mode\n    const object = this.object\n    let space = this.space\n\n    if (mode === 'scale') {\n      space = 'local'\n    } else if (axis === 'E' || axis === 'XYZE' || axis === 'XYZ') {\n      space = 'world'\n    }\n\n    if (object === undefined || axis === null || this.dragging === false || pointer.button !== -1) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n    if (!planeIntersect) return\n\n    this.pointEnd.copy(planeIntersect.point).sub(this.worldPositionStart)\n\n    if (mode === 'translate') {\n      // Apply translate\n\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.worldQuaternionInv)\n      }\n\n      if (axis.indexOf('X') === -1) this.offset.x = 0\n      if (axis.indexOf('Y') === -1) this.offset.y = 0\n      if (axis.indexOf('Z') === -1) this.offset.z = 0\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.quaternionStart).divide(this.parentScale)\n      } else {\n        this.offset.applyQuaternion(this.parentQuaternionInv).divide(this.parentScale)\n      }\n\n      object.position.copy(this.offset).add(this.positionStart)\n\n      // Apply translation snap\n\n      if (this.translationSnap) {\n        if (space === 'local') {\n          object.position.applyQuaternion(this.tempQuaternion.copy(this.quaternionStart).invert())\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          object.position.applyQuaternion(this.quaternionStart)\n        }\n\n        if (space === 'world') {\n          if (object.parent) {\n            object.position.add(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          if (object.parent) {\n            object.position.sub(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n        }\n      }\n    } else if (mode === 'scale') {\n      if (axis.search('XYZ') !== -1) {\n        let d = this.pointEnd.length() / this.pointStart.length()\n\n        if (this.pointEnd.dot(this.pointStart) < 0) d *= -1\n\n        this.tempVector2.set(d, d, d)\n      } else {\n        this.tempVector.copy(this.pointStart)\n        this.tempVector2.copy(this.pointEnd)\n\n        this.tempVector.applyQuaternion(this.worldQuaternionInv)\n        this.tempVector2.applyQuaternion(this.worldQuaternionInv)\n\n        this.tempVector2.divide(this.tempVector)\n\n        if (axis.search('X') === -1) {\n          this.tempVector2.x = 1\n        }\n\n        if (axis.search('Y') === -1) {\n          this.tempVector2.y = 1\n        }\n\n        if (axis.search('Z') === -1) {\n          this.tempVector2.z = 1\n        }\n      }\n\n      // Apply scale\n\n      object.scale.copy(this.scaleStart).multiply(this.tempVector2)\n\n      if (this.scaleSnap && this.object) {\n        if (axis.search('X') !== -1) {\n          this.object.scale.x = Math.round(object.scale.x / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Y') !== -1) {\n          object.scale.y = Math.round(object.scale.y / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Z') !== -1) {\n          object.scale.z = Math.round(object.scale.z / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n      }\n    } else if (mode === 'rotate') {\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      const ROTATION_SPEED =\n        20 / this.worldPosition.distanceTo(this.tempVector.setFromMatrixPosition(this.camera.matrixWorld))\n\n      if (axis === 'E') {\n        this.rotationAxis.copy(this.eye)\n        this.rotationAngle = this.pointEnd.angleTo(this.pointStart)\n\n        this.startNorm.copy(this.pointStart).normalize()\n        this.endNorm.copy(this.pointEnd).normalize()\n\n        this.rotationAngle *= this.endNorm.cross(this.startNorm).dot(this.eye) < 0 ? 1 : -1\n      } else if (axis === 'XYZE') {\n        this.rotationAxis.copy(this.offset).cross(this.eye).normalize()\n        this.rotationAngle = this.offset.dot(this.tempVector.copy(this.rotationAxis).cross(this.eye)) * ROTATION_SPEED\n      } else if (axis === 'X' || axis === 'Y' || axis === 'Z') {\n        this.rotationAxis.copy(this.unit[axis])\n\n        this.tempVector.copy(this.unit[axis])\n\n        if (space === 'local') {\n          this.tempVector.applyQuaternion(this.worldQuaternion)\n        }\n\n        this.rotationAngle = this.offset.dot(this.tempVector.cross(this.eye).normalize()) * ROTATION_SPEED\n      }\n\n      // Apply rotation snap\n\n      if (this.rotationSnap) {\n        this.rotationAngle = Math.round(this.rotationAngle / this.rotationSnap) * this.rotationSnap\n      }\n\n      // Apply rotate\n      if (space === 'local' && axis !== 'E' && axis !== 'XYZE') {\n        object.quaternion.copy(this.quaternionStart)\n        object.quaternion\n          .multiply(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n          .normalize()\n      } else {\n        this.rotationAxis.applyQuaternion(this.parentQuaternionInv)\n        object.quaternion.copy(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n        object.quaternion.multiply(this.quaternionStart).normalize()\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.objectChangeEvent)\n  }\n\n  private pointerUp = (pointer: TransformControlsPointerObject): void => {\n    if (pointer.button !== 0) return\n\n    if (this.dragging && this.axis !== null) {\n      this.mouseUpEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseUpEvent)\n    }\n\n    this.dragging = false\n    this.axis = null\n  }\n\n  private getPointer = (event: Event): TransformControlsPointerObject => {\n    if (this.domElement && this.domElement.ownerDocument?.pointerLockElement) {\n      return {\n        x: 0,\n        y: 0,\n        button: (event as MouseEvent).button,\n      }\n    } else {\n      const pointer = (event as TouchEvent).changedTouches\n        ? (event as TouchEvent).changedTouches[0]\n        : (event as MouseEvent)\n\n      const rect = this.domElement!.getBoundingClientRect()\n\n      return {\n        x: ((pointer.clientX - rect.left) / rect.width) * 2 - 1,\n        y: (-(pointer.clientY - rect.top) / rect.height) * 2 + 1,\n        button: (event as MouseEvent).button,\n      }\n    }\n  }\n\n  private onPointerHover = (event: Event): void => {\n    if (!this.enabled) return\n\n    switch ((event as PointerEvent).pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.pointerHover(this.getPointer(event))\n        break\n    }\n  }\n\n  private onPointerDown = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction = 'none' // disable touch scroll\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.pointerHover(this.getPointer(event))\n    this.pointerDown(this.getPointer(event))\n  }\n\n  private onPointerMove = (event: Event): void => {\n    if (!this.enabled) return\n\n    this.pointerMove(this.getPointer(event))\n  }\n\n  private onPointerUp = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction! = ''\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n\n    this.pointerUp(this.getPointer(event))\n  }\n\n  public getMode = (): TransformControls['mode'] => this.mode\n\n  public setMode = (mode: TransformControls['mode']): void => {\n    this.mode = mode\n  }\n\n  public setTranslationSnap = (translationSnap: number): void => {\n    this.translationSnap = translationSnap\n  }\n\n  public setRotationSnap = (rotationSnap: number): void => {\n    this.rotationSnap = rotationSnap\n  }\n\n  public setScaleSnap = (scaleSnap: number): void => {\n    this.scaleSnap = scaleSnap\n  }\n\n  public setSize = (size: number): void => {\n    this.size = size\n  }\n\n  public setSpace = (space: string): void => {\n    this.space = space\n  }\n\n  public update = (): void => {\n    console.warn(\n      'THREE.TransformControls: update function has no more functionality and therefore has been deprecated.',\n    )\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointermove', this.onPointerHover)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n  }\n\n  public dispose = (): void => {\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointermove', this.onPointerHover)\n    this.domElement?.ownerDocument?.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement?.ownerDocument?.removeEventListener('pointerup', this.onPointerUp)\n\n    this.traverse((child) => {\n      const mesh = child as Mesh<BufferGeometry, Material>\n      if (mesh.geometry) {\n        mesh.geometry.dispose()\n      }\n      if (mesh.material) {\n        mesh.material.dispose()\n      }\n    })\n  }\n}\n\ntype TransformControlsGizmoPrivateGizmos = {\n  ['translate']: Object3D\n  ['scale']: Object3D\n  ['rotate']: Object3D\n  ['visible']: boolean\n}\n\nclass TransformControlsGizmo extends Object3D {\n  private isTransformControlsGizmo = true\n  public type = 'TransformControlsGizmo'\n\n  private tempVector = new Vector3(0, 0, 0)\n  private tempEuler = new Euler()\n  private alignVector = new Vector3(0, 1, 0)\n  private zeroVector = new Vector3(0, 0, 0)\n  private lookAtMatrix = new Matrix4()\n  private tempQuaternion = new Quaternion()\n  private tempQuaternion2 = new Quaternion()\n  private identityQuaternion = new Quaternion()\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private gizmo: TransformControlsGizmoPrivateGizmos\n  public picker: TransformControlsGizmoPrivateGizmos\n  private helper: TransformControlsGizmoPrivateGizmos\n\n  // these are set from parent class TransformControls\n  private rotationAxis = new Vector3()\n\n  private cameraPosition = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private camera: PerspectiveCamera | OrthographicCamera = null!\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  constructor() {\n    super()\n\n    const gizmoMaterial = new MeshBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      side: DoubleSide,\n      fog: false,\n      toneMapped: false,\n    })\n\n    const gizmoLineMaterial = new LineBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      linewidth: 1,\n      fog: false,\n      toneMapped: false,\n    })\n\n    // Make unique material for each axis/color\n\n    const matInvisible = gizmoMaterial.clone()\n    matInvisible.opacity = 0.15\n\n    const matHelper = gizmoMaterial.clone()\n    matHelper.opacity = 0.33\n\n    const matRed = gizmoMaterial.clone() as MeshBasicMaterial\n    matRed.color.set(0xff0000)\n\n    const matGreen = gizmoMaterial.clone() as MeshBasicMaterial\n    matGreen.color.set(0x00ff00)\n\n    const matBlue = gizmoMaterial.clone() as MeshBasicMaterial\n    matBlue.color.set(0x0000ff)\n\n    const matWhiteTransparent = gizmoMaterial.clone() as MeshBasicMaterial\n    matWhiteTransparent.opacity = 0.25\n\n    const matYellowTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matYellowTransparent.color.set(0xffff00)\n\n    const matCyanTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matCyanTransparent.color.set(0x00ffff)\n\n    const matMagentaTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matMagentaTransparent.color.set(0xff00ff)\n\n    const matYellow = gizmoMaterial.clone() as MeshBasicMaterial\n    matYellow.color.set(0xffff00)\n\n    const matLineRed = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineRed.color.set(0xff0000)\n\n    const matLineGreen = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGreen.color.set(0x00ff00)\n\n    const matLineBlue = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineBlue.color.set(0x0000ff)\n\n    const matLineCyan = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineCyan.color.set(0x00ffff)\n\n    const matLineMagenta = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineMagenta.color.set(0xff00ff)\n\n    const matLineYellow = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineYellow.color.set(0xffff00)\n\n    const matLineGray = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGray.color.set(0x787878)\n\n    const matLineYellowTransparent = matLineYellow.clone() as LineBasicMaterial\n    matLineYellowTransparent.opacity = 0.25\n\n    // reusable geometry\n\n    const arrowGeometry = new CylinderGeometry(0, 0.05, 0.2, 12, 1, false)\n\n    const scaleHandleGeometry = new BoxGeometry(0.125, 0.125, 0.125)\n\n    const lineGeometry = new BufferGeometry()\n    lineGeometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 0, 0], 3))\n\n    const CircleGeometry = (radius: number, arc: number): BufferGeometry => {\n      const geometry = new BufferGeometry()\n      const vertices = []\n\n      for (let i = 0; i <= 64 * arc; ++i) {\n        vertices.push(0, Math.cos((i / 32) * Math.PI) * radius, Math.sin((i / 32) * Math.PI) * radius)\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n\n      return geometry\n    }\n\n    // Special geometry for transform helper. If scaled with position vector it spans from [0,0,0] to position\n\n    const TranslateHelperGeometry = (): BufferGeometry => {\n      const geometry = new BufferGeometry()\n\n      geometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 1, 1], 3))\n\n      return geometry\n    }\n\n    // Gizmo definitions - custom hierarchy definitions for setupGizmo() function\n\n    const gizmoTranslate = {\n      X: [\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, -Math.PI / 2], null, 'fwd'],\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, Math.PI / 2], null, 'bwd'],\n        [new Line(lineGeometry, matLineRed)],\n      ],\n      Y: [\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], null, null, 'fwd'],\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], [Math.PI, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2]],\n      ],\n      Z: [\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [Math.PI / 2, 0, 0], null, 'fwd'],\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [-Math.PI / 2, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0]],\n      ],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.1, 0), matWhiteTransparent.clone()), [0, 0, 0], [0, 0, 0]]],\n      XY: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matYellowTransparent.clone()), [0.15, 0.15, 0]],\n        [new Line(lineGeometry, matLineYellow), [0.18, 0.3, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.3, 0.18, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matCyanTransparent.clone()), [0, 0.15, 0.15], [0, Math.PI / 2, 0]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.18, 0.3], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.3, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [\n          new Mesh(new PlaneGeometry(0.295, 0.295), matMagentaTransparent.clone()),\n          [0.15, 0, 0.15],\n          [-Math.PI / 2, 0, 0],\n        ],\n        [new Line(lineGeometry, matLineMagenta), [0.18, 0, 0.3], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.3, 0, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n    }\n\n    const pickerTranslate = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0.6, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0.6, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0, 0.6], [Math.PI / 2, 0, 0]]],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.2, 0), matInvisible)]],\n      XY: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0.2, 0]]],\n      YZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0, 0.2, 0.2], [0, Math.PI / 2, 0]]],\n      XZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0, 0.2], [-Math.PI / 2, 0, 0]]],\n    }\n\n    const helperTranslate = {\n      START: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      END: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      DELTA: [[new Line(TranslateHelperGeometry(), matHelper), null, null, null, 'helper']],\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    const gizmoRotate = {\n      X: [\n        [new Line(CircleGeometry(1, 0.5), matLineRed)],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matRed), [0, 0, 0.99], null, [1, 3, 1]],\n      ],\n      Y: [\n        [new Line(CircleGeometry(1, 0.5), matLineGreen), null, [0, 0, -Math.PI / 2]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matGreen), [0, 0, 0.99], null, [3, 1, 1]],\n      ],\n      Z: [\n        [new Line(CircleGeometry(1, 0.5), matLineBlue), null, [0, Math.PI / 2, 0]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matBlue), [0.99, 0, 0], null, [1, 3, 1]],\n      ],\n      E: [\n        [new Line(CircleGeometry(1.25, 1), matLineYellowTransparent), null, [0, Math.PI / 2, 0]],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [1.17, 0, 0],\n          [0, 0, -Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [-1.17, 0, 0],\n          [0, 0, Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, -1.17, 0],\n          [Math.PI, 0, 0],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, 1.17, 0],\n          [0, 0, 0],\n          [1, 1, 0.001],\n        ],\n      ],\n      XYZE: [[new Line(CircleGeometry(1, 1), matLineGray), null, [0, Math.PI / 2, 0]]],\n    }\n\n    const helperRotate = {\n      AXIS: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n    }\n\n    const pickerRotate = {\n      X: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, -Math.PI / 2, -Math.PI / 2]]],\n      Y: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [Math.PI / 2, 0, 0]]],\n      Z: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, 0, -Math.PI / 2]]],\n      E: [[new Mesh(new TorusGeometry(1.25, 0.1, 2, 24), matInvisible)]],\n      XYZE: [[new Mesh(new SphereGeometry(0.7, 10, 8), matInvisible)]],\n    }\n\n    const gizmoScale = {\n      X: [\n        [new Mesh(scaleHandleGeometry, matRed), [0.8, 0, 0], [0, 0, -Math.PI / 2]],\n        [new Line(lineGeometry, matLineRed), null, null, [0.8, 1, 1]],\n      ],\n      Y: [\n        [new Mesh(scaleHandleGeometry, matGreen), [0, 0.8, 0]],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2], [0.8, 1, 1]],\n      ],\n      Z: [\n        [new Mesh(scaleHandleGeometry, matBlue), [0, 0, 0.8], [Math.PI / 2, 0, 0]],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0], [0.8, 1, 1]],\n      ],\n      XY: [\n        [new Mesh(scaleHandleGeometry, matYellowTransparent), [0.85, 0.85, 0], null, [2, 2, 0.2]],\n        [new Line(lineGeometry, matLineYellow), [0.855, 0.98, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.98, 0.855, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(scaleHandleGeometry, matCyanTransparent), [0, 0.85, 0.85], null, [0.2, 2, 2]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.855, 0.98], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.98, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [new Mesh(scaleHandleGeometry, matMagentaTransparent), [0.85, 0, 0.85], null, [2, 0.2, 2]],\n        [new Line(lineGeometry, matLineMagenta), [0.855, 0, 0.98], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.98, 0, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XYZX: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 0, 1.1]]],\n    }\n\n    const pickerScale = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0.5, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0.5, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0, 0.5], [Math.PI / 2, 0, 0]]],\n      XY: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0.85, 0], null, [3, 3, 0.2]]],\n      YZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0, 0.85, 0.85], null, [0.2, 3, 3]]],\n      XZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0, 0.85], null, [3, 0.2, 3]]],\n      XYZX: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 0, 1.1]]],\n    }\n\n    const helperScale = {\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    // Creates an Object3D with gizmos described in custom hierarchy definition.\n    // this is nearly impossible to Type so i'm leaving it\n    const setupGizmo = (gizmoMap: any): Object3D => {\n      const gizmo = new Object3D()\n\n      for (let name in gizmoMap) {\n        for (let i = gizmoMap[name].length; i--; ) {\n          const object = gizmoMap[name][i][0].clone() as Mesh\n          const position = gizmoMap[name][i][1]\n          const rotation = gizmoMap[name][i][2]\n          const scale = gizmoMap[name][i][3]\n          const tag = gizmoMap[name][i][4]\n\n          // name and tag properties are essential for picking and updating logic.\n          object.name = name\n          // @ts-ignore\n          object.tag = tag\n\n          if (position) {\n            object.position.set(position[0], position[1], position[2])\n          }\n\n          if (rotation) {\n            object.rotation.set(rotation[0], rotation[1], rotation[2])\n          }\n\n          if (scale) {\n            object.scale.set(scale[0], scale[1], scale[2])\n          }\n\n          object.updateMatrix()\n\n          const tempGeometry = object.geometry.clone()\n          tempGeometry.applyMatrix4(object.matrix)\n          object.geometry = tempGeometry\n          object.renderOrder = Infinity\n\n          object.position.set(0, 0, 0)\n          object.rotation.set(0, 0, 0)\n          object.scale.set(1, 1, 1)\n\n          gizmo.add(object)\n        }\n      }\n\n      return gizmo\n    }\n\n    this.gizmo = {} as TransformControlsGizmoPrivateGizmos\n    this.picker = {} as TransformControlsGizmoPrivateGizmos\n    this.helper = {} as TransformControlsGizmoPrivateGizmos\n\n    this.add((this.gizmo['translate'] = setupGizmo(gizmoTranslate)))\n    this.add((this.gizmo['rotate'] = setupGizmo(gizmoRotate)))\n    this.add((this.gizmo['scale'] = setupGizmo(gizmoScale)))\n    this.add((this.picker['translate'] = setupGizmo(pickerTranslate)))\n    this.add((this.picker['rotate'] = setupGizmo(pickerRotate)))\n    this.add((this.picker['scale'] = setupGizmo(pickerScale)))\n    this.add((this.helper['translate'] = setupGizmo(helperTranslate)))\n    this.add((this.helper['rotate'] = setupGizmo(helperRotate)))\n    this.add((this.helper['scale'] = setupGizmo(helperScale)))\n\n    // Pickers should be hidden always\n\n    this.picker['translate'].visible = false\n    this.picker['rotate'].visible = false\n    this.picker['scale'].visible = false\n  }\n\n  // updateMatrixWorld will update transformations and appearance of individual handles\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    if (this.mode === 'scale') {\n      space = 'local' // scale always oriented to local rotation\n    }\n\n    const quaternion = space === 'local' ? this.worldQuaternion : this.identityQuaternion\n\n    // Show only gizmos for current transform mode\n\n    this.gizmo['translate'].visible = this.mode === 'translate'\n    this.gizmo['rotate'].visible = this.mode === 'rotate'\n    this.gizmo['scale'].visible = this.mode === 'scale'\n\n    this.helper['translate'].visible = this.mode === 'translate'\n    this.helper['rotate'].visible = this.mode === 'rotate'\n    this.helper['scale'].visible = this.mode === 'scale'\n\n    let handles: Array<Object3D & { tag?: string }> = []\n    handles = handles.concat(this.picker[this.mode].children)\n    handles = handles.concat(this.gizmo[this.mode].children)\n    handles = handles.concat(this.helper[this.mode].children)\n\n    for (let i = 0; i < handles.length; i++) {\n      const handle = handles[i]\n\n      // hide aligned to camera\n\n      handle.visible = true\n      handle.rotation.set(0, 0, 0)\n      handle.position.copy(this.worldPosition)\n\n      let factor\n\n      if ((this.camera as OrthographicCamera).isOrthographicCamera) {\n        factor =\n          ((this.camera as OrthographicCamera).top - (this.camera as OrthographicCamera).bottom) /\n          (this.camera as OrthographicCamera).zoom\n      } else {\n        factor =\n          this.worldPosition.distanceTo(this.cameraPosition) *\n          Math.min((1.9 * Math.tan((Math.PI * (this.camera as PerspectiveCamera).fov) / 360)) / this.camera.zoom, 7)\n      }\n\n      handle.scale.set(1, 1, 1).multiplyScalar((factor * this.size) / 7)\n\n      // TODO: simplify helpers and consider decoupling from gizmo\n\n      if (handle.tag === 'helper') {\n        handle.visible = false\n\n        if (handle.name === 'AXIS') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = !!this.axis\n\n          if (this.axis === 'X') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Y') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, Math.PI / 2))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Z') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'XYZE') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            this.alignVector.copy(this.rotationAxis)\n            handle.quaternion.setFromRotationMatrix(\n              this.lookAtMatrix.lookAt(this.zeroVector, this.alignVector, this.unitY),\n            )\n            handle.quaternion.multiply(this.tempQuaternion)\n            handle.visible = this.dragging\n          }\n\n          if (this.axis === 'E') {\n            handle.visible = false\n          }\n        } else if (handle.name === 'START') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = this.dragging\n        } else if (handle.name === 'END') {\n          handle.position.copy(this.worldPosition)\n          handle.visible = this.dragging\n        } else if (handle.name === 'DELTA') {\n          handle.position.copy(this.worldPositionStart)\n          handle.quaternion.copy(this.worldQuaternionStart)\n          this.tempVector\n            .set(1e-10, 1e-10, 1e-10)\n            .add(this.worldPositionStart)\n            .sub(this.worldPosition)\n            .multiplyScalar(-1)\n          this.tempVector.applyQuaternion(this.worldQuaternionStart.clone().invert())\n          handle.scale.copy(this.tempVector)\n          handle.visible = this.dragging\n        } else {\n          handle.quaternion.copy(quaternion)\n\n          if (this.dragging) {\n            handle.position.copy(this.worldPositionStart)\n          } else {\n            handle.position.copy(this.worldPosition)\n          }\n\n          if (this.axis) {\n            handle.visible = this.axis.search(handle.name) !== -1\n          }\n        }\n\n        // If updating helper, skip rest of the loop\n        continue\n      }\n\n      // Align handles to current local or world rotation\n\n      handle.quaternion.copy(quaternion)\n\n      if (this.mode === 'translate' || this.mode === 'scale') {\n        // Hide translate and scale axis facing the camera\n\n        const AXIS_HIDE_TRESHOLD = 0.99\n        const PLANE_HIDE_TRESHOLD = 0.2\n        const AXIS_FLIP_TRESHOLD = 0.0\n\n        if (handle.name === 'X' || handle.name === 'XYZX') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Y' || handle.name === 'XYZY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Z' || handle.name === 'XYZZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'YZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        // Flip translate and scale axis ocluded behind another axis\n\n        if (handle.name.search('X') !== -1) {\n          if (this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.x *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Y') !== -1) {\n          if (this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.y *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Z') !== -1) {\n          if (this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.z *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n      } else if (this.mode === 'rotate') {\n        // Align handles to current local or world rotation\n\n        this.tempQuaternion2.copy(quaternion)\n        this.alignVector.copy(this.eye).applyQuaternion(this.tempQuaternion.copy(quaternion).invert())\n\n        if (handle.name.search('E') !== -1) {\n          handle.quaternion.setFromRotationMatrix(this.lookAtMatrix.lookAt(this.eye, this.zeroVector, this.unitY))\n        }\n\n        if (handle.name === 'X') {\n          this.tempQuaternion.setFromAxisAngle(this.unitX, Math.atan2(-this.alignVector.y, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Y') {\n          this.tempQuaternion.setFromAxisAngle(this.unitY, Math.atan2(this.alignVector.x, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Z') {\n          this.tempQuaternion.setFromAxisAngle(this.unitZ, Math.atan2(this.alignVector.y, this.alignVector.x))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n      }\n\n      // Hide disabled axes\n      handle.visible = handle.visible && (handle.name.indexOf('X') === -1 || this.showX)\n      handle.visible = handle.visible && (handle.name.indexOf('Y') === -1 || this.showY)\n      handle.visible = handle.visible && (handle.name.indexOf('Z') === -1 || this.showZ)\n      handle.visible = handle.visible && (handle.name.indexOf('E') === -1 || (this.showX && this.showY && this.showZ))\n\n      // highlight selected axis\n\n      //@ts-ignore\n      handle.material.tempOpacity = handle.material.tempOpacity || handle.material.opacity\n      //@ts-ignore\n      handle.material.tempColor = handle.material.tempColor || handle.material.color.clone()\n      //@ts-ignore\n      handle.material.color.copy(handle.material.tempColor)\n      //@ts-ignore\n      handle.material.opacity = handle.material.tempOpacity\n\n      if (!this.enabled) {\n        //@ts-ignore\n        handle.material.opacity *= 0.5\n        //@ts-ignore\n        handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n      } else if (this.axis) {\n        if (handle.name === this.axis) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else if (\n          this.axis.split('').some(function (a) {\n            return handle.name === a\n          })\n        ) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else {\n          //@ts-ignore\n          handle.material.opacity *= 0.25\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        }\n      }\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nclass TransformControlsPlane extends Mesh<PlaneGeometry, MeshBasicMaterial> {\n  private isTransformControlsPlane = true\n  public type = 'TransformControlsPlane'\n\n  constructor() {\n    super(\n      new PlaneGeometry(100000, 100000, 2, 2),\n      new MeshBasicMaterial({\n        visible: false,\n        wireframe: true,\n        side: DoubleSide,\n        transparent: true,\n        opacity: 0.1,\n        toneMapped: false,\n      }),\n    )\n  }\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private tempVector = new Vector3()\n  private dirVector = new Vector3()\n  private alignVector = new Vector3()\n  private tempMatrix = new Matrix4()\n  private identityQuaternion = new Quaternion()\n\n  // these are set from parent class TransformControls\n  private cameraQuaternion = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    this.position.copy(this.worldPosition)\n\n    if (this.mode === 'scale') space = 'local' // scale always oriented to local rotation\n\n    this.unitX.set(1, 0, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitY.set(0, 1, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitZ.set(0, 0, 1).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n\n    // Align the plane for current transform mode, axis and space.\n\n    this.alignVector.copy(this.unitY)\n\n    switch (this.mode) {\n      case 'translate':\n      case 'scale':\n        switch (this.axis) {\n          case 'X':\n            this.alignVector.copy(this.eye).cross(this.unitX)\n            this.dirVector.copy(this.unitX).cross(this.alignVector)\n            break\n          case 'Y':\n            this.alignVector.copy(this.eye).cross(this.unitY)\n            this.dirVector.copy(this.unitY).cross(this.alignVector)\n            break\n          case 'Z':\n            this.alignVector.copy(this.eye).cross(this.unitZ)\n            this.dirVector.copy(this.unitZ).cross(this.alignVector)\n            break\n          case 'XY':\n            this.dirVector.copy(this.unitZ)\n            break\n          case 'YZ':\n            this.dirVector.copy(this.unitX)\n            break\n          case 'XZ':\n            this.alignVector.copy(this.unitZ)\n            this.dirVector.copy(this.unitY)\n            break\n          case 'XYZ':\n          case 'E':\n            this.dirVector.set(0, 0, 0)\n            break\n        }\n\n        break\n      case 'rotate':\n      default:\n        // special case for rotate\n        this.dirVector.set(0, 0, 0)\n    }\n\n    if (this.dirVector.length() === 0) {\n      // If in rotate mode, make the plane parallel to camera\n      this.quaternion.copy(this.cameraQuaternion)\n    } else {\n      this.tempMatrix.lookAt(this.tempVector.set(0, 0, 0), this.dirVector, this.alignVector)\n\n      this.quaternion.setFromRotationMatrix(this.tempMatrix)\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nexport { TransformControls, TransformControlsGizmo, TransformControlsPlane }\n"], "names": ["Object3D", "Raycaster", "Vector3", "Quaternion", "<PERSON>uler", "Matrix4", "Color", "MeshBasicMaterial", "DoubleSide", "LineBasicMaterial", "CylinderGeometry", "BoxGeometry", "BufferGeometry", "Float32BufferAttribute", "<PERSON><PERSON>", "Line", "OctahedronGeometry", "PlaneGeometry", "TorusGeometry", "SphereGeometry"], "mappings": ";;;;;;;;;AAmCA,MAAM,0BAA2DA,MAAAA,SAAS;AAAA,EA0ExE,YAAY,QAAiB,YAAqC;AAC1D;AA1EQ,+CAAsB;AAE/B,mCAAU;AAET;AAEA,qCAAY,IAAIC,MAAAA;AAEhB;AACA;AAEA,sCAAa,IAAIC,MAAAA;AACjB,uCAAc,IAAIA,MAAAA;AAClB,0CAAiB,IAAIC,MAAAA;AACrB,gCAAO;AAAA,MACb,GAAG,IAAID,MAAQ,QAAA,GAAG,GAAG,CAAC;AAAA,MACtB,GAAG,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAAA,MACtB,GAAG,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAAA,IAAA;AAGhB,sCAAa,IAAIA,MAAAA;AACjB,oCAAW,IAAIA,MAAAA;AACf,kCAAS,IAAIA,MAAAA;AACb,wCAAe,IAAIA,MAAAA;AACnB,qCAAY,IAAIA,MAAAA;AAChB,mCAAU,IAAIA,MAAAA;AACd,yCAAgB;AAEhB,0CAAiB,IAAIA,MAAAA;AACrB,4CAAmB,IAAIC,MAAAA;AACvB,uCAAc,IAAID,MAAAA;AAElB,0CAAiB,IAAIA,MAAAA;AACrB,4CAAmB,IAAIC,MAAAA;AACvB,+CAAsB,IAAIA,MAAAA;AAC1B,uCAAc,IAAID,MAAAA;AAElB,8CAAqB,IAAIA,MAAAA;AACzB,gDAAuB,IAAIC,MAAAA;AAC3B,2CAAkB,IAAID,MAAAA;AAEtB,yCAAgB,IAAIA,MAAAA;AACpB,2CAAkB,IAAIC,MAAAA;AACtB,8CAAqB,IAAIA,MAAAA;AACzB,sCAAa,IAAID,MAAAA;AAEjB,+BAAM,IAAIA,MAAAA;AAEV,yCAAgB,IAAIA,MAAAA;AACpB,2CAAkB,IAAIC,MAAAA;AACtB,sCAAa,IAAID,MAAAA;AAEjB;AACA;AACA,mCAAU;AACV,gCAAsB;AACtB,gCAAyC;AACzC,2CAAiC;AACjC,wCAA8B;AAC9B,qCAA2B;AAC3B,iCAAQ;AACR,gCAAO;AACP,oCAAW;AACX,iCAAQ;AACR,iCAAQ;AACR,iCAAQ;AAGR;AAAA,uCAAc,EAAE,MAAM;AACtB,0CAAiB,EAAE,MAAM,aAAa,MAAM,KAAK;AACjD,wCAAe,EAAE,MAAM,WAAW,MAAM,KAAK;AAC7C,6CAAoB,EAAE,MAAM;AAyE5B,kDAAyB,CAC/B,QACA,WACA,qBACyB;AACzB,YAAM,mBAAmB,UAAU,gBAAgB,QAAQ,IAAI;AAE/D,eAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,YAAI,iBAAiB,CAAC,EAAE,OAAO,WAAW,kBAAkB;AAC1D,iBAAO,iBAAiB,CAAC;AAAA,QAC3B;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAIF;AAAA,kCAAS,CAAC,WAA2B;AAC1C,WAAK,SAAS;AACd,WAAK,UAAU;AAER,aAAA;AAAA,IAAA;AAIF;AAAA,kCAAS,MAAY;AAC1B,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,OAAO;AAEL,aAAA;AAAA,IAAA;AAIF;AAAA,iCAAQ,MAAY;AACzB,UAAI,CAAC,KAAK;AAAgB,eAAA;AAE1B,UAAI,KAAK,UAAU;AACb,YAAA,KAAK,WAAW,QAAW;AAC7B,eAAK,OAAO,SAAS,KAAK,KAAK,aAAa;AAC5C,eAAK,OAAO,WAAW,KAAK,KAAK,eAAe;AAChD,eAAK,OAAO,MAAM,KAAK,KAAK,UAAU;AAEjC,eAAA,cAAc,KAAK,WAAW;AAE9B,eAAA,cAAc,KAAK,iBAAiB;AACpC,eAAA,WAAW,KAAK,KAAK,QAAQ;AAAA,QACpC;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAGF,6CAAoB,MAAY;AACjC,UAAA,KAAK,WAAW,QAAW;AAC7B,aAAK,OAAO;AAER,YAAA,KAAK,OAAO,WAAW,MAAM;AAC/B,kBAAQ,MAAM,8EAA8E;AAAA,QAAA,OACvF;AACA,eAAA,OAAO,OAAO,YAAY,UAAU,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,WAAW;AAAA,QACvG;AAEK,aAAA,OAAO,YAAY,UAAU,KAAK,eAAe,KAAK,iBAAiB,KAAK,UAAU;AAE3F,aAAK,oBAAoB,KAAK,KAAK,gBAAgB,EAAE;AACrD,aAAK,mBAAmB,KAAK,KAAK,eAAe,EAAE;MACrD;AAEA,WAAK,OAAO;AACP,WAAA,OAAO,YAAY,UAAU,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,WAAW;AAEzF,WAAA,IAAI,KAAK,KAAK,cAAc,EAAE,IAAI,KAAK,aAAa,EAAE;AAE3D,YAAM,kBAAkB;AAAA,IAAA;AAGlB,wCAAe,CAAC,YAAkD;AACxE,UAAI,KAAK,WAAW,UAAa,KAAK,aAAa;AAAM;AAEzD,WAAK,UAAU,cAAe,SAAgC,KAAK,MAAM;AAEnE,YAAA,YAAY,KAAK,uBAAuB,KAAK,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,SAAS;AAE1F,UAAI,WAAW;AACR,aAAA,OAAO,UAAU,OAAO;AAAA,MAAA,OACxB;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IAAA;AAGM,uCAAc,CAAC,YAAkD;AACvE,UAAI,KAAK,WAAW,UAAa,KAAK,aAAa,QAAQ,QAAQ,WAAW;AAAG;AAE7E,UAAA,KAAK,SAAS,MAAM;AACtB,aAAK,UAAU,cAAe,SAAgC,KAAK,MAAM;AAEzE,cAAM,iBAAiB,KAAK,uBAAuB,KAAK,OAAO,KAAK,WAAW,IAAI;AAEnF,YAAI,gBAAgB;AAClB,cAAI,QAAQ,KAAK;AAEb,cAAA,KAAK,SAAS,SAAS;AACjB,oBAAA;AAAA,UAAA,WACC,KAAK,SAAS,OAAO,KAAK,SAAS,UAAU,KAAK,SAAS,OAAO;AACnE,oBAAA;AAAA,UACV;AAEA,cAAI,UAAU,WAAW,KAAK,SAAS,UAAU;AAC/C,kBAAM,OAAO,KAAK;AAEd,gBAAA,KAAK,SAAS,OAAO;AAAW,mBAAA,OAAO,SAAS,IAAI,KAAK,MAAM,KAAK,OAAO,SAAS,IAAI,IAAI,IAAI;AAChG,gBAAA,KAAK,SAAS,OAAO;AAAW,mBAAA,OAAO,SAAS,IAAI,KAAK,MAAM,KAAK,OAAO,SAAS,IAAI,IAAI,IAAI;AAChG,gBAAA,KAAK,SAAS,OAAO;AAAW,mBAAA,OAAO,SAAS,IAAI,KAAK,MAAM,KAAK,OAAO,SAAS,IAAI,IAAI,IAAI;AAAA,UACtG;AAEA,eAAK,OAAO;AAER,cAAA,KAAK,OAAO,QAAQ;AACjB,iBAAA,OAAO,OAAO;UACrB;AAEA,eAAK,cAAc,KAAK,KAAK,OAAO,QAAQ;AAC5C,eAAK,gBAAgB,KAAK,KAAK,OAAO,UAAU;AAChD,eAAK,WAAW,KAAK,KAAK,OAAO,KAAK;AAEjC,eAAA,OAAO,YAAY,UAAU,KAAK,oBAAoB,KAAK,sBAAsB,KAAK,eAAe;AAE1G,eAAK,WAAW,KAAK,eAAe,KAAK,EAAE,IAAI,KAAK,kBAAkB;AAAA,QACxE;AAEA,aAAK,WAAW;AACX,aAAA,eAAe,OAAO,KAAK;AAE3B,aAAA,cAAc,KAAK,cAAc;AAAA,MACxC;AAAA,IAAA;AAGM,uCAAc,CAAC,YAAkD;AACvE,YAAM,OAAO,KAAK;AAClB,YAAM,OAAO,KAAK;AAClB,YAAM,SAAS,KAAK;AACpB,UAAI,QAAQ,KAAK;AAEjB,UAAI,SAAS,SAAS;AACZ,gBAAA;AAAA,MAAA,WACC,SAAS,OAAO,SAAS,UAAU,SAAS,OAAO;AACpD,gBAAA;AAAA,MACV;AAEI,UAAA,WAAW,UAAa,SAAS,QAAQ,KAAK,aAAa,SAAS,QAAQ,WAAW;AAAI;AAE/F,WAAK,UAAU,cAAe,SAAgC,KAAK,MAAM;AAEzE,YAAM,iBAAiB,KAAK,uBAAuB,KAAK,OAAO,KAAK,WAAW,IAAI;AAEnF,UAAI,CAAC;AAAgB;AAErB,WAAK,SAAS,KAAK,eAAe,KAAK,EAAE,IAAI,KAAK,kBAAkB;AAEpE,UAAI,SAAS,aAAa;AAGxB,aAAK,OAAO,KAAK,KAAK,QAAQ,EAAE,IAAI,KAAK,UAAU;AAE/C,YAAA,UAAU,WAAW,SAAS,OAAO;AAClC,eAAA,OAAO,gBAAgB,KAAK,kBAAkB;AAAA,QACrD;AAEI,YAAA,KAAK,QAAQ,GAAG,MAAM;AAAI,eAAK,OAAO,IAAI;AAC1C,YAAA,KAAK,QAAQ,GAAG,MAAM;AAAI,eAAK,OAAO,IAAI;AAC1C,YAAA,KAAK,QAAQ,GAAG,MAAM;AAAI,eAAK,OAAO,IAAI;AAE1C,YAAA,UAAU,WAAW,SAAS,OAAO;AACvC,eAAK,OAAO,gBAAgB,KAAK,eAAe,EAAE,OAAO,KAAK,WAAW;AAAA,QAAA,OACpE;AACL,eAAK,OAAO,gBAAgB,KAAK,mBAAmB,EAAE,OAAO,KAAK,WAAW;AAAA,QAC/E;AAEA,eAAO,SAAS,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK,aAAa;AAIxD,YAAI,KAAK,iBAAiB;AACxB,cAAI,UAAU,SAAS;AACd,mBAAA,SAAS,gBAAgB,KAAK,eAAe,KAAK,KAAK,eAAe,EAAE,OAAA,CAAQ;AAEvF,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEA,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEA,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEO,mBAAA,SAAS,gBAAgB,KAAK,eAAe;AAAA,UACtD;AAEA,cAAI,UAAU,SAAS;AACrB,gBAAI,OAAO,QAAQ;AACV,qBAAA,SAAS,IAAI,KAAK,WAAW,sBAAsB,OAAO,OAAO,WAAW,CAAC;AAAA,YACtF;AAEA,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEA,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEA,gBAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AACpB,qBAAA,SAAS,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,KAAK,eAAe,IAAI,KAAK;AAAA,YAClF;AAEA,gBAAI,OAAO,QAAQ;AACV,qBAAA,SAAS,IAAI,KAAK,WAAW,sBAAsB,OAAO,OAAO,WAAW,CAAC;AAAA,YACtF;AAAA,UACF;AAAA,QACF;AAAA,MAAA,WACS,SAAS,SAAS;AAC3B,YAAI,KAAK,OAAO,KAAK,MAAM,IAAI;AAC7B,cAAI,IAAI,KAAK,SAAS,OAAW,IAAA,KAAK,WAAW;AAEjD,cAAI,KAAK,SAAS,IAAI,KAAK,UAAU,IAAI;AAAQ,iBAAA;AAEjD,eAAK,YAAY,IAAI,GAAG,GAAG,CAAC;AAAA,QAAA,OACvB;AACA,eAAA,WAAW,KAAK,KAAK,UAAU;AAC/B,eAAA,YAAY,KAAK,KAAK,QAAQ;AAE9B,eAAA,WAAW,gBAAgB,KAAK,kBAAkB;AAClD,eAAA,YAAY,gBAAgB,KAAK,kBAAkB;AAEnD,eAAA,YAAY,OAAO,KAAK,UAAU;AAEvC,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,iBAAK,YAAY,IAAI;AAAA,UACvB;AAEA,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,iBAAK,YAAY,IAAI;AAAA,UACvB;AAEA,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,iBAAK,YAAY,IAAI;AAAA,UACvB;AAAA,QACF;AAIA,eAAO,MAAM,KAAK,KAAK,UAAU,EAAE,SAAS,KAAK,WAAW;AAExD,YAAA,KAAK,aAAa,KAAK,QAAQ;AACjC,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,iBAAK,OAAO,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,IAAI,KAAK,SAAS,IAAI,KAAK,aAAa,KAAK;AAAA,UAC7F;AAEA,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,mBAAO,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,IAAI,KAAK,SAAS,IAAI,KAAK,aAAa,KAAK;AAAA,UACxF;AAEA,cAAI,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,mBAAO,MAAM,IAAI,KAAK,MAAM,OAAO,MAAM,IAAI,KAAK,SAAS,IAAI,KAAK,aAAa,KAAK;AAAA,UACxF;AAAA,QACF;AAAA,MAAA,WACS,SAAS,UAAU;AAC5B,aAAK,OAAO,KAAK,KAAK,QAAQ,EAAE,IAAI,KAAK,UAAU;AAE7C,cAAA,iBACJ,KAAK,KAAK,cAAc,WAAW,KAAK,WAAW,sBAAsB,KAAK,OAAO,WAAW,CAAC;AAEnG,YAAI,SAAS,KAAK;AACX,eAAA,aAAa,KAAK,KAAK,GAAG;AAC/B,eAAK,gBAAgB,KAAK,SAAS,QAAQ,KAAK,UAAU;AAE1D,eAAK,UAAU,KAAK,KAAK,UAAU,EAAE;AACrC,eAAK,QAAQ,KAAK,KAAK,QAAQ,EAAE;AAEjC,eAAK,iBAAiB,KAAK,QAAQ,MAAM,KAAK,SAAS,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,QAAA,WACxE,SAAS,QAAQ;AACrB,eAAA,aAAa,KAAK,KAAK,MAAM,EAAE,MAAM,KAAK,GAAG,EAAE;AACpD,eAAK,gBAAgB,KAAK,OAAO,IAAI,KAAK,WAAW,KAAK,KAAK,YAAY,EAAE,MAAM,KAAK,GAAG,CAAC,IAAI;AAAA,QAAA,WACvF,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AACvD,eAAK,aAAa,KAAK,KAAK,KAAK,IAAI,CAAC;AAEtC,eAAK,WAAW,KAAK,KAAK,KAAK,IAAI,CAAC;AAEpC,cAAI,UAAU,SAAS;AAChB,iBAAA,WAAW,gBAAgB,KAAK,eAAe;AAAA,UACtD;AAEA,eAAK,gBAAgB,KAAK,OAAO,IAAI,KAAK,WAAW,MAAM,KAAK,GAAG,EAAE,UAAW,CAAA,IAAI;AAAA,QACtF;AAIA,YAAI,KAAK,cAAc;AAChB,eAAA,gBAAgB,KAAK,MAAM,KAAK,gBAAgB,KAAK,YAAY,IAAI,KAAK;AAAA,QACjF;AAGA,YAAI,UAAU,WAAW,SAAS,OAAO,SAAS,QAAQ;AACjD,iBAAA,WAAW,KAAK,KAAK,eAAe;AACpC,iBAAA,WACJ,SAAS,KAAK,eAAe,iBAAiB,KAAK,cAAc,KAAK,aAAa,CAAC,EACpF,UAAU;AAAA,QAAA,OACR;AACA,eAAA,aAAa,gBAAgB,KAAK,mBAAmB;AACnD,iBAAA,WAAW,KAAK,KAAK,eAAe,iBAAiB,KAAK,cAAc,KAAK,aAAa,CAAC;AAClG,iBAAO,WAAW,SAAS,KAAK,eAAe,EAAE;QACnD;AAAA,MACF;AAGK,WAAA,cAAc,KAAK,WAAW;AAE9B,WAAA,cAAc,KAAK,iBAAiB;AAAA,IAAA;AAGnC,qCAAY,CAAC,YAAkD;AACrE,UAAI,QAAQ,WAAW;AAAG;AAE1B,UAAI,KAAK,YAAY,KAAK,SAAS,MAAM;AAClC,aAAA,aAAa,OAAO,KAAK;AAEzB,aAAA,cAAc,KAAK,YAAY;AAAA,MACtC;AAEA,WAAK,WAAW;AAChB,WAAK,OAAO;AAAA,IAAA;AAGN,sCAAa,CAAC,UAAiD;;AACrE,UAAI,KAAK,gBAAc,UAAK,WAAW,kBAAhB,mBAA+B,qBAAoB;AACjE,eAAA;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,UACH,QAAS,MAAqB;AAAA,QAAA;AAAA,MAChC,OACK;AACL,cAAM,UAAW,MAAqB,iBACjC,MAAqB,eAAe,CAAC,IACrC;AAEC,cAAA,OAAO,KAAK,WAAY,sBAAsB;AAE7C,eAAA;AAAA,UACL,IAAK,QAAQ,UAAU,KAAK,QAAQ,KAAK,QAAS,IAAI;AAAA,UACtD,GAAI,EAAE,QAAQ,UAAU,KAAK,OAAO,KAAK,SAAU,IAAI;AAAA,UACvD,QAAS,MAAqB;AAAA,QAAA;AAAA,MAElC;AAAA,IAAA;AAGM,0CAAiB,CAAC,UAAuB;AAC/C,UAAI,CAAC,KAAK;AAAS;AAEnB,cAAS,MAAuB,aAAa;AAAA,QAC3C,KAAK;AAAA,QACL,KAAK;AACH,eAAK,aAAa,KAAK,WAAW,KAAK,CAAC;AACxC;AAAA,MACJ;AAAA,IAAA;AAGM,yCAAgB,CAAC,UAAuB;AAC9C,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK;AAAY;AAElC,WAAA,WAAW,MAAM,cAAc;AACpC,WAAK,WAAW,cAAc,iBAAiB,eAAe,KAAK,aAAa;AAChF,WAAK,aAAa,KAAK,WAAW,KAAK,CAAC;AACxC,WAAK,YAAY,KAAK,WAAW,KAAK,CAAC;AAAA,IAAA;AAGjC,yCAAgB,CAAC,UAAuB;AAC9C,UAAI,CAAC,KAAK;AAAS;AAEnB,WAAK,YAAY,KAAK,WAAW,KAAK,CAAC;AAAA,IAAA;AAGjC,uCAAc,CAAC,UAAuB;AAC5C,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK;AAAY;AAElC,WAAA,WAAW,MAAM,cAAe;AACrC,WAAK,WAAW,cAAc,oBAAoB,eAAe,KAAK,aAAa;AAEnF,WAAK,UAAU,KAAK,WAAW,KAAK,CAAC;AAAA,IAAA;AAGhC,mCAAU,MAAiC,KAAK;AAEhD,mCAAU,CAAC,SAA0C;AAC1D,WAAK,OAAO;AAAA,IAAA;AAGP,8CAAqB,CAAC,oBAAkC;AAC7D,WAAK,kBAAkB;AAAA,IAAA;AAGlB,2CAAkB,CAAC,iBAA+B;AACvD,WAAK,eAAe;AAAA,IAAA;AAGf,wCAAe,CAAC,cAA4B;AACjD,WAAK,YAAY;AAAA,IAAA;AAGZ,mCAAU,CAAC,SAAuB;AACvC,WAAK,OAAO;AAAA,IAAA;AAGP,oCAAW,CAAC,UAAwB;AACzC,WAAK,QAAQ;AAAA,IAAA;AAGR,kCAAS,MAAY;AAClB,cAAA;AAAA,QACN;AAAA,MAAA;AAAA,IACF;AAGK,mCAAU,CAAC,eAAkC;AAClD,UAAK,eAAuB,UAAU;AAC5B,gBAAA;AAAA,UACN;AAAA,QAAA;AAAA,MAEJ;AACA,WAAK,aAAa;AAElB,WAAK,WAAW,iBAAiB,eAAe,KAAK,aAAa;AAClE,WAAK,WAAW,iBAAiB,eAAe,KAAK,cAAc;AACnE,WAAK,WAAW,cAAc,iBAAiB,aAAa,KAAK,WAAW;AAAA,IAAA;AAGvE,mCAAU,MAAY;;AAC3B,iBAAK,eAAL,mBAAiB,oBAAoB,eAAe,KAAK;AACzD,iBAAK,eAAL,mBAAiB,oBAAoB,eAAe,KAAK;AACzD,uBAAK,eAAL,mBAAiB,kBAAjB,mBAAgC,oBAAoB,eAAe,KAAK;AACxE,uBAAK,eAAL,mBAAiB,kBAAjB,mBAAgC,oBAAoB,aAAa,KAAK;AAEjE,WAAA,SAAS,CAAC,UAAU;AACvB,cAAM,OAAO;AACb,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS;QAChB;AACA,YAAI,KAAK,UAAU;AACjB,eAAK,SAAS;QAChB;AAAA,MAAA,CACD;AAAA,IAAA;AAzgBD,SAAK,aAAa;AAClB,SAAK,SAAS;AAET,SAAA,QAAQ,IAAI;AACZ,SAAA,IAAI,KAAK,KAAK;AAEd,SAAA,QAAQ,IAAI;AACZ,SAAA,IAAI,KAAK,KAAK;AAGb,UAAA,iBAAiB,CAAS,UAAkB,iBAA+B;AAC/E,UAAI,YAAY;AAET,aAAA,eAAe,MAAM,UAAU;AAAA,QACpC,KAAK,WAAY;AACR,iBAAA,cAAc,SAAY,YAAY;AAAA,QAC/C;AAAA,QAEA,KAAK,SAAU,OAAO;AACpB,cAAI,cAAc,OAAO;AACX,wBAAA;AACP,iBAAA,MAAM,QAAQ,IAAI;AAClB,iBAAA,MAAM,QAAQ,IAAI;AAEvB,iBAAK,cAAc,EAAE,MAAM,WAAW,YAAY,OAAc;AAC3D,iBAAA,cAAc,KAAK,WAAW;AAAA,UACrC;AAAA,QACF;AAAA,MAAA,CACD;AAGD,WAAK,QAAQ,IAAI;AAEZ,WAAA,MAAM,QAAQ,IAAI;AAElB,WAAA,MAAM,QAAQ,IAAI;AAAA,IAAA;AAGV,mBAAA,UAAU,KAAK,MAAM;AACrB,mBAAA,UAAU,KAAK,MAAM;AACrB,mBAAA,WAAW,KAAK,OAAO;AACvB,mBAAA,QAAQ,KAAK,IAAI;AACjB,mBAAA,QAAQ,KAAK,IAAI;AACjB,mBAAA,mBAAmB,KAAK,eAAe;AACvC,mBAAA,gBAAgB,KAAK,YAAY;AACjC,mBAAA,aAAa,KAAK,SAAS;AAC3B,mBAAA,SAAS,KAAK,KAAK;AACnB,mBAAA,QAAQ,KAAK,IAAI;AACjB,mBAAA,YAAY,KAAK,QAAQ;AACzB,mBAAA,SAAS,KAAK,KAAK;AACnB,mBAAA,SAAS,KAAK,KAAK;AACnB,mBAAA,SAAS,KAAK,KAAK;AACnB,mBAAA,iBAAiB,KAAK,aAAa;AACnC,mBAAA,sBAAsB,KAAK,kBAAkB;AAC7C,mBAAA,mBAAmB,KAAK,eAAe;AACvC,mBAAA,wBAAwB,KAAK,oBAAoB;AACjD,mBAAA,kBAAkB,KAAK,cAAc;AACrC,mBAAA,oBAAoB,KAAK,gBAAgB;AACzC,mBAAA,cAAc,KAAK,UAAU;AAC7B,mBAAA,YAAY,KAAK,QAAQ;AACzB,mBAAA,gBAAgB,KAAK,YAAY;AACjC,mBAAA,iBAAiB,KAAK,aAAa;AACnC,mBAAA,OAAO,KAAK,GAAG;AAG9B,QAAI,eAAe;AAAW,WAAK,QAAQ,UAAU;AAAA,EACvD;AAycF;AASA,MAAM,+BAA+BF,MAAAA,SAAS;AAAA,EA6C5C,cAAc;AACN;AA7CA,oDAA2B;AAC5B,gCAAO;AAEN,sCAAa,IAAIE,MAAQ,QAAA,GAAG,GAAG,CAAC;AAChC,qCAAY,IAAIE,MAAAA;AAChB,uCAAc,IAAIF,MAAQ,QAAA,GAAG,GAAG,CAAC;AACjC,sCAAa,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAChC,wCAAe,IAAIG,MAAAA;AACnB,0CAAiB,IAAIF,MAAAA;AACrB,2CAAkB,IAAIA,MAAAA;AACtB,8CAAqB,IAAIA,MAAAA;AAEzB,iCAAQ,IAAID,MAAQ,QAAA,GAAG,GAAG,CAAC;AAC3B,iCAAQ,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAC3B,iCAAQ,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAE3B;AACD;AACC;AAGA;AAAA,wCAAe,IAAIA,MAAAA;AAEnB,0CAAiB,IAAIA,MAAAA;AAErB,8CAAqB,IAAIA,MAAAA;AACzB,gDAAuB,IAAIC,MAAAA;AAE3B,yCAAgB,IAAID,MAAAA;AACpB,2CAAkB,IAAIC,MAAAA;AAEtB,+BAAM,IAAID,MAAAA;AAEV,kCAAiD;AACjD,mCAAU;AACV,gCAAsB;AACtB,gCAAyC;AACzC,iCAAQ;AACR,gCAAO;AACP,oCAAW;AACX,iCAAQ;AACR,iCAAQ;AACR,iCAAQ;AA0VT;AAAA,6CAAoB,MAAY;AACrC,UAAI,QAAQ,KAAK;AAEb,UAAA,KAAK,SAAS,SAAS;AACjB,gBAAA;AAAA,MACV;AAEA,YAAM,aAAa,UAAU,UAAU,KAAK,kBAAkB,KAAK;AAInE,WAAK,MAAM,WAAW,EAAE,UAAU,KAAK,SAAS;AAChD,WAAK,MAAM,QAAQ,EAAE,UAAU,KAAK,SAAS;AAC7C,WAAK,MAAM,OAAO,EAAE,UAAU,KAAK,SAAS;AAE5C,WAAK,OAAO,WAAW,EAAE,UAAU,KAAK,SAAS;AACjD,WAAK,OAAO,QAAQ,EAAE,UAAU,KAAK,SAAS;AAC9C,WAAK,OAAO,OAAO,EAAE,UAAU,KAAK,SAAS;AAE7C,UAAI,UAA8C,CAAA;AAClD,gBAAU,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,EAAE,QAAQ;AACxD,gBAAU,QAAQ,OAAO,KAAK,MAAM,KAAK,IAAI,EAAE,QAAQ;AACvD,gBAAU,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,EAAE,QAAQ;AAExD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACjC,cAAA,SAAS,QAAQ,CAAC;AAIxB,eAAO,UAAU;AACjB,eAAO,SAAS,IAAI,GAAG,GAAG,CAAC;AACpB,eAAA,SAAS,KAAK,KAAK,aAAa;AAEnC,YAAA;AAEC,YAAA,KAAK,OAA8B,sBAAsB;AAC5D,oBACI,KAAK,OAA8B,MAAO,KAAK,OAA8B,UAC9E,KAAK,OAA8B;AAAA,QAAA,OACjC;AAEH,mBAAA,KAAK,cAAc,WAAW,KAAK,cAAc,IACjD,KAAK,IAAK,MAAM,KAAK,IAAK,KAAK,KAAM,KAAK,OAA6B,MAAO,GAAG,IAAK,KAAK,OAAO,MAAM,CAAC;AAAA,QAC7G;AAEO,eAAA,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,eAAgB,SAAS,KAAK,OAAQ,CAAC;AAI7D,YAAA,OAAO,QAAQ,UAAU;AAC3B,iBAAO,UAAU;AAEb,cAAA,OAAO,SAAS,QAAQ;AACnB,mBAAA,SAAS,KAAK,KAAK,kBAAkB;AACrC,mBAAA,UAAU,CAAC,CAAC,KAAK;AAEpB,gBAAA,KAAK,SAAS,KAAK;AAChB,mBAAA,eAAe,aAAa,KAAK,UAAU,IAAI,GAAG,GAAG,CAAC,CAAC;AAC5D,qBAAO,WAAW,KAAK,UAAU,EAAE,SAAS,KAAK,cAAc;AAE/D,kBAAI,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK;AAC/F,uBAAO,UAAU;AAAA,cACnB;AAAA,YACF;AAEI,gBAAA,KAAK,SAAS,KAAK;AAChB,mBAAA,eAAe,aAAa,KAAK,UAAU,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC;AACtE,qBAAO,WAAW,KAAK,UAAU,EAAE,SAAS,KAAK,cAAc;AAE/D,kBAAI,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK;AAC/F,uBAAO,UAAU;AAAA,cACnB;AAAA,YACF;AAEI,gBAAA,KAAK,SAAS,KAAK;AAChB,mBAAA,eAAe,aAAa,KAAK,UAAU,IAAI,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AACtE,qBAAO,WAAW,KAAK,UAAU,EAAE,SAAS,KAAK,cAAc;AAE/D,kBAAI,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK;AAC/F,uBAAO,UAAU;AAAA,cACnB;AAAA,YACF;AAEI,gBAAA,KAAK,SAAS,QAAQ;AACnB,mBAAA,eAAe,aAAa,KAAK,UAAU,IAAI,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AACjE,mBAAA,YAAY,KAAK,KAAK,YAAY;AACvC,qBAAO,WAAW;AAAA,gBAChB,KAAK,aAAa,OAAO,KAAK,YAAY,KAAK,aAAa,KAAK,KAAK;AAAA,cAAA;AAEjE,qBAAA,WAAW,SAAS,KAAK,cAAc;AAC9C,qBAAO,UAAU,KAAK;AAAA,YACxB;AAEI,gBAAA,KAAK,SAAS,KAAK;AACrB,qBAAO,UAAU;AAAA,YACnB;AAAA,UAAA,WACS,OAAO,SAAS,SAAS;AAC3B,mBAAA,SAAS,KAAK,KAAK,kBAAkB;AAC5C,mBAAO,UAAU,KAAK;AAAA,UAAA,WACb,OAAO,SAAS,OAAO;AACzB,mBAAA,SAAS,KAAK,KAAK,aAAa;AACvC,mBAAO,UAAU,KAAK;AAAA,UAAA,WACb,OAAO,SAAS,SAAS;AAC3B,mBAAA,SAAS,KAAK,KAAK,kBAAkB;AACrC,mBAAA,WAAW,KAAK,KAAK,oBAAoB;AAChD,iBAAK,WACF,IAAI,OAAO,OAAO,KAAK,EACvB,IAAI,KAAK,kBAAkB,EAC3B,IAAI,KAAK,aAAa,EACtB,eAAe,EAAE;AACpB,iBAAK,WAAW,gBAAgB,KAAK,qBAAqB,MAAM,EAAE,QAAQ;AACnE,mBAAA,MAAM,KAAK,KAAK,UAAU;AACjC,mBAAO,UAAU,KAAK;AAAA,UAAA,OACjB;AACE,mBAAA,WAAW,KAAK,UAAU;AAEjC,gBAAI,KAAK,UAAU;AACV,qBAAA,SAAS,KAAK,KAAK,kBAAkB;AAAA,YAAA,OACvC;AACE,qBAAA,SAAS,KAAK,KAAK,aAAa;AAAA,YACzC;AAEA,gBAAI,KAAK,MAAM;AACb,qBAAO,UAAU,KAAK,KAAK,OAAO,OAAO,IAAI,MAAM;AAAA,YACrD;AAAA,UACF;AAGA;AAAA,QACF;AAIO,eAAA,WAAW,KAAK,UAAU;AAEjC,YAAI,KAAK,SAAS,eAAe,KAAK,SAAS,SAAS;AAGtD,gBAAM,qBAAqB;AAC3B,gBAAM,sBAAsB;AAC5B,gBAAM,qBAAqB;AAE3B,cAAI,OAAO,SAAS,OAAO,OAAO,SAAS,QAAQ;AACjD,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,oBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,OAAO,OAAO,SAAS,QAAQ;AACjD,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,oBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,OAAO,OAAO,SAAS,QAAQ;AACjD,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,oBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEI,cAAA,OAAO,SAAS,MAAM;AACxB,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,qBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEI,cAAA,OAAO,SAAS,MAAM;AACxB,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,qBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEI,cAAA,OAAO,SAAS,MAAM;AACxB,gBACE,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,qBACxF;AACA,qBAAO,MAAM,IAAI,OAAO,OAAO,KAAK;AACpC,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAIA,cAAI,OAAO,KAAK,OAAO,GAAG,MAAM,IAAI;AAClC,gBAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,IAAI,oBAAoB;AAChG,kBAAA,OAAO,QAAQ,OAAO;AACxB,uBAAO,UAAU;AAAA,cAAA,OACZ;AACL,uBAAO,MAAM,KAAK;AAAA,cACpB;AAAA,YAAA,WACS,OAAO,QAAQ,OAAO;AAC/B,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEA,cAAI,OAAO,KAAK,OAAO,GAAG,MAAM,IAAI;AAClC,gBAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,IAAI,oBAAoB;AAChG,kBAAA,OAAO,QAAQ,OAAO;AACxB,uBAAO,UAAU;AAAA,cAAA,OACZ;AACL,uBAAO,MAAM,KAAK;AAAA,cACpB;AAAA,YAAA,WACS,OAAO,QAAQ,OAAO;AAC/B,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAEA,cAAI,OAAO,KAAK,OAAO,GAAG,MAAM,IAAI;AAClC,gBAAI,KAAK,YAAY,KAAK,KAAK,KAAK,EAAE,gBAAgB,UAAU,EAAE,IAAI,KAAK,GAAG,IAAI,oBAAoB;AAChG,kBAAA,OAAO,QAAQ,OAAO;AACxB,uBAAO,UAAU;AAAA,cAAA,OACZ;AACL,uBAAO,MAAM,KAAK;AAAA,cACpB;AAAA,YAAA,WACS,OAAO,QAAQ,OAAO;AAC/B,qBAAO,UAAU;AAAA,YACnB;AAAA,UACF;AAAA,QAAA,WACS,KAAK,SAAS,UAAU;AAG5B,eAAA,gBAAgB,KAAK,UAAU;AACpC,eAAK,YAAY,KAAK,KAAK,GAAG,EAAE,gBAAgB,KAAK,eAAe,KAAK,UAAU,EAAE,OAAQ,CAAA;AAE7F,cAAI,OAAO,KAAK,OAAO,GAAG,MAAM,IAAI;AAC3B,mBAAA,WAAW,sBAAsB,KAAK,aAAa,OAAO,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,UACzG;AAEI,cAAA,OAAO,SAAS,KAAK;AACvB,iBAAK,eAAe,iBAAiB,KAAK,OAAO,KAAK,MAAM,CAAC,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC,CAAC;AACpG,iBAAK,eAAe,oBAAoB,KAAK,iBAAiB,KAAK,cAAc;AAC1E,mBAAA,WAAW,KAAK,KAAK,cAAc;AAAA,UAC5C;AAEI,cAAA,OAAO,SAAS,KAAK;AACvB,iBAAK,eAAe,iBAAiB,KAAK,OAAO,KAAK,MAAM,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC,CAAC;AACnG,iBAAK,eAAe,oBAAoB,KAAK,iBAAiB,KAAK,cAAc;AAC1E,mBAAA,WAAW,KAAK,KAAK,cAAc;AAAA,UAC5C;AAEI,cAAA,OAAO,SAAS,KAAK;AACvB,iBAAK,eAAe,iBAAiB,KAAK,OAAO,KAAK,MAAM,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC,CAAC;AACnG,iBAAK,eAAe,oBAAoB,KAAK,iBAAiB,KAAK,cAAc;AAC1E,mBAAA,WAAW,KAAK,KAAK,cAAc;AAAA,UAC5C;AAAA,QACF;AAGO,eAAA,UAAU,OAAO,YAAY,OAAO,KAAK,QAAQ,GAAG,MAAM,MAAM,KAAK;AACrE,eAAA,UAAU,OAAO,YAAY,OAAO,KAAK,QAAQ,GAAG,MAAM,MAAM,KAAK;AACrE,eAAA,UAAU,OAAO,YAAY,OAAO,KAAK,QAAQ,GAAG,MAAM,MAAM,KAAK;AAC5E,eAAO,UAAU,OAAO,YAAY,OAAO,KAAK,QAAQ,GAAG,MAAM,MAAO,KAAK,SAAS,KAAK,SAAS,KAAK;AAKzG,eAAO,SAAS,cAAc,OAAO,SAAS,eAAe,OAAO,SAAS;AAEtE,eAAA,SAAS,YAAY,OAAO,SAAS,aAAa,OAAO,SAAS,MAAM;AAE/E,eAAO,SAAS,MAAM,KAAK,OAAO,SAAS,SAAS;AAE7C,eAAA,SAAS,UAAU,OAAO,SAAS;AAEtC,YAAA,CAAC,KAAK,SAAS;AAEjB,iBAAO,SAAS,WAAW;AAEpB,iBAAA,SAAS,MAAM,KAAK,IAAII,MAAAA,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;AAAA,QAAA,WACzC,KAAK,MAAM;AAChB,cAAA,OAAO,SAAS,KAAK,MAAM;AAE7B,mBAAO,SAAS,UAAU;AAEnB,mBAAA,SAAS,MAAM,KAAK,IAAIA,MAAAA,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;AAAA,UAAA,WAElD,KAAK,KAAK,MAAM,EAAE,EAAE,KAAK,SAAU,GAAG;AACpC,mBAAO,OAAO,SAAS;AAAA,UAAA,CACxB,GACD;AAEA,mBAAO,SAAS,UAAU;AAEnB,mBAAA,SAAS,MAAM,KAAK,IAAIA,MAAAA,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;AAAA,UAAA,OAC7C;AAEL,mBAAO,SAAS,WAAW;AAEpB,mBAAA,SAAS,MAAM,KAAK,IAAIA,MAAAA,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,IAAA;AAzoBlB,UAAA,gBAAgB,IAAIC,wBAAkB;AAAA,MAC1C,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,MAAMC,MAAA;AAAA,MACN,KAAK;AAAA,MACL,YAAY;AAAA,IAAA,CACb;AAEK,UAAA,oBAAoB,IAAIC,wBAAkB;AAAA,MAC9C,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,WAAW;AAAA,MACX,KAAK;AAAA,MACL,YAAY;AAAA,IAAA,CACb;AAIK,UAAA,eAAe,cAAc;AACnC,iBAAa,UAAU;AAEjB,UAAA,YAAY,cAAc;AAChC,cAAU,UAAU;AAEd,UAAA,SAAS,cAAc;AACtB,WAAA,MAAM,IAAI,QAAQ;AAEnB,UAAA,WAAW,cAAc;AACtB,aAAA,MAAM,IAAI,KAAQ;AAErB,UAAA,UAAU,cAAc;AACtB,YAAA,MAAM,IAAI,GAAQ;AAEpB,UAAA,sBAAsB,cAAc;AAC1C,wBAAoB,UAAU;AAExB,UAAA,uBAAuB,oBAAoB;AAC5B,yBAAA,MAAM,IAAI,QAAQ;AAEjC,UAAA,qBAAqB,oBAAoB;AAC5B,uBAAA,MAAM,IAAI,KAAQ;AAE/B,UAAA,wBAAwB,oBAAoB;AAC5B,0BAAA,MAAM,IAAI,QAAQ;AAElC,UAAA,YAAY,cAAc;AACtB,cAAA,MAAM,IAAI,QAAQ;AAEtB,UAAA,aAAa,kBAAkB;AAC1B,eAAA,MAAM,IAAI,QAAQ;AAEvB,UAAA,eAAe,kBAAkB;AAC1B,iBAAA,MAAM,IAAI,KAAQ;AAEzB,UAAA,cAAc,kBAAkB;AAC1B,gBAAA,MAAM,IAAI,GAAQ;AAExB,UAAA,cAAc,kBAAkB;AAC1B,gBAAA,MAAM,IAAI,KAAQ;AAExB,UAAA,iBAAiB,kBAAkB;AAC1B,mBAAA,MAAM,IAAI,QAAQ;AAE3B,UAAA,gBAAgB,kBAAkB;AAC1B,kBAAA,MAAM,IAAI,QAAQ;AAE1B,UAAA,cAAc,kBAAkB;AAC1B,gBAAA,MAAM,IAAI,OAAQ;AAExB,UAAA,2BAA2B,cAAc;AAC/C,6BAAyB,UAAU;AAI7B,UAAA,gBAAgB,IAAIC,MAAiB,iBAAA,GAAG,MAAM,KAAK,IAAI,GAAG,KAAK;AAErE,UAAM,sBAAsB,IAAIC,MAAY,YAAA,OAAO,OAAO,KAAK;AAEzD,UAAA,eAAe,IAAIC,MAAAA;AACzB,iBAAa,aAAa,YAAY,IAAIC,MAAA,uBAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAEjF,UAAA,iBAAiB,CAAC,QAAgB,QAAgC;AAChE,YAAA,WAAW,IAAID,MAAAA;AACrB,YAAM,WAAW,CAAA;AAEjB,eAAS,IAAI,GAAG,KAAK,KAAK,KAAK,EAAE,GAAG;AAClC,iBAAS,KAAK,GAAG,KAAK,IAAK,IAAI,KAAM,KAAK,EAAE,IAAI,QAAQ,KAAK,IAAK,IAAI,KAAM,KAAK,EAAE,IAAI,MAAM;AAAA,MAC/F;AAEA,eAAS,aAAa,YAAY,IAAIC,MAAuB,uBAAA,UAAU,CAAC,CAAC;AAElE,aAAA;AAAA,IAAA;AAKT,UAAM,0BAA0B,MAAsB;AAC9C,YAAA,WAAW,IAAID,MAAAA;AAErB,eAAS,aAAa,YAAY,IAAIC,MAAA,uBAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAE5E,aAAA;AAAA,IAAA;AAKT,UAAM,iBAAiB;AAAA,MACrB,GAAG;AAAA,QACD,CAAC,IAAIC,MAAK,KAAA,eAAe,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK;AAAA,QAC9E,CAAC,IAAIA,MAAA,KAAK,eAAe,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK;AAAA,QAC7E,CAAC,IAAIC,MAAK,KAAA,cAAc,UAAU,CAAC;AAAA,MACrC;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAK,KAAA,eAAe,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,MAAM,KAAK;AAAA,QAChE,CAAC,IAAIA,MAAA,KAAK,eAAe,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,MAAM,KAAK;AAAA,QAC3E,CAAC,IAAIC,MAAK,KAAA,cAAc,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,MAClE;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAA,KAAK,eAAe,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK;AAAA,QAC9E,CAAC,IAAIA,MAAK,KAAA,eAAe,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK;AAAA,QAC/E,CAAC,IAAIC,MAAK,KAAA,cAAc,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,MAClE;AAAA,MACA,KAAK,CAAC,CAAC,IAAID,WAAK,IAAIE,MAAmB,mBAAA,KAAK,CAAC,GAAG,oBAAoB,MAAO,CAAA,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,MACnG,IAAI;AAAA,QACF,CAAC,IAAIF,MAAAA,KAAK,IAAIG,oBAAc,OAAO,KAAK,GAAG,qBAAqB,MAAO,CAAA,GAAG,CAAC,MAAM,MAAM,CAAC,CAAC;AAAA,QACzF,CAAC,IAAIF,MAAAA,KAAK,cAAc,aAAa,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QAC3E,CAAC,IAAIA,MAAAA,KAAK,cAAc,aAAa,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5F;AAAA,MACA,IAAI;AAAA,QACF,CAAC,IAAID,MAAAA,KAAK,IAAIG,oBAAc,OAAO,KAAK,GAAG,mBAAmB,OAAO,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,QAC5G,CAAC,IAAIF,MAAAA,KAAK,cAAc,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QACxF,CAAC,IAAIA,MAAAA,KAAK,cAAc,WAAW,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAC3F;AAAA,MACA,IAAI;AAAA,QACF;AAAA,UACE,IAAID,MAAAA,KAAK,IAAIG,oBAAc,OAAO,KAAK,GAAG,sBAAsB,OAAO;AAAA,UACvE,CAAC,MAAM,GAAG,IAAI;AAAA,UACd,CAAC,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC;AAAA,QACrB;AAAA,QACA,CAAC,IAAIF,MAAAA,KAAK,cAAc,cAAc,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QAC5E,CAAC,IAAIA,MAAAA,KAAK,cAAc,cAAc,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAC9F;AAAA,IAAA;AAGF,UAAM,kBAAkB;AAAA,MACtB,GAAG,CAAC,CAAC,IAAID,MAAK,KAAA,IAAIJ,MAAiB,iBAAA,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC7G,GAAG,CAAC,CAAC,IAAII,WAAK,IAAIJ,MAAAA,iBAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MACvF,GAAG,CAAC,CAAC,IAAII,MAAK,KAAA,IAAIJ,MAAiB,iBAAA,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,MAC5G,KAAK,CAAC,CAAC,IAAII,MAAAA,KAAK,IAAIE,MAAAA,mBAAmB,KAAK,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,MAC9D,IAAI,CAAC,CAAC,IAAIF,MAAA,KAAK,IAAIG,MAAAA,cAAc,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MACzE,IAAI,CAAC,CAAC,IAAIH,WAAK,IAAIG,MAAc,cAAA,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,MAC9F,IAAI,CAAC,CAAC,IAAIH,WAAK,IAAIG,MAAc,cAAA,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,IAAA;AAGjG,UAAM,kBAAkB;AAAA,MACtB,OAAO,CAAC,CAAC,IAAIH,MAAA,KAAK,IAAIE,MAAAA,mBAAmB,MAAM,CAAC,GAAG,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MAC1F,KAAK,CAAC,CAAC,IAAIF,MAAA,KAAK,IAAIE,MAAAA,mBAAmB,MAAM,CAAC,GAAG,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MACxF,OAAO,CAAC,CAAC,IAAID,MAAK,KAAA,wBAA2B,GAAA,SAAS,GAAG,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MACpF,GAAG,CAAC,CAAC,IAAIA,WAAK,cAAc,UAAU,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,MAC1F,GAAG,CAAC,CAAC,IAAIA,MAAK,KAAA,cAAc,UAAU,MAAA,CAAO,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,MACzG,GAAG,CAAC,CAAC,IAAIA,MAAK,KAAA,cAAc,UAAU,MAAA,CAAO,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,IAAA;AAG5G,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,QACD,CAAC,IAAIA,MAAK,KAAA,eAAe,GAAG,GAAG,GAAG,UAAU,CAAC;AAAA,QAC7C,CAAC,IAAID,MAAK,KAAA,IAAIE,yBAAmB,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACnF;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAA,KAAK,eAAe,GAAG,GAAG,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AAAA,QAC3E,CAAC,IAAID,MAAK,KAAA,IAAIE,yBAAmB,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACrF;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAAA,KAAK,eAAe,GAAG,GAAG,GAAG,WAAW,GAAG,MAAM,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,QACzE,CAAC,IAAID,MAAK,KAAA,IAAIE,yBAAmB,MAAM,CAAC,GAAG,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACpF;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAAA,KAAK,eAAe,MAAM,CAAC,GAAG,wBAAwB,GAAG,MAAM,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,QACvF;AAAA,UACE,IAAID,WAAK,IAAIJ,uBAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;AAAA,UACnF,CAAC,MAAM,GAAG,CAAC;AAAA,UACX,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC;AAAA,UACnB,CAAC,GAAG,GAAG,IAAK;AAAA,QACd;AAAA,QACA;AAAA,UACE,IAAII,WAAK,IAAIJ,uBAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;AAAA,UACnF,CAAC,OAAO,GAAG,CAAC;AAAA,UACZ,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC;AAAA,UAClB,CAAC,GAAG,GAAG,IAAK;AAAA,QACd;AAAA,QACA;AAAA,UACE,IAAII,WAAK,IAAIJ,uBAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;AAAA,UACnF,CAAC,GAAG,OAAO,CAAC;AAAA,UACZ,CAAC,KAAK,IAAI,GAAG,CAAC;AAAA,UACd,CAAC,GAAG,GAAG,IAAK;AAAA,QACd;AAAA,QACA;AAAA,UACE,IAAII,WAAK,IAAIJ,uBAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;AAAA,UACnF,CAAC,GAAG,MAAM,CAAC;AAAA,UACX,CAAC,GAAG,GAAG,CAAC;AAAA,UACR,CAAC,GAAG,GAAG,IAAK;AAAA,QACd;AAAA,MACF;AAAA,MACA,MAAM,CAAC,CAAC,IAAIK,MAAAA,KAAK,eAAe,GAAG,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,IAAA;AAGjF,UAAM,eAAe;AAAA,MACnB,MAAM,CAAC,CAAC,IAAIA,WAAK,cAAc,UAAU,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,IAAA;AAG/F,UAAM,eAAe;AAAA,MACnB,GAAG,CAAC,CAAC,IAAID,MAAK,KAAA,IAAII,MAAAA,cAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC1G,GAAG,CAAC,CAAC,IAAIJ,MAAAA,KAAK,IAAII,MAAAA,cAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,MAC9F,GAAG,CAAC,CAAC,IAAIJ,MAAAA,KAAK,IAAII,MAAAA,cAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC/F,GAAG,CAAC,CAAC,IAAIJ,WAAK,IAAII,MAAAA,cAAc,MAAM,KAAK,GAAG,EAAE,GAAG,YAAY,CAAC,CAAC;AAAA,MACjE,MAAM,CAAC,CAAC,IAAIJ,MAAK,KAAA,IAAIK,MAAe,eAAA,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,IAAA;AAGjE,UAAM,aAAa;AAAA,MACjB,GAAG;AAAA,QACD,CAAC,IAAIL,MAAA,KAAK,qBAAqB,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;AAAA,QACzE,CAAC,IAAIC,MAAK,KAAA,cAAc,UAAU,GAAG,MAAM,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AAAA,MAC9D;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAAA,KAAK,qBAAqB,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,QACrD,CAAC,IAAIC,MAAA,KAAK,cAAc,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AAAA,MAC/E;AAAA,MACA,GAAG;AAAA,QACD,CAAC,IAAID,MAAA,KAAK,qBAAqB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC;AAAA,QACzE,CAAC,IAAIC,MAAA,KAAK,cAAc,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;AAAA,MAC/E;AAAA,MACA,IAAI;AAAA,QACF,CAAC,IAAID,MAAAA,KAAK,qBAAqB,oBAAoB,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,QACxF,CAAC,IAAIC,MAAAA,KAAK,cAAc,aAAa,GAAG,CAAC,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QAC7E,CAAC,IAAIA,MAAAA,KAAK,cAAc,aAAa,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAC9F;AAAA,MACA,IAAI;AAAA,QACF,CAAC,IAAID,MAAAA,KAAK,qBAAqB,kBAAkB,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AAAA,QACtF,CAAC,IAAIC,MAAAA,KAAK,cAAc,WAAW,GAAG,CAAC,GAAG,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QAC1F,CAAC,IAAIA,MAAAA,KAAK,cAAc,WAAW,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAC7F;AAAA,MACA,IAAI;AAAA,QACF,CAAC,IAAID,MAAAA,KAAK,qBAAqB,qBAAqB,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,QACzF,CAAC,IAAIC,MAAAA,KAAK,cAAc,cAAc,GAAG,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,QAC9E,CAAC,IAAIA,MAAAA,KAAK,cAAc,cAAc,GAAG,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,MAChG;AAAA,MACA,MAAM,CAAC,CAAC,IAAID,MAAAA,KAAK,IAAIH,MAAA,YAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,MAAA,CAAO,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,MACjG,MAAM,CAAC,CAAC,IAAIG,MAAAA,KAAK,IAAIH,MAAA,YAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,MAAA,CAAO,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MACjG,MAAM,CAAC,CAAC,IAAIG,MAAAA,KAAK,IAAIH,MAAA,YAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,MAAA,CAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAAA;AAGnG,UAAM,cAAc;AAAA,MAClB,GAAG,CAAC,CAAC,IAAIG,MAAK,KAAA,IAAIJ,MAAiB,iBAAA,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MAC/G,GAAG,CAAC,CAAC,IAAII,WAAK,IAAIJ,MAAAA,iBAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MACzF,GAAG,CAAC,CAAC,IAAII,MAAK,KAAA,IAAIJ,MAAiB,iBAAA,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,MAC9G,IAAI,CAAC,CAAC,IAAII,MAAAA,KAAK,qBAAqB,YAAY,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,MACtF,IAAI,CAAC,CAAC,IAAIA,MAAAA,KAAK,qBAAqB,YAAY,GAAG,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,MACtF,IAAI,CAAC,CAAC,IAAIA,MAAAA,KAAK,qBAAqB,YAAY,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MACtF,MAAM,CAAC,CAAC,IAAIA,MAAAA,KAAK,IAAIH,kBAAY,KAAK,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAAA,MAC5E,MAAM,CAAC,CAAC,IAAIG,MAAAA,KAAK,IAAIH,kBAAY,KAAK,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MAC5E,MAAM,CAAC,CAAC,IAAIG,MAAAA,KAAK,IAAIH,kBAAY,KAAK,KAAK,GAAG,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAAA;AAG9E,UAAM,cAAc;AAAA,MAClB,GAAG,CAAC,CAAC,IAAII,WAAK,cAAc,UAAU,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,MAC1F,GAAG,CAAC,CAAC,IAAIA,MAAK,KAAA,cAAc,UAAU,MAAA,CAAO,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,MACzG,GAAG,CAAC,CAAC,IAAIA,MAAK,KAAA,cAAc,UAAU,MAAA,CAAO,GAAG,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AAAA,IAAA;AAKtG,UAAA,aAAa,CAAC,aAA4B;AACxC,YAAA,QAAQ,IAAIf,MAAAA;AAElB,eAAS,QAAQ,UAAU;AACzB,iBAAS,IAAI,SAAS,IAAI,EAAE,QAAQ,OAAO;AACnC,gBAAA,SAAS,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,gBAAM,WAAW,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;AACpC,gBAAM,WAAW,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;AACpC,gBAAM,QAAQ,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;AACjC,gBAAM,MAAM,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;AAG/B,iBAAO,OAAO;AAEd,iBAAO,MAAM;AAEb,cAAI,UAAU;AACL,mBAAA,SAAS,IAAI,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,UAC3D;AAEA,cAAI,UAAU;AACL,mBAAA,SAAS,IAAI,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,UAC3D;AAEA,cAAI,OAAO;AACF,mBAAA,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,UAC/C;AAEA,iBAAO,aAAa;AAEd,gBAAA,eAAe,OAAO,SAAS,MAAM;AAC9B,uBAAA,aAAa,OAAO,MAAM;AACvC,iBAAO,WAAW;AAClB,iBAAO,cAAc;AAErB,iBAAO,SAAS,IAAI,GAAG,GAAG,CAAC;AAC3B,iBAAO,SAAS,IAAI,GAAG,GAAG,CAAC;AAC3B,iBAAO,MAAM,IAAI,GAAG,GAAG,CAAC;AAExB,gBAAM,IAAI,MAAM;AAAA,QAClB;AAAA,MACF;AAEO,aAAA;AAAA,IAAA;AAGT,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AAEd,SAAK,IAAK,KAAK,MAAM,WAAW,IAAI,WAAW,cAAc,CAAE;AAC/D,SAAK,IAAK,KAAK,MAAM,QAAQ,IAAI,WAAW,WAAW,CAAE;AACzD,SAAK,IAAK,KAAK,MAAM,OAAO,IAAI,WAAW,UAAU,CAAE;AACvD,SAAK,IAAK,KAAK,OAAO,WAAW,IAAI,WAAW,eAAe,CAAE;AACjE,SAAK,IAAK,KAAK,OAAO,QAAQ,IAAI,WAAW,YAAY,CAAE;AAC3D,SAAK,IAAK,KAAK,OAAO,OAAO,IAAI,WAAW,WAAW,CAAE;AACzD,SAAK,IAAK,KAAK,OAAO,WAAW,IAAI,WAAW,eAAe,CAAE;AACjE,SAAK,IAAK,KAAK,OAAO,QAAQ,IAAI,WAAW,YAAY,CAAE;AAC3D,SAAK,IAAK,KAAK,OAAO,OAAO,IAAI,WAAW,WAAW,CAAE;AAIpD,SAAA,OAAO,WAAW,EAAE,UAAU;AAC9B,SAAA,OAAO,QAAQ,EAAE,UAAU;AAC3B,SAAA,OAAO,OAAO,EAAE,UAAU;AAAA,EACjC;AAyTF;AAEA,MAAM,+BAA+Bc,MAAAA,KAAuC;AAAA,EAI1E,cAAc;AACZ;AAAA,MACE,IAAIG,MAAA,cAAc,KAAQ,KAAQ,GAAG,CAAC;AAAA,MACtC,IAAIV,wBAAkB;AAAA,QACpB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,MAAMC,MAAA;AAAA,QACN,aAAa;AAAA,QACb,SAAS;AAAA,QACT,YAAY;AAAA,MAAA,CACb;AAAA,IAAA;AAbG,oDAA2B;AAC5B,gCAAO;AAgBN,iCAAQ,IAAIN,MAAQ,QAAA,GAAG,GAAG,CAAC;AAC3B,iCAAQ,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAC3B,iCAAQ,IAAIA,MAAQ,QAAA,GAAG,GAAG,CAAC;AAE3B,sCAAa,IAAIA,MAAAA;AACjB,qCAAY,IAAIA,MAAAA;AAChB,uCAAc,IAAIA,MAAAA;AAClB,sCAAa,IAAIG,MAAAA;AACjB,8CAAqB,IAAIF,MAAAA;AAGzB;AAAA,4CAAmB,IAAIA,MAAAA;AAEvB,yCAAgB,IAAID,MAAAA;AACpB,2CAAkB,IAAIC,MAAAA;AAEtB,+BAAM,IAAID,MAAAA;AAEV,gCAAsB;AACtB,gCAAyC;AACzC,iCAAQ;AAET,6CAAoB,MAAY;AACrC,UAAI,QAAQ,KAAK;AAEZ,WAAA,SAAS,KAAK,KAAK,aAAa;AAErC,UAAI,KAAK,SAAS;AAAiB,gBAAA;AAEnC,WAAK,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,UAAU,UAAU,KAAK,kBAAkB,KAAK,kBAAkB;AAC1G,WAAK,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,UAAU,UAAU,KAAK,kBAAkB,KAAK,kBAAkB;AAC1G,WAAK,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,gBAAgB,UAAU,UAAU,KAAK,kBAAkB,KAAK,kBAAkB;AAIrG,WAAA,YAAY,KAAK,KAAK,KAAK;AAEhC,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AAAA,QACL,KAAK;AACH,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK;AACH,mBAAK,YAAY,KAAK,KAAK,GAAG,EAAE,MAAM,KAAK,KAAK;AAChD,mBAAK,UAAU,KAAK,KAAK,KAAK,EAAE,MAAM,KAAK,WAAW;AACtD;AAAA,YACF,KAAK;AACH,mBAAK,YAAY,KAAK,KAAK,GAAG,EAAE,MAAM,KAAK,KAAK;AAChD,mBAAK,UAAU,KAAK,KAAK,KAAK,EAAE,MAAM,KAAK,WAAW;AACtD;AAAA,YACF,KAAK;AACH,mBAAK,YAAY,KAAK,KAAK,GAAG,EAAE,MAAM,KAAK,KAAK;AAChD,mBAAK,UAAU,KAAK,KAAK,KAAK,EAAE,MAAM,KAAK,WAAW;AACtD;AAAA,YACF,KAAK;AACE,mBAAA,UAAU,KAAK,KAAK,KAAK;AAC9B;AAAA,YACF,KAAK;AACE,mBAAA,UAAU,KAAK,KAAK,KAAK;AAC9B;AAAA,YACF,KAAK;AACE,mBAAA,YAAY,KAAK,KAAK,KAAK;AAC3B,mBAAA,UAAU,KAAK,KAAK,KAAK;AAC9B;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AACH,mBAAK,UAAU,IAAI,GAAG,GAAG,CAAC;AAC1B;AAAA,UACJ;AAEA;AAAA,QACF,KAAK;AAAA,QACL;AAEE,eAAK,UAAU,IAAI,GAAG,GAAG,CAAC;AAAA,MAC9B;AAEA,UAAI,KAAK,UAAU,OAAO,MAAM,GAAG;AAE5B,aAAA,WAAW,KAAK,KAAK,gBAAgB;AAAA,MAAA,OACrC;AACL,aAAK,WAAW,OAAO,KAAK,WAAW,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,WAAW,KAAK,WAAW;AAEhF,aAAA,WAAW,sBAAsB,KAAK,UAAU;AAAA,MACvD;AAEA,YAAM,kBAAkB;AAAA,IAAA;AAAA,EAvF1B;AAyFF;;;;"}