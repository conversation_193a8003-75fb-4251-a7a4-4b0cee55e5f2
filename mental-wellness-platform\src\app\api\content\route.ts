import { NextRequest, NextResponse } from 'next/server'

// Mock content data - in a real app, this would come from a database
const contentItems = [
  {
    id: '1',
    title: 'Understanding Your Emotions',
    type: 'video',
    category: 'emotional_intelligence',
    ageGroup: 'child',
    duration: 8,
    thumbnail: '🎭',
    description: 'A fun animated video that helps children identify and understand different emotions.',
    tags: ['emotions', 'feelings', 'kids', 'animation'],
    views: 15420,
    rating: 4.8,
    featured: true,
    contentUrl: '/content/understanding-emotions',
    createdAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    title: 'Breathing Exercises for Anxiety',
    type: 'exercise',
    category: 'coping_skills',
    ageGroup: 'all',
    duration: 5,
    thumbnail: '🫁',
    description: 'Simple breathing techniques to help manage anxiety and stress in daily life.',
    tags: ['breathing', 'anxiety', 'relaxation', 'mindfulness'],
    views: 23150,
    rating: 4.9,
    featured: true,
    contentUrl: '/content/breathing-exercises',
    createdAt: '2024-01-10T14:30:00Z'
  },
  {
    id: '3',
    title: 'Recognizing Safe vs Unsafe Situations',
    type: 'comic',
    category: 'safety',
    ageGroup: 'child',
    thumbnail: '🛡️',
    description: 'An interactive comic that teaches children how to identify safe and unsafe situations.',
    tags: ['safety', 'protection', 'awareness', 'comic'],
    views: 8930,
    rating: 4.7,
    featured: false,
    contentUrl: '/content/safety-comic',
    createdAt: '2024-01-08T09:15:00Z'
  },
  {
    id: '4',
    title: 'Building Self-Confidence',
    type: 'article',
    category: 'wellness',
    ageGroup: 'teen',
    thumbnail: '💪',
    description: 'Practical strategies for teenagers to build self-confidence and positive self-image.',
    tags: ['confidence', 'self-esteem', 'teens', 'empowerment'],
    views: 12680,
    rating: 4.6,
    featured: false,
    contentUrl: '/content/self-confidence',
    createdAt: '2024-01-05T16:45:00Z'
  },
  {
    id: '5',
    title: 'The Worry Monster Story',
    type: 'story',
    category: 'emotional_intelligence',
    ageGroup: 'child',
    duration: 12,
    thumbnail: '👹',
    description: 'A gentle story about a child who learns to tame their worry monster with help and courage.',
    tags: ['worry', 'anxiety', 'story', 'coping'],
    views: 19240,
    rating: 4.8,
    featured: true,
    contentUrl: '/content/worry-monster',
    createdAt: '2024-01-03T11:20:00Z'
  },
  {
    id: '6',
    title: 'Mindfulness for Busy Moms',
    type: 'video',
    category: 'wellness',
    ageGroup: 'adult',
    duration: 15,
    thumbnail: '🧘‍♀️',
    description: 'Quick mindfulness practices that busy mothers can incorporate into their daily routine.',
    tags: ['mindfulness', 'mothers', 'stress-relief', 'self-care'],
    views: 31200,
    rating: 4.9,
    featured: true,
    contentUrl: '/content/mindfulness-moms',
    createdAt: '2024-01-01T08:00:00Z'
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const type = searchParams.get('type')
    const ageGroup = searchParams.get('ageGroup')
    const featured = searchParams.get('featured')
    const limit = searchParams.get('limit')
    const search = searchParams.get('search')

    let filteredContent = contentItems

    // Filter by category
    if (category && category !== 'all') {
      filteredContent = filteredContent.filter(item => item.category === category)
    }

    // Filter by type
    if (type && type !== 'all') {
      filteredContent = filteredContent.filter(item => item.type === type)
    }

    // Filter by age group
    if (ageGroup && ageGroup !== 'all') {
      filteredContent = filteredContent.filter(item => 
        item.ageGroup === ageGroup || item.ageGroup === 'all'
      )
    }

    // Filter by featured status
    if (featured === 'true') {
      filteredContent = filteredContent.filter(item => item.featured)
    }

    // Search functionality
    if (search) {
      const searchLower = search.toLowerCase()
      filteredContent = filteredContent.filter(item =>
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      )
    }

    // Sort by views (most popular first) and then by creation date
    filteredContent.sort((a, b) => {
      if (b.views !== a.views) {
        return b.views - a.views
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })

    // Apply limit if specified
    if (limit) {
      const limitNum = parseInt(limit, 10)
      if (!isNaN(limitNum) && limitNum > 0) {
        filteredContent = filteredContent.slice(0, limitNum)
      }
    }

    // Separate featured and regular content
    const featuredContent = filteredContent.filter(item => item.featured)
    const regularContent = filteredContent.filter(item => !item.featured)

    return NextResponse.json({
      success: true,
      data: {
        featured: featuredContent,
        regular: regularContent,
        all: filteredContent
      },
      total: filteredContent.length,
      filters: {
        category,
        type,
        ageGroup,
        featured,
        search
      }
    })

  } catch (error) {
    console.error('Content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, contentId, userId, data } = await request.json()

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'view':
        // Log content view
        return NextResponse.json({
          success: true,
          data: {
            message: 'Content view logged',
            contentId,
            viewedAt: new Date().toISOString()
          }
        })

      case 'complete':
        // Log content completion
        const { duration, progress } = data || {}
        return NextResponse.json({
          success: true,
          data: {
            message: 'Content completion logged',
            contentId,
            duration,
            progress: progress || 100,
            completedAt: new Date().toISOString()
          }
        })

      case 'rate':
        // Save content rating
        const { rating, review } = data || {}
        if (!rating || rating < 1 || rating > 5) {
          return NextResponse.json(
            { error: 'Valid rating (1-5) is required' },
            { status: 400 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            message: 'Rating saved successfully',
            contentId,
            rating,
            review,
            ratedAt: new Date().toISOString()
          }
        })

      case 'bookmark':
        // Toggle bookmark status
        return NextResponse.json({
          success: true,
          data: {
            message: 'Bookmark toggled',
            contentId,
            bookmarked: true,
            bookmarkedAt: new Date().toISOString()
          }
        })

      case 'share':
        // Log content share
        const { platform } = data || {}
        return NextResponse.json({
          success: true,
          data: {
            message: 'Content share logged',
            contentId,
            platform,
            sharedAt: new Date().toISOString()
          }
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Content API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
