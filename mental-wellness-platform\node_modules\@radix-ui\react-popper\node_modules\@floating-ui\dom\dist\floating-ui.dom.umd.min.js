!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@floating-ui/core")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/core"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIDOM={},t.FloatingUICore)}(this,(function(t,e){"use strict";function n(t){return t&&t.document&&t.location&&t.alert&&t.setInterval}function o(t){if(null==t)return window;if(!n(t)){const e=t.ownerDocument;return e&&e.defaultView||window}return t}function i(t){return o(t).getComputedStyle(t)}function r(t){return n(t)?"":t?(t.nodeName||"").toLowerCase():""}function l(){const t=navigator.userAgentData;return null!=t&&t.brands?t.brands.map((t=>t.brand+"/"+t.version)).join(" "):navigator.userAgent}function c(t){return t instanceof o(t).HTMLElement}function f(t){return t instanceof o(t).Element}function u(t){if("undefined"==typeof ShadowRoot)return!1;return t instanceof o(t).ShadowRoot||t instanceof ShadowRoot}function s(t){const{overflow:e,overflowX:n,overflowY:o}=i(t);return/auto|scroll|overlay|hidden/.test(e+o+n)}function d(t){return["table","td","th"].includes(r(t))}function a(t){const e=/firefox/i.test(l()),n=i(t);return"none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||["transform","perspective"].includes(n.willChange)||e&&"filter"===n.willChange||e&&!!n.filter&&"none"!==n.filter}function h(){return!/^((?!chrome|android).)*safari/i.test(l())}const g=Math.min,p=Math.max,m=Math.round;function y(t,e,n){var i,r,l,u;void 0===e&&(e=!1),void 0===n&&(n=!1);const s=t.getBoundingClientRect();let d=1,a=1;e&&c(t)&&(d=t.offsetWidth>0&&m(s.width)/t.offsetWidth||1,a=t.offsetHeight>0&&m(s.height)/t.offsetHeight||1);const g=f(t)?o(t):window,p=!h()&&n,y=(s.left+(p&&null!=(i=null==(r=g.visualViewport)?void 0:r.offsetLeft)?i:0))/d,w=(s.top+(p&&null!=(l=null==(u=g.visualViewport)?void 0:u.offsetTop)?l:0))/a,b=s.width/d,v=s.height/a;return{width:b,height:v,top:w,right:y+b,bottom:w+v,left:y,x:y,y:w}}function w(t){return(e=t,(e instanceof o(e).Node?t.ownerDocument:t.document)||window.document).documentElement;var e}function b(t){return f(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function v(t){return y(w(t)).left+b(t).scrollLeft}function x(t,e,n){const o=c(e),i=w(e),l=y(t,o&&function(t){const e=y(t);return m(e.width)!==t.offsetWidth||m(e.height)!==t.offsetHeight}(e),"fixed"===n);let f={scrollLeft:0,scrollTop:0};const u={x:0,y:0};if(o||!o&&"fixed"!==n)if(("body"!==r(e)||s(i))&&(f=b(e)),c(e)){const t=y(e,!0);u.x=t.x+e.clientLeft,u.y=t.y+e.clientTop}else i&&(u.x=v(i));return{x:l.left+f.scrollLeft-u.x,y:l.top+f.scrollTop-u.y,width:l.width,height:l.height}}function L(t){return"html"===r(t)?t:t.assignedSlot||t.parentNode||(u(t)?t.host:null)||w(t)}function O(t){return c(t)&&"fixed"!==getComputedStyle(t).position?t.offsetParent:null}function R(t){const e=o(t);let n=O(t);for(;n&&d(n)&&"static"===getComputedStyle(n).position;)n=O(n);return n&&("html"===r(n)||"body"===r(n)&&"static"===getComputedStyle(n).position&&!a(n))?e:n||function(t){let e=L(t);for(u(e)&&(e=e.host);c(e)&&!["html","body"].includes(r(e));){if(a(e))return e;e=e.parentNode}return null}(t)||e}function T(t){if(c(t))return{width:t.offsetWidth,height:t.offsetHeight};const e=y(t);return{width:e.width,height:e.height}}function P(t){const e=L(t);return["html","body","#document"].includes(r(e))?t.ownerDocument.body:c(e)&&s(e)?e:P(e)}function C(t,e){var n;void 0===e&&(e=[]);const i=P(t),r=i===(null==(n=t.ownerDocument)?void 0:n.body),l=o(i),c=r?[l].concat(l.visualViewport||[],s(i)?i:[]):i,f=e.concat(c);return r?f:f.concat(C(c))}function j(t,n,r){return"viewport"===n?e.rectToClientRect(function(t,e){const n=o(t),i=w(t),r=n.visualViewport;let l=i.clientWidth,c=i.clientHeight,f=0,u=0;if(r){l=r.width,c=r.height;const t=h();(t||!t&&"fixed"===e)&&(f=r.offsetLeft,u=r.offsetTop)}return{width:l,height:c,x:f,y:u}}(t,r)):f(n)?function(t,e){const n=y(t,!1,"fixed"===e),o=n.top+t.clientTop,i=n.left+t.clientLeft;return{top:o,left:i,x:i,y:o,right:i+t.clientWidth,bottom:o+t.clientHeight,width:t.clientWidth,height:t.clientHeight}}(n,r):e.rectToClientRect(function(t){var e;const n=w(t),o=b(t),r=null==(e=t.ownerDocument)?void 0:e.body,l=p(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),c=p(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0);let f=-o.scrollLeft+v(t);const u=-o.scrollTop;return"rtl"===i(r||n).direction&&(f+=p(n.clientWidth,r?r.clientWidth:0)-l),{width:l,height:c,x:f,y:u}}(w(t)))}function W(t){const e=C(t),n=["absolute","fixed"].includes(i(t).position)&&c(t)?R(t):t;return f(n)?e.filter((t=>f(t)&&function(t,e){const n=null==e.getRootNode?void 0:e.getRootNode();if(t.contains(e))return!0;if(n&&u(n)){let n=e;do{if(n&&t===n)return!0;n=n.parentNode||n.host}while(n)}return!1}(t,n)&&"body"!==r(t))):[]}const E={getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const r=[..."clippingAncestors"===n?W(e):[].concat(n),o],l=r[0],c=r.reduce(((t,n)=>{const o=j(e,n,i);return t.top=p(o.top,t.top),t.right=g(o.right,t.right),t.bottom=g(o.bottom,t.bottom),t.left=p(o.left,t.left),t}),j(e,l,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{rect:e,offsetParent:n,strategy:o}=t;const i=c(n),l=w(n);if(n===l)return e;let f={scrollLeft:0,scrollTop:0};const u={x:0,y:0};if((i||!i&&"fixed"!==o)&&(("body"!==r(n)||s(l))&&(f=b(n)),c(n))){const t=y(n,!0);u.x=t.x+n.clientLeft,u.y=t.y+n.clientTop}return{...e,x:e.x-f.scrollLeft+u.x,y:e.y-f.scrollTop+u.y}},isElement:f,getDimensions:T,getOffsetParent:R,getDocumentElement:w,getElementRects:t=>{let{reference:e,floating:n,strategy:o}=t;return{reference:x(e,R(n),o),floating:{...T(n),x:0,y:0}}},getClientRects:t=>Array.from(t.getClientRects()),isRTL:t=>"rtl"===i(t).direction};Object.defineProperty(t,"arrow",{enumerable:!0,get:function(){return e.arrow}}),Object.defineProperty(t,"autoPlacement",{enumerable:!0,get:function(){return e.autoPlacement}}),Object.defineProperty(t,"detectOverflow",{enumerable:!0,get:function(){return e.detectOverflow}}),Object.defineProperty(t,"flip",{enumerable:!0,get:function(){return e.flip}}),Object.defineProperty(t,"hide",{enumerable:!0,get:function(){return e.hide}}),Object.defineProperty(t,"inline",{enumerable:!0,get:function(){return e.inline}}),Object.defineProperty(t,"limitShift",{enumerable:!0,get:function(){return e.limitShift}}),Object.defineProperty(t,"offset",{enumerable:!0,get:function(){return e.offset}}),Object.defineProperty(t,"shift",{enumerable:!0,get:function(){return e.shift}}),Object.defineProperty(t,"size",{enumerable:!0,get:function(){return e.size}}),t.autoUpdate=function(t,e,n,o){void 0===o&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:l=!0,animationFrame:c=!1}=o,u=i&&!c,s=r&&!c,d=u||s?[...f(t)?C(t):[],...C(e)]:[];d.forEach((t=>{u&&t.addEventListener("scroll",n,{passive:!0}),s&&t.addEventListener("resize",n)}));let a,h=null;if(l){let o=!0;h=new ResizeObserver((()=>{o||n(),o=!1})),f(t)&&!c&&h.observe(t),h.observe(e)}let g=c?y(t):null;return c&&function e(){const o=y(t);!g||o.x===g.x&&o.y===g.y&&o.width===g.width&&o.height===g.height||n();g=o,a=requestAnimationFrame(e)}(),n(),()=>{var t;d.forEach((t=>{u&&t.removeEventListener("scroll",n),s&&t.removeEventListener("resize",n)})),null==(t=h)||t.disconnect(),h=null,c&&cancelAnimationFrame(a)}},t.computePosition=(t,n,o)=>e.computePosition(t,n,{platform:E,...o}),t.getOverflowAncestors=C,Object.defineProperty(t,"__esModule",{value:!0})}));
