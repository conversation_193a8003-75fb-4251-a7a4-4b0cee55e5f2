import * as React from 'react';
import type { Middleware, SideObject } from '@floating-ui/core';
/**
 * Positions an inner element of the floating element such that it is centered
 * to the reference element.
 * This wraps the core `arrow` middleware to allow React refs as the element.
 * @see https://floating-ui.com/docs/arrow
 */
export declare const arrow: (options: {
    element: React.MutableRefObject<HTMLElement | null> | HTMLElement;
    padding?: number | SideObject;
}) => Middleware;
