'use client'

import { useRef, useEffect, useState } from 'react'
import { <PERSON>vas, useFrame, useThree } from '@react-three/fiber'
import { OrbitControls, Text, Sphere, Box } from '@react-three/drei'
import * as THREE from 'three'

interface AvatarProps {
  isListening: boolean
  isSpeaking: boolean
  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'
  message?: string
}

// Custom 3D Human Head Component
function HumanHead({ isListening, isSpeaking, emotion }: Omit<AvatarProps, 'message'>) {
  const headRef = useRef<THREE.Group>(null)
  const leftEyeRef = useRef<THREE.Mesh>(null)
  const rightEyeRef = useRef<THREE.Mesh>(null)
  const mouthRef = useRef<THREE.Mesh>(null)
  const eyebrowLeftRef = useRef<THREE.Mesh>(null)
  const eyebrowRightRef = useRef<THREE.Mesh>(null)
  
  const [blinkTimer, setBlinkTimer] = useState(0)
  const [mouthAnimation, setMouthAnimation] = useState(0)

  // Animation loop
  useFrame((state, delta) => {
    if (!headRef.current) return

    // Breathing animation
    const breathingScale = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02
    headRef.current.scale.setScalar(breathingScale)

    // Blinking animation
    setBlinkTimer(prev => prev + delta)
    if (blinkTimer > 3) {
      setBlinkTimer(0)
      // Blink animation
      if (leftEyeRef.current && rightEyeRef.current) {
        const blinkScale = Math.max(0.1, Math.sin(state.clock.elapsedTime * 20))
        leftEyeRef.current.scale.y = blinkScale
        rightEyeRef.current.scale.y = blinkScale
      }
    }

    // Speaking animation
    if (isSpeaking && mouthRef.current) {
      const mouthMovement = Math.sin(state.clock.elapsedTime * 8) * 0.3 + 0.7
      mouthRef.current.scale.y = mouthMovement
      setMouthAnimation(mouthMovement)
    } else if (mouthRef.current) {
      mouthRef.current.scale.y = THREE.MathUtils.lerp(mouthRef.current.scale.y, 0.3, delta * 5)
    }

    // Listening animation - slight head tilt
    if (isListening && headRef.current) {
      const tilt = Math.sin(state.clock.elapsedTime * 1.5) * 0.1
      headRef.current.rotation.z = tilt
    } else if (headRef.current) {
      headRef.current.rotation.z = THREE.MathUtils.lerp(headRef.current.rotation.z, 0, delta * 3)
    }
  })

  // Emotion-based expressions
  const getEmotionColors = () => {
    switch (emotion) {
      case 'happy':
        return { skin: '#FFE4C4', cheek: '#FFB6C1' }
      case 'sad':
        return { skin: '#F5DEB3', cheek: '#D3D3D3' }
      case 'concerned':
        return { skin: '#FAEBD7', cheek: '#DDA0DD' }
      case 'empathetic':
        return { skin: '#FFF8DC', cheek: '#F0E68C' }
      default:
        return { skin: '#FDBCB4', cheek: '#FFB6C1' }
    }
  }

  const colors = getEmotionColors()

  return (
    <group ref={headRef} position={[0, 0, 0]}>
      {/* Head */}
      <Sphere args={[1.2, 32, 32]} position={[0, 0, 0]}>
        <meshStandardMaterial color={colors.skin} />
      </Sphere>

      {/* Eyes */}
      <Sphere ref={leftEyeRef} args={[0.15, 16, 16]} position={[-0.3, 0.2, 0.8]}>
        <meshStandardMaterial color="#FFFFFF" />
      </Sphere>
      <Sphere ref={rightEyeRef} args={[0.15, 16, 16]} position={[0.3, 0.2, 0.8]}>
        <meshStandardMaterial color="#FFFFFF" />
      </Sphere>

      {/* Pupils */}
      <Sphere args={[0.08, 16, 16]} position={[-0.3, 0.2, 0.9]}>
        <meshStandardMaterial color="#2C3E50" />
      </Sphere>
      <Sphere args={[0.08, 16, 16]} position={[0.3, 0.2, 0.9]}>
        <meshStandardMaterial color="#2C3E50" />
      </Sphere>

      {/* Eyebrows */}
      <Box 
        ref={eyebrowLeftRef} 
        args={[0.3, 0.05, 0.1]} 
        position={[-0.3, 0.4, 0.8]}
        rotation={[0, 0, emotion === 'concerned' ? 0.3 : 0]}
      >
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box 
        ref={eyebrowRightRef} 
        args={[0.3, 0.05, 0.1]} 
        position={[0.3, 0.4, 0.8]}
        rotation={[0, 0, emotion === 'concerned' ? -0.3 : 0]}
      >
        <meshStandardMaterial color="#8B4513" />
      </Box>

      {/* Nose */}
      <Sphere args={[0.08, 16, 16]} position={[0, 0, 0.9]}>
        <meshStandardMaterial color={colors.skin} />
      </Sphere>

      {/* Mouth */}
      <Box 
        ref={mouthRef} 
        args={[0.3, 0.1, 0.05]} 
        position={[0, -0.3, 0.8]}
        rotation={[0, 0, emotion === 'happy' ? 0.3 : emotion === 'sad' ? -0.3 : 0]}
      >
        <meshStandardMaterial color="#CD5C5C" />
      </Box>

      {/* Cheeks (for emotion) */}
      {(emotion === 'happy' || emotion === 'empathetic') && (
        <>
          <Sphere args={[0.1, 16, 16]} position={[-0.6, -0.1, 0.6]}>
            <meshStandardMaterial color={colors.cheek} transparent opacity={0.6} />
          </Sphere>
          <Sphere args={[0.1, 16, 16]} position={[0.6, -0.1, 0.6]}>
            <meshStandardMaterial color={colors.cheek} transparent opacity={0.6} />
          </Sphere>
        </>
      )}

      {/* Hair */}
      <Sphere args={[1.3, 32, 32]} position={[0, 0.3, -0.2]}>
        <meshStandardMaterial color="#8B4513" />
      </Sphere>

      {/* Neck */}
      <Box args={[0.4, 0.8, 0.4]} position={[0, -1.2, 0]}>
        <meshStandardMaterial color={colors.skin} />
      </Box>

      {/* Shoulders */}
      <Box args={[2, 0.3, 0.8]} position={[0, -1.8, 0]}>
        <meshStandardMaterial color="#4A90E2" />
      </Box>
    </group>
  )
}

// Background Environment
function Environment() {
  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />
      
      {/* Background */}
      <Sphere args={[50]} position={[0, 0, -20]}>
        <meshBasicMaterial color="#E8F4FD" side={THREE.BackSide} />
      </Sphere>
    </>
  )
}

// Main Avatar Component
export default function HumanAvatar({ isListening, isSpeaking, emotion, message }: AvatarProps) {
  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 0, 5])

  useEffect(() => {
    // Adjust camera based on interaction state
    if (isListening) {
      setCameraPosition([0, 0, 4]) // Zoom in when listening
    } else if (isSpeaking) {
      setCameraPosition([0, 0, 4.5]) // Slight zoom when speaking
    } else {
      setCameraPosition([0, 0, 5]) // Default position
    }
  }, [isListening, isSpeaking])

  return (
    <div className="w-full h-96 relative">
      <Canvas camera={{ position: cameraPosition, fov: 50 }}>
        <Environment />
        <HumanHead 
          isListening={isListening} 
          isSpeaking={isSpeaking} 
          emotion={emotion} 
        />
        <OrbitControls 
          enableZoom={false} 
          enablePan={false}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 3}
        />
      </Canvas>
      
      {/* Status Indicators */}
      <div className="absolute top-4 left-4 space-y-2">
        {isListening && (
          <div className="flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Listening...</span>
          </div>
        )}
        {isSpeaking && (
          <div className="flex items-center space-x-2 bg-blue-500 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Speaking...</span>
          </div>
        )}
      </div>

      {/* Message Display */}
      {message && (
        <div className="absolute bottom-4 left-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
          <p className="text-sm text-gray-800">{message}</p>
        </div>
      )}
    </div>
  )
}
