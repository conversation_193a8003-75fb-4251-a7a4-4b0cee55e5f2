'use client'

import { useRef, useEffect, useState } from 'react'
import { <PERSON>vas, useFrame, useThree } from '@react-three/fiber'
import { OrbitControls, Text, Sphere, Box, Cylinder, Cone } from '@react-three/drei'
import * as THREE from 'three'

interface AvatarProps {
  isListening: boolean
  isSpeaking: boolean
  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'
  message?: string
  lipSyncData?: {
    mouthOpenness: number
    mouthWidth: number
    phoneme: string
  }
  facialData?: {
    eyeBlinkLeft: number
    eyeBlinkRight: number
    eyebrowLeft: number
    eyebrowRight: number
    cheekPuff: number
    jawOpen: number
  }
}

// Advanced Realistic Human Head Component
function RealisticHumanHead({
  isListening,
  isSpeaking,
  emotion,
  lipSyncData,
  facialData
}: Omit<AvatarProps, 'message'>) {
  // Head and facial feature refs
  const headRef = useRef<THREE.Group>(null)
  const faceRef = useRef<THREE.Mesh>(null)
  const leftEyeRef = useRef<THREE.Group>(null)
  const rightEyeRef = useRef<THREE.Group>(null)
  const leftEyeballRef = useRef<THREE.Mesh>(null)
  const rightEyeballRef = useRef<THREE.Mesh>(null)
  const leftEyelidRef = useRef<THREE.Mesh>(null)
  const rightEyelidRef = useRef<THREE.Mesh>(null)
  const mouthRef = useRef<THREE.Group>(null)
  const upperLipRef = useRef<THREE.Mesh>(null)
  const lowerLipRef = useRef<THREE.Mesh>(null)
  const teethRef = useRef<THREE.Mesh>(null)
  const tongueRef = useRef<THREE.Mesh>(null)
  const leftEyebrowRef = useRef<THREE.Mesh>(null)
  const rightEyebrowRef = useRef<THREE.Mesh>(null)
  const noseRef = useRef<THREE.Group>(null)
  const leftCheekRef = useRef<THREE.Mesh>(null)
  const rightCheekRef = useRef<THREE.Mesh>(null)
  const jawRef = useRef<THREE.Mesh>(null)

  // Animation states
  const [blinkTimer, setBlinkTimer] = useState(0)
  const [mouthAnimation, setMouthAnimation] = useState(0)
  const [eyeMovement, setEyeMovement] = useState({ x: 0, y: 0 })
  const [headMovement, setHeadMovement] = useState({ x: 0, y: 0, z: 0 })
  const [breathingPhase, setBreathingPhase] = useState(0)

  // Advanced Animation Loop
  useFrame((state, delta) => {
    if (!headRef.current) return

    const time = state.clock.elapsedTime

    // Natural breathing animation
    setBreathingPhase(prev => prev + delta * 0.3)
    const breathingIntensity = 1 + Math.sin(breathingPhase) * 0.015
    if (headRef.current) {
      headRef.current.scale.setScalar(breathingIntensity)
    }

    // Realistic blinking system
    setBlinkTimer(prev => prev + delta)
    if (blinkTimer > (2 + Math.random() * 3)) { // Random blink interval 2-5 seconds
      setBlinkTimer(0)

      // Smooth eyelid animation
      if (leftEyelidRef.current && rightEyelidRef.current) {
        const blinkDuration = 0.15
        const blinkProgress = Math.min((time % blinkDuration) / blinkDuration, 1)
        const blinkValue = Math.sin(blinkProgress * Math.PI)

        leftEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9)
        rightEyelidRef.current.scale.y = Math.max(0.1, 1 - blinkValue * 0.9)
      }
    }

    // Advanced lip-sync animation
    if (isSpeaking && lipSyncData) {
      // Upper and lower lip movement based on phonemes
      if (upperLipRef.current && lowerLipRef.current) {
        const mouthOpen = lipSyncData.mouthOpenness * 0.8
        const mouthWidth = lipSyncData.mouthWidth * 0.6

        upperLipRef.current.position.y = 0.1 + mouthOpen * 0.1
        lowerLipRef.current.position.y = -0.1 - mouthOpen * 0.15
        upperLipRef.current.scale.x = 1 + mouthWidth * 0.3
        lowerLipRef.current.scale.x = 1 + mouthWidth * 0.3

        // Jaw movement
        if (jawRef.current) {
          jawRef.current.position.y = -mouthOpen * 0.1
          jawRef.current.rotation.x = mouthOpen * 0.2
        }

        // Tongue visibility for certain phonemes
        if (tongueRef.current) {
          const showTongue = ['L', 'TH', 'R'].includes(lipSyncData.phoneme)
          tongueRef.current.visible = showTongue
          if (showTongue) {
            tongueRef.current.position.z = 0.05 + mouthOpen * 0.1
          }
        }
      }
    } else {
      // Return to neutral position
      if (upperLipRef.current && lowerLipRef.current && jawRef.current) {
        upperLipRef.current.position.y = THREE.MathUtils.lerp(upperLipRef.current.position.y, 0.1, delta * 8)
        lowerLipRef.current.position.y = THREE.MathUtils.lerp(lowerLipRef.current.position.y, -0.1, delta * 8)
        jawRef.current.position.y = THREE.MathUtils.lerp(jawRef.current.position.y, 0, delta * 8)
        jawRef.current.rotation.x = THREE.MathUtils.lerp(jawRef.current.rotation.x, 0, delta * 8)

        if (tongueRef.current) {
          tongueRef.current.visible = false
        }
      }
    }

    // Facial expression animation based on facialData
    if (facialData) {
      // Eyebrow movement
      if (leftEyebrowRef.current && rightEyebrowRef.current) {
        leftEyebrowRef.current.position.y = 0.4 + facialData.eyebrowLeft * 0.1
        rightEyebrowRef.current.position.y = 0.4 + facialData.eyebrowRight * 0.1
        leftEyebrowRef.current.rotation.z = facialData.eyebrowLeft * 0.2
        rightEyebrowRef.current.rotation.z = -facialData.eyebrowRight * 0.2
      }

      // Cheek movement for smiling/expressions
      if (leftCheekRef.current && rightCheekRef.current) {
        const cheekRaise = facialData.cheekPuff
        leftCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2)
        rightCheekRef.current.scale.setScalar(1 + cheekRaise * 0.2)
        leftCheekRef.current.position.y = -0.1 + cheekRaise * 0.05
        rightCheekRef.current.position.y = -0.1 + cheekRaise * 0.05
      }
    }

    // Natural eye movement and gaze
    setEyeMovement(prev => ({
      x: prev.x + (Math.random() - 0.5) * 0.001,
      y: prev.y + (Math.random() - 0.5) * 0.001
    }))

    if (leftEyeballRef.current && rightEyeballRef.current) {
      const gazeX = Math.sin(time * 0.3) * 0.1 + eyeMovement.x
      const gazeY = Math.cos(time * 0.4) * 0.05 + eyeMovement.y

      leftEyeballRef.current.position.x = gazeX
      leftEyeballRef.current.position.y = gazeY
      rightEyeballRef.current.position.x = gazeX
      rightEyeballRef.current.position.y = gazeY
    }

    // Listening animation - subtle head movement
    if (isListening) {
      setHeadMovement(prev => ({
        x: Math.sin(time * 1.2) * 0.05,
        y: Math.cos(time * 0.8) * 0.03,
        z: Math.sin(time * 1.5) * 0.08
      }))
    } else {
      setHeadMovement(prev => ({
        x: THREE.MathUtils.lerp(prev.x, Math.sin(time * 0.5) * 0.02, delta * 2),
        y: THREE.MathUtils.lerp(prev.y, Math.cos(time * 0.3) * 0.01, delta * 2),
        z: THREE.MathUtils.lerp(prev.z, 0, delta * 3)
      }))
    }

    if (headRef.current) {
      headRef.current.rotation.x = headMovement.x
      headRef.current.rotation.y = headMovement.y
      headRef.current.rotation.z = headMovement.z
    }
  })

  // Emotion-based expressions
  const getEmotionColors = () => {
    switch (emotion) {
      case 'happy':
        return { skin: '#FFE4C4', cheek: '#FFB6C1' }
      case 'sad':
        return { skin: '#F5DEB3', cheek: '#D3D3D3' }
      case 'concerned':
        return { skin: '#FAEBD7', cheek: '#DDA0DD' }
      case 'empathetic':
        return { skin: '#FFF8DC', cheek: '#F0E68C' }
      default:
        return { skin: '#FDBCB4', cheek: '#FFB6C1' }
    }
  }

  const colors = getEmotionColors()

  return (
    <group ref={headRef} position={[0, 0, 0]}>
      {/* Main Head Structure */}
      <Sphere ref={faceRef} args={[1.2, 64, 64]} position={[0, 0, 0]}>
        <meshStandardMaterial
          color={colors.skin}
          roughness={0.8}
          metalness={0.1}
        />
      </Sphere>

      {/* Detailed Eye Structure */}
      <group ref={leftEyeRef} position={[-0.35, 0.25, 0.85]}>
        {/* Eye socket */}
        <Sphere args={[0.18, 32, 32]} position={[0, 0, -0.05]}>
          <meshStandardMaterial color={colors.skin} />
        </Sphere>
        {/* Eyeball */}
        <Sphere ref={leftEyeballRef} args={[0.15, 32, 32]} position={[0, 0, 0]}>
          <meshStandardMaterial color="#FFFFFF" />
        </Sphere>
        {/* Iris */}
        <Sphere args={[0.08, 32, 32]} position={[0, 0, 0.12]}>
          <meshStandardMaterial color="#4A90E2" />
        </Sphere>
        {/* Pupil */}
        <Sphere args={[0.04, 32, 32]} position={[0, 0, 0.13]}>
          <meshStandardMaterial color="#000000" />
        </Sphere>
        {/* Eyelid */}
        <Sphere ref={leftEyelidRef} args={[0.16, 32, 16]} position={[0, 0.05, 0.1]} scale={[1, 1, 0.3]}>
          <meshStandardMaterial color={colors.skin} />
        </Sphere>
      </group>

      <group ref={rightEyeRef} position={[0.35, 0.25, 0.85]}>
        {/* Eye socket */}
        <Sphere args={[0.18, 32, 32]} position={[0, 0, -0.05]}>
          <meshStandardMaterial color={colors.skin} />
        </Sphere>
        {/* Eyeball */}
        <Sphere ref={rightEyeballRef} args={[0.15, 32, 32]} position={[0, 0, 0]}>
          <meshStandardMaterial color="#FFFFFF" />
        </Sphere>
        {/* Iris */}
        <Sphere args={[0.08, 32, 32]} position={[0, 0, 0.12]}>
          <meshStandardMaterial color="#4A90E2" />
        </Sphere>
        {/* Pupil */}
        <Sphere args={[0.04, 32, 32]} position={[0, 0, 0.13]}>
          <meshStandardMaterial color="#000000" />
        </Sphere>
        {/* Eyelid */}
        <Sphere ref={rightEyelidRef} args={[0.16, 32, 16]} position={[0, 0.05, 0.1]} scale={[1, 1, 0.3]}>
          <meshStandardMaterial color={colors.skin} />
        </Sphere>
      </group>

      {/* Detailed Eyebrows */}
      <Box
        ref={leftEyebrowRef}
        args={[0.35, 0.08, 0.12]}
        position={[-0.35, 0.45, 0.8]}
        rotation={[0, 0, emotion === 'concerned' ? 0.4 : emotion === 'happy' ? -0.1 : 0]}
      >
        <meshStandardMaterial color="#654321" roughness={0.9} />
      </Box>
      <Box
        ref={rightEyebrowRef}
        args={[0.35, 0.08, 0.12]}
        position={[0.35, 0.45, 0.8]}
        rotation={[0, 0, emotion === 'concerned' ? -0.4 : emotion === 'happy' ? 0.1 : 0]}
      >
        <meshStandardMaterial color="#654321" roughness={0.9} />
      </Box>

      {/* Detailed Nose Structure */}
      <group ref={noseRef} position={[0, 0.05, 0.95]}>
        {/* Nose bridge */}
        <Box args={[0.08, 0.3, 0.15]} position={[0, 0.1, 0]}>
          <meshStandardMaterial color={colors.skin} />
        </Box>
        {/* Nose tip */}
        <Sphere args={[0.06, 16, 16]} position={[0, -0.05, 0.05]}>
          <meshStandardMaterial color={colors.skin} />
        </Sphere>
        {/* Nostrils */}
        <Sphere args={[0.02, 8, 8]} position={[-0.03, -0.08, 0.02]}>
          <meshStandardMaterial color="#8B4513" />
        </Sphere>
        <Sphere args={[0.02, 8, 8]} position={[0.03, -0.08, 0.02]}>
          <meshStandardMaterial color="#8B4513" />
        </Sphere>
      </group>

      {/* Advanced Mouth Structure */}
      <group ref={mouthRef} position={[0, -0.25, 0.85]}>
        {/* Upper Lip */}
        <Sphere
          ref={upperLipRef}
          args={[0.25, 32, 16]}
          position={[0, 0.1, 0]}
          scale={[1, 0.4, 0.8]}
          rotation={[0, 0, emotion === 'happy' ? 0.2 : emotion === 'sad' ? -0.2 : 0]}
        >
          <meshStandardMaterial color="#CD5C5C" />
        </Sphere>

        {/* Lower Lip */}
        <Sphere
          ref={lowerLipRef}
          args={[0.28, 32, 16]}
          position={[0, -0.1, 0]}
          scale={[1, 0.5, 0.8]}
          rotation={[0, 0, emotion === 'happy' ? 0.15 : emotion === 'sad' ? -0.15 : 0]}
        >
          <meshStandardMaterial color="#B22222" />
        </Sphere>

        {/* Teeth */}
        <Box ref={teethRef} args={[0.3, 0.08, 0.05]} position={[0, 0, 0.05]}>
          <meshStandardMaterial color="#FFFACD" />
        </Box>

        {/* Tongue */}
        <Sphere
          ref={tongueRef}
          args={[0.15, 16, 16]}
          position={[0, -0.05, 0]}
          scale={[1, 0.6, 1.5]}
          visible={false}
        >
          <meshStandardMaterial color="#FF69B4" />
        </Sphere>
      </group>

      {/* Jaw */}
      <Sphere ref={jawRef} args={[0.8, 32, 32]} position={[0, -0.6, 0.3]} scale={[1.2, 0.8, 1]}>
        <meshStandardMaterial color={colors.skin} />
      </Sphere>

      {/* Cheeks */}
      <Sphere ref={leftCheekRef} args={[0.15, 32, 32]} position={[-0.7, -0.1, 0.6]}>
        <meshStandardMaterial
          color={emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin}
          transparent
          opacity={emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1}
        />
      </Sphere>
      <Sphere ref={rightCheekRef} args={[0.15, 32, 32]} position={[0.7, -0.1, 0.6]}>
        <meshStandardMaterial
          color={emotion === 'happy' || emotion === 'empathetic' ? colors.cheek : colors.skin}
          transparent
          opacity={emotion === 'happy' || emotion === 'empathetic' ? 0.8 : 1}
        />
      </Sphere>

      {/* Realistic Hair */}
      <group position={[0, 0.4, -0.1]}>
        <Sphere args={[1.35, 32, 32]} position={[0, 0, 0]} scale={[1, 1.2, 0.8]}>
          <meshStandardMaterial color="#654321" roughness={0.8} />
        </Sphere>
        {/* Hair strands for detail */}
        {Array.from({ length: 20 }, (_, i) => (
          <Cylinder
            key={i}
            args={[0.01, 0.01, 0.3]}
            position={[
              (Math.random() - 0.5) * 2,
              Math.random() * 0.5,
              (Math.random() - 0.5) * 1.5
            ]}
            rotation={[
              Math.random() * 0.5,
              Math.random() * Math.PI * 2,
              Math.random() * 0.5
            ]}
          >
            <meshStandardMaterial color="#543A2F" />
          </Cylinder>
        ))}
      </group>

      {/* Neck with realistic proportions */}
      <Cylinder args={[0.25, 0.3, 0.8]} position={[0, -1.2, 0]}>
        <meshStandardMaterial color={colors.skin} />
      </Cylinder>

      {/* Shoulders and clothing */}
      <group position={[0, -1.8, 0]}>
        <Box args={[2.2, 0.4, 0.8]}>
          <meshStandardMaterial color="#4A90E2" />
        </Box>
        {/* Collar */}
        <Box args={[0.6, 0.1, 0.1]} position={[0, 0.2, 0.4]}>
          <meshStandardMaterial color="#2C5282" />
        </Box>
      </group>
    </group>
  )
}

// Advanced Lighting Environment for Realistic Rendering
function Environment() {
  return (
    <>
      {/* Advanced Lighting Setup */}
      <ambientLight intensity={0.4} color="#ffffff" />

      {/* Key Light - Main illumination */}
      <directionalLight
        position={[5, 8, 5]}
        intensity={1.2}
        color="#ffffff"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />

      {/* Fill Light - Soften shadows */}
      <directionalLight
        position={[-3, 4, 2]}
        intensity={0.6}
        color="#E6F3FF"
      />

      {/* Rim Light - Edge definition */}
      <pointLight
        position={[0, 2, -3]}
        intensity={0.8}
        color="#FFE4B5"
      />

      {/* Face Light - Enhance facial features */}
      <spotLight
        position={[0, 1, 4]}
        angle={0.3}
        penumbra={0.5}
        intensity={0.5}
        color="#FFFACD"
        target-position={[0, 0, 0]}
      />

      {/* Background with gradient effect */}
      <Sphere args={[50]} position={[0, 0, -25]}>
        <meshBasicMaterial
          color="#E8F4FD"
          side={THREE.BackSide}
        />
      </Sphere>

      {/* Subtle floor reflection */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -3, 0]} receiveShadow>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial
          color="#F0F8FF"
          transparent
          opacity={0.3}
          roughness={0.1}
          metalness={0.1}
        />
      </mesh>
    </>
  )
}

// Main Avatar Component with Advanced Features
export default function HumanAvatar({
  isListening,
  isSpeaking,
  emotion,
  message,
  lipSyncData,
  facialData
}: AvatarProps) {
  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 0, 4.5])
  const [currentLipSync, setCurrentLipSync] = useState({
    mouthOpenness: 0,
    mouthWidth: 0,
    phoneme: 'silence'
  })
  const [currentFacialData, setCurrentFacialData] = useState({
    eyeBlinkLeft: 1,
    eyeBlinkRight: 1,
    eyebrowLeft: 0,
    eyebrowRight: 0,
    cheekPuff: 0,
    jawOpen: 0
  })

  // Update lip sync data when speaking
  useEffect(() => {
    if (isSpeaking && message) {
      // Simple phoneme extraction from message
      const words = message.split(' ')
      let phonemeIndex = 0

      const animateLipSync = () => {
        if (phonemeIndex < message.length && isSpeaking) {
          const char = message[phonemeIndex].toLowerCase()
          let phoneme = 'silence'
          let mouthOpenness = 0.2
          let mouthWidth = 0.5

          // Enhanced phoneme mapping
          if ('aeiou'.includes(char)) {
            phoneme = char.toUpperCase()
            mouthOpenness = char === 'a' ? 0.8 : char === 'o' ? 0.9 : char === 'i' ? 0.3 : 0.6
            mouthWidth = char === 'i' ? 0.9 : char === 'o' ? 0.4 : 0.6
          } else if ('mbp'.includes(char)) {
            phoneme = char.toUpperCase()
            mouthOpenness = 0.1
            mouthWidth = 0.5
          } else if ('fv'.includes(char)) {
            phoneme = char.toUpperCase()
            mouthOpenness = 0.3
            mouthWidth = 0.7
          } else if ('sz'.includes(char)) {
            phoneme = 'S'
            mouthOpenness = 0.2
            mouthWidth = 0.6
          } else if ('rl'.includes(char)) {
            phoneme = char.toUpperCase()
            mouthOpenness = 0.4
            mouthWidth = 0.6
          }

          setCurrentLipSync({ mouthOpenness, mouthWidth, phoneme })
          phonemeIndex++

          setTimeout(animateLipSync, 80 + Math.random() * 40) // Variable speed
        } else {
          setCurrentLipSync({ mouthOpenness: 0.1, mouthWidth: 0.5, phoneme: 'silence' })
        }
      }

      animateLipSync()
    } else {
      setCurrentLipSync({ mouthOpenness: 0.1, mouthWidth: 0.5, phoneme: 'silence' })
    }
  }, [isSpeaking, message])

  // Update facial expressions based on emotion
  useEffect(() => {
    const emotionMappings = {
      happy: { eyebrowLeft: 0.2, eyebrowRight: 0.2, cheekPuff: 0.4 },
      sad: { eyebrowLeft: -0.3, eyebrowRight: -0.3, cheekPuff: 0 },
      concerned: { eyebrowLeft: -0.2, eyebrowRight: 0.1, cheekPuff: 0 },
      empathetic: { eyebrowLeft: 0.1, eyebrowRight: 0.1, cheekPuff: 0.2 },
      neutral: { eyebrowLeft: 0, eyebrowRight: 0, cheekPuff: 0 }
    }

    const mapping = emotionMappings[emotion]
    setCurrentFacialData(prev => ({
      ...prev,
      ...mapping
    }))
  }, [emotion])

  // Dynamic camera positioning
  useEffect(() => {
    if (isListening) {
      setCameraPosition([0.2, 0.1, 3.8]) // Slight angle when listening
    } else if (isSpeaking) {
      setCameraPosition([0, 0, 4.2]) // Centered when speaking
    } else {
      setCameraPosition([0, 0, 4.5]) // Default position
    }
  }, [isListening, isSpeaking])

  return (
    <div className="w-full h-96 relative">
      <Canvas
        camera={{
          position: cameraPosition,
          fov: 45,
          near: 0.1,
          far: 1000
        }}
        shadows
      >
        <Environment />
        <RealisticHumanHead
          isListening={isListening}
          isSpeaking={isSpeaking}
          emotion={emotion}
          lipSyncData={lipSyncData || currentLipSync}
          facialData={facialData || currentFacialData}
        />
        <OrbitControls
          enableZoom={true}
          enablePan={false}
          maxDistance={8}
          minDistance={3}
          maxPolarAngle={Math.PI / 1.8}
          minPolarAngle={Math.PI / 4}
          enableDamping={true}
          dampingFactor={0.05}
        />
      </Canvas>
      
      {/* Status Indicators */}
      <div className="absolute top-4 left-4 space-y-2">
        {isListening && (
          <div className="flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Listening...</span>
          </div>
        )}
        {isSpeaking && (
          <div className="flex items-center space-x-2 bg-blue-500 text-white px-3 py-1 rounded-full text-sm">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>Speaking...</span>
          </div>
        )}
      </div>

      {/* Message Display */}
      {message && (
        <div className="absolute bottom-4 left-4 right-4 bg-white bg-opacity-90 rounded-lg p-3 shadow-lg">
          <p className="text-sm text-gray-800">{message}</p>
        </div>
      )}
    </div>
  )
}
