'use client'

import { useRef, useEffect, useState } from 'react'

interface FacialAnimationProps {
  audioData?: Float32Array
  text?: string
  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'
  isActive: boolean
  onAnimationUpdate: (animationData: FacialAnimationData) => void
}

export interface FacialAnimationData {
  mouthOpenness: number
  eyeBlinkLeft: number
  eyeBlinkRight: number
  eyebrowPosition: number
  headRotation: { x: number; y: number; z: number }
  emotion: string
  intensity: number
}

// Advanced Phoneme to mouth shape mapping for realistic lip-sync
const PHONEME_MOUTH_SHAPES = {
  'A': { openness: 0.85, width: 0.65, lipRounding: 0.2, tonguePosition: 0.1 },
  'E': { openness: 0.55, width: 0.85, lipRounding: 0.1, tonguePosition: 0.3 },
  'I': { openness: 0.25, width: 0.95, lipRounding: 0.0, tonguePosition: 0.8 },
  'O': { openness: 0.9, width: 0.35, lipRounding: 0.9, tonguePosition: 0.2 },
  'U': { openness: 0.65, width: 0.25, lipRounding: 0.95, tonguePosition: 0.1 },
  'M': { openness: 0.05, width: 0.5, lipRounding: 0.3, tonguePosition: 0.0 },
  'B': { openness: 0.08, width: 0.5, lipRounding: 0.2, tonguePosition: 0.0 },
  'P': { openness: 0.02, width: 0.45, lipRounding: 0.4, tonguePosition: 0.0 },
  'F': { openness: 0.35, width: 0.7, lipRounding: 0.1, tonguePosition: 0.2 },
  'V': { openness: 0.32, width: 0.72, lipRounding: 0.1, tonguePosition: 0.2 },
  'TH': { openness: 0.25, width: 0.8, lipRounding: 0.0, tonguePosition: 0.9 },
  'S': { openness: 0.18, width: 0.65, lipRounding: 0.0, tonguePosition: 0.7 },
  'SH': { openness: 0.4, width: 0.5, lipRounding: 0.6, tonguePosition: 0.5 },
  'R': { openness: 0.45, width: 0.6, lipRounding: 0.3, tonguePosition: 0.6 },
  'L': { openness: 0.35, width: 0.75, lipRounding: 0.1, tonguePosition: 0.8 },
  'T': { openness: 0.2, width: 0.6, lipRounding: 0.0, tonguePosition: 0.9 },
  'D': { openness: 0.25, width: 0.65, lipRounding: 0.0, tonguePosition: 0.85 },
  'N': { openness: 0.15, width: 0.6, lipRounding: 0.0, tonguePosition: 0.8 },
  'K': { openness: 0.3, width: 0.5, lipRounding: 0.0, tonguePosition: 0.3 },
  'G': { openness: 0.35, width: 0.55, lipRounding: 0.0, tonguePosition: 0.3 },
  'silence': { openness: 0.08, width: 0.5, lipRounding: 0.2, tonguePosition: 0.0 }
}

// Advanced emotion-based facial expressions with micro-expressions
const EMOTION_EXPRESSIONS = {
  neutral: {
    eyebrowHeight: 0,
    eyebrowAngle: 0,
    mouthCurve: 0,
    mouthCorners: 0,
    eyeOpenness: 1,
    eyeSquint: 0,
    cheekRaise: 0,
    nostrilFlare: 0,
    jawTension: 0,
    foreheadWrinkle: 0,
    lipTension: 0
  },
  happy: {
    eyebrowHeight: 0.15,
    eyebrowAngle: 0.1,
    mouthCurve: 0.7,
    mouthCorners: 0.8,
    eyeOpenness: 0.85,
    eyeSquint: 0.3,
    cheekRaise: 0.6,
    nostrilFlare: 0.1,
    jawTension: 0,
    foreheadWrinkle: 0,
    lipTension: 0.2
  },
  sad: {
    eyebrowHeight: -0.4,
    eyebrowAngle: -0.3,
    mouthCurve: -0.5,
    mouthCorners: -0.6,
    eyeOpenness: 0.7,
    eyeSquint: 0.1,
    cheekRaise: -0.2,
    nostrilFlare: 0,
    jawTension: 0.2,
    foreheadWrinkle: 0.4,
    lipTension: 0.3
  },
  concerned: {
    eyebrowHeight: -0.25,
    eyebrowAngle: -0.4,
    mouthCurve: -0.15,
    mouthCorners: -0.2,
    eyeOpenness: 1.1,
    eyeSquint: 0,
    cheekRaise: 0,
    nostrilFlare: 0.2,
    jawTension: 0.3,
    foreheadWrinkle: 0.6,
    lipTension: 0.4
  },
  empathetic: {
    eyebrowHeight: 0.1,
    eyebrowAngle: 0.2,
    mouthCurve: 0.3,
    mouthCorners: 0.4,
    eyeOpenness: 0.95,
    eyeSquint: 0.1,
    cheekRaise: 0.3,
    nostrilFlare: 0,
    jawTension: 0,
    foreheadWrinkle: 0.1,
    lipTension: 0.1
  }
}

export default function FacialAnimationEngine({
  audioData,
  text,
  emotion,
  isActive,
  onAnimationUpdate
}: FacialAnimationProps) {
  const [currentPhoneme, setCurrentPhoneme] = useState('silence')
  const [blinkTimer, setBlinkTimer] = useState(0)
  const [emotionIntensity, setEmotionIntensity] = useState(1)
  const animationFrameRef = useRef<number>()
  const audioContextRef = useRef<AudioContext>()
  const analyserRef = useRef<AnalyserNode>()

  // Advanced phoneme detection from text with context awareness
  const detectPhonemeFromText = (text: string, position: number): string => {
    if (!text || position >= text.length) return 'silence'

    const char = text[position].toLowerCase()
    const nextChar = position + 1 < text.length ? text[position + 1].toLowerCase() : ''
    const prevChar = position > 0 ? text[position - 1].toLowerCase() : ''

    // Advanced phoneme mapping with digraphs and context
    const phonemeMap: { [key: string]: string } = {
      // Vowels
      'a': 'A', 'e': 'E', 'i': 'I', 'o': 'O', 'u': 'U',

      // Consonants - Bilabials
      'm': 'M', 'b': 'B', 'p': 'P',

      // Labiodentals
      'f': 'F', 'v': 'V',

      // Alveolars
      't': 'T', 'd': 'D', 'n': 'N', 's': 'S', 'z': 'S', 'l': 'L', 'r': 'R',

      // Velars
      'k': 'K', 'g': 'G',

      // Special cases
      'c': nextChar === 'h' ? 'SH' : 'K',
      'j': 'SH',
      'w': 'U',
      'y': 'I',
      'q': 'K',
      'x': 'S',

      // Silence
      ' ': 'silence',
      '.': 'silence',
      ',': 'silence',
      '!': 'silence',
      '?': 'silence'
    }

    // Handle digraphs
    if (char === 't' && nextChar === 'h') return 'TH'
    if (char === 's' && nextChar === 'h') return 'SH'
    if (char === 'c' && nextChar === 'h') return 'SH'
    if (char === 'p' && nextChar === 'h') return 'F'
    if (char === 'g' && nextChar === 'h') return 'G'

    // Handle silent letters
    if (char === 'h' && prevChar !== '') return 'silence'
    if (char === 'w' && prevChar === 's') return 'silence'
    if (char === 'b' && prevChar === 'm') return 'silence'
    if (char === 'l' && prevChar === 'a' && nextChar === 'k') return 'silence'

    return phonemeMap[char] || 'silence'
  }

  // Audio analysis for lip-sync
  const analyzeAudio = (audioData: Float32Array): number => {
    if (!audioData) return 0
    
    let sum = 0
    for (let i = 0; i < audioData.length; i++) {
      sum += Math.abs(audioData[i])
    }
    return sum / audioData.length
  }

  // Generate realistic blinking
  const generateBlink = (deltaTime: number): { left: number; right: number } => {
    setBlinkTimer(prev => prev + deltaTime)
    
    // Random blinking every 2-4 seconds
    const blinkInterval = 2000 + Math.random() * 2000
    
    if (blinkTimer > blinkInterval) {
      setBlinkTimer(0)
      // Quick blink animation
      const blinkPhase = (Date.now() % 200) / 200
      const blinkValue = Math.sin(blinkPhase * Math.PI)
      return { left: blinkValue, right: blinkValue }
    }
    
    return { left: 1, right: 1 }
  }

  // Main animation loop
  useEffect(() => {
    if (!isActive) return

    let textPosition = 0
    let lastTime = Date.now()
    
    const animate = () => {
      const currentTime = Date.now()
      const deltaTime = currentTime - lastTime
      lastTime = currentTime

      // Text-based lip-sync animation
      if (text && textPosition < text.length) {
        const phoneme = detectPhonemeFromText(text, Math.floor(textPosition))
        setCurrentPhoneme(phoneme)
        
        // Advance through text at speaking pace (adjust speed as needed)
        textPosition += deltaTime * 0.01 // Adjust this multiplier for speaking speed
      } else {
        setCurrentPhoneme('silence')
      }

      // Audio-based animation (if available)
      let audioIntensity = 0
      if (audioData) {
        audioIntensity = analyzeAudio(audioData)
      }

      // Generate advanced facial animation data
      const mouthShape = PHONEME_MOUTH_SHAPES[currentPhoneme as keyof typeof PHONEME_MOUTH_SHAPES] || PHONEME_MOUTH_SHAPES.silence
      const expression = EMOTION_EXPRESSIONS[emotion]
      const blink = generateBlink(deltaTime)

      // Calculate micro-expressions and natural variations
      const naturalVariation = {
        eyebrow: Math.sin(currentTime * 0.002) * 0.02,
        mouth: Math.cos(currentTime * 0.003) * 0.01,
        eye: Math.sin(currentTime * 0.004) * 0.01
      }

      const animationData: FacialAnimationData = {
        mouthOpenness: Math.max(0, Math.min(1,
          mouthShape.openness +
          audioIntensity * 0.4 +
          expression.mouthCurve * 0.2 +
          naturalVariation.mouth
        )),
        eyeBlinkLeft: blink.left * (1 + expression.eyeSquint * 0.3),
        eyeBlinkRight: blink.right * (1 + expression.eyeSquint * 0.3),
        eyebrowPosition: expression.eyebrowHeight + naturalVariation.eyebrow,
        headRotation: {
          x: Math.sin(currentTime * 0.0008) * 0.04 + expression.jawTension * 0.02,
          y: Math.sin(currentTime * 0.0006) * 0.025 + (audioIntensity * 0.05),
          z: Math.sin(currentTime * 0.001) * 0.015 + expression.foreheadWrinkle * 0.01
        },
        emotion,
        intensity: emotionIntensity * (1 + Math.sin(currentTime * 0.005) * 0.1)
      }

      // Add breathing influence on facial features
      const breathingInfluence = Math.sin(currentTime * 0.0015) * 0.008
      animationData.mouthOpenness += breathingInfluence
      animationData.eyebrowPosition += breathingInfluence * 0.5

      onAnimationUpdate(animationData)

      if (isActive) {
        animationFrameRef.current = requestAnimationFrame(animate)
      }
    }

    animate()

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isActive, text, emotion, audioData, onAnimationUpdate])

  // Setup audio analysis
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      analyserRef.current.fftSize = 256
    }

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  return null // This is a logic-only component
}

// Utility function to convert text to estimated phonemes
export function textToPhonemes(text: string): Array<{ phoneme: string; duration: number }> {
  const words = text.split(' ')
  const phonemes: Array<{ phoneme: string; duration: number }> = []
  
  words.forEach((word, wordIndex) => {
    for (let i = 0; i < word.length; i++) {
      const char = word[i].toLowerCase()
      let phoneme = 'silence'
      
      // Simple character to phoneme mapping
      if ('aeiou'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('mbp'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('fv'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('sz'.includes(char)) {
        phoneme = 'S'
      } else if ('rl'.includes(char)) {
        phoneme = char.toUpperCase()
      }
      
      phonemes.push({
        phoneme,
        duration: 100 + Math.random() * 100 // Random duration between 100-200ms
      })
    }
    
    // Add pause between words
    if (wordIndex < words.length - 1) {
      phonemes.push({ phoneme: 'silence', duration: 200 })
    }
  })
  
  return phonemes
}

// Advanced emotion detection from text
export function detectEmotionFromText(text: string): 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic' {
  const lowerText = text.toLowerCase()
  
  // Happy indicators
  if (lowerText.includes('happy') || lowerText.includes('joy') || lowerText.includes('great') || 
      lowerText.includes('wonderful') || lowerText.includes('excited') || lowerText.includes('😊') ||
      lowerText.includes('good') || lowerText.includes('amazing')) {
    return 'happy'
  }
  
  // Sad indicators
  if (lowerText.includes('sad') || lowerText.includes('depressed') || lowerText.includes('cry') ||
      lowerText.includes('hurt') || lowerText.includes('pain') || lowerText.includes('😢') ||
      lowerText.includes('terrible') || lowerText.includes('awful')) {
    return 'sad'
  }
  
  // Concerned indicators
  if (lowerText.includes('worried') || lowerText.includes('anxious') || lowerText.includes('scared') ||
      lowerText.includes('afraid') || lowerText.includes('nervous') || lowerText.includes('stress') ||
      lowerText.includes('concerned') || lowerText.includes('help')) {
    return 'concerned'
  }
  
  // Empathetic indicators
  if (lowerText.includes('understand') || lowerText.includes('support') || lowerText.includes('care') ||
      lowerText.includes('here for you') || lowerText.includes('listen') || lowerText.includes('comfort')) {
    return 'empathetic'
  }
  
  return 'neutral'
}
