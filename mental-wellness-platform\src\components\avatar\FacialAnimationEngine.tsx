'use client'

import { useRef, useEffect, useState } from 'react'

interface FacialAnimationProps {
  audioData?: Float32Array
  text?: string
  emotion: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'
  isActive: boolean
  onAnimationUpdate: (animationData: FacialAnimationData) => void
}

export interface FacialAnimationData {
  mouthOpenness: number
  eyeBlinkLeft: number
  eyeBlinkRight: number
  eyebrowPosition: number
  headRotation: { x: number; y: number; z: number }
  emotion: string
  intensity: number
}

// Phoneme to mouth shape mapping for lip-sync
const PHONEME_MOUTH_SHAPES = {
  'A': { openness: 0.8, width: 0.6 },
  'E': { openness: 0.6, width: 0.8 },
  'I': { openness: 0.3, width: 0.9 },
  'O': { openness: 0.9, width: 0.4 },
  'U': { openness: 0.7, width: 0.3 },
  'M': { openness: 0.1, width: 0.5 },
  'B': { openness: 0.1, width: 0.5 },
  'P': { openness: 0.1, width: 0.5 },
  'F': { openness: 0.3, width: 0.7 },
  'V': { openness: 0.3, width: 0.7 },
  'TH': { openness: 0.2, width: 0.8 },
  'S': { openness: 0.2, width: 0.6 },
  'SH': { openness: 0.4, width: 0.5 },
  'R': { openness: 0.4, width: 0.6 },
  'L': { openness: 0.3, width: 0.7 },
  'silence': { openness: 0.1, width: 0.5 }
}

// Emotion-based facial expressions
const EMOTION_EXPRESSIONS = {
  neutral: {
    eyebrowHeight: 0,
    mouthCurve: 0,
    eyeOpenness: 1,
    cheekRaise: 0
  },
  happy: {
    eyebrowHeight: 0.2,
    mouthCurve: 0.6,
    eyeOpenness: 0.8,
    cheekRaise: 0.4
  },
  sad: {
    eyebrowHeight: -0.3,
    mouthCurve: -0.4,
    eyeOpenness: 0.6,
    cheekRaise: 0
  },
  concerned: {
    eyebrowHeight: -0.2,
    mouthCurve: -0.2,
    eyeOpenness: 1.2,
    cheekRaise: 0
  },
  empathetic: {
    eyebrowHeight: 0.1,
    mouthCurve: 0.2,
    eyeOpenness: 0.9,
    cheekRaise: 0.2
  }
}

export default function FacialAnimationEngine({
  audioData,
  text,
  emotion,
  isActive,
  onAnimationUpdate
}: FacialAnimationProps) {
  const [currentPhoneme, setCurrentPhoneme] = useState('silence')
  const [blinkTimer, setBlinkTimer] = useState(0)
  const [emotionIntensity, setEmotionIntensity] = useState(1)
  const animationFrameRef = useRef<number>()
  const audioContextRef = useRef<AudioContext>()
  const analyserRef = useRef<AnalyserNode>()

  // Simple phoneme detection from text
  const detectPhonemeFromText = (text: string, position: number): string => {
    if (!text || position >= text.length) return 'silence'
    
    const char = text[position].toLowerCase()
    
    // Simple mapping - in a real implementation, you'd use a proper phoneme library
    const phonemeMap: { [key: string]: string } = {
      'a': 'A', 'e': 'E', 'i': 'I', 'o': 'O', 'u': 'U',
      'm': 'M', 'b': 'B', 'p': 'P',
      'f': 'F', 'v': 'V',
      's': 'S', 'z': 'S',
      'r': 'R', 'l': 'L',
      ' ': 'silence'
    }
    
    return phonemeMap[char] || 'silence'
  }

  // Audio analysis for lip-sync
  const analyzeAudio = (audioData: Float32Array): number => {
    if (!audioData) return 0
    
    let sum = 0
    for (let i = 0; i < audioData.length; i++) {
      sum += Math.abs(audioData[i])
    }
    return sum / audioData.length
  }

  // Generate realistic blinking
  const generateBlink = (deltaTime: number): { left: number; right: number } => {
    setBlinkTimer(prev => prev + deltaTime)
    
    // Random blinking every 2-4 seconds
    const blinkInterval = 2000 + Math.random() * 2000
    
    if (blinkTimer > blinkInterval) {
      setBlinkTimer(0)
      // Quick blink animation
      const blinkPhase = (Date.now() % 200) / 200
      const blinkValue = Math.sin(blinkPhase * Math.PI)
      return { left: blinkValue, right: blinkValue }
    }
    
    return { left: 1, right: 1 }
  }

  // Main animation loop
  useEffect(() => {
    if (!isActive) return

    let textPosition = 0
    let lastTime = Date.now()
    
    const animate = () => {
      const currentTime = Date.now()
      const deltaTime = currentTime - lastTime
      lastTime = currentTime

      // Text-based lip-sync animation
      if (text && textPosition < text.length) {
        const phoneme = detectPhonemeFromText(text, Math.floor(textPosition))
        setCurrentPhoneme(phoneme)
        
        // Advance through text at speaking pace (adjust speed as needed)
        textPosition += deltaTime * 0.01 // Adjust this multiplier for speaking speed
      } else {
        setCurrentPhoneme('silence')
      }

      // Audio-based animation (if available)
      let audioIntensity = 0
      if (audioData) {
        audioIntensity = analyzeAudio(audioData)
      }

      // Generate facial animation data
      const mouthShape = PHONEME_MOUTH_SHAPES[currentPhoneme as keyof typeof PHONEME_MOUTH_SHAPES] || PHONEME_MOUTH_SHAPES.silence
      const expression = EMOTION_EXPRESSIONS[emotion]
      const blink = generateBlink(deltaTime)

      const animationData: FacialAnimationData = {
        mouthOpenness: mouthShape.openness + audioIntensity * 0.3,
        eyeBlinkLeft: blink.left,
        eyeBlinkRight: blink.right,
        eyebrowPosition: expression.eyebrowHeight,
        headRotation: {
          x: Math.sin(currentTime * 0.001) * 0.05, // Subtle head movement
          y: Math.sin(currentTime * 0.0008) * 0.03,
          z: Math.sin(currentTime * 0.0012) * 0.02
        },
        emotion,
        intensity: emotionIntensity
      }

      onAnimationUpdate(animationData)

      if (isActive) {
        animationFrameRef.current = requestAnimationFrame(animate)
      }
    }

    animate()

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isActive, text, emotion, audioData, onAnimationUpdate])

  // Setup audio analysis
  useEffect(() => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      analyserRef.current.fftSize = 256
    }

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  return null // This is a logic-only component
}

// Utility function to convert text to estimated phonemes
export function textToPhonemes(text: string): Array<{ phoneme: string; duration: number }> {
  const words = text.split(' ')
  const phonemes: Array<{ phoneme: string; duration: number }> = []
  
  words.forEach((word, wordIndex) => {
    for (let i = 0; i < word.length; i++) {
      const char = word[i].toLowerCase()
      let phoneme = 'silence'
      
      // Simple character to phoneme mapping
      if ('aeiou'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('mbp'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('fv'.includes(char)) {
        phoneme = char.toUpperCase()
      } else if ('sz'.includes(char)) {
        phoneme = 'S'
      } else if ('rl'.includes(char)) {
        phoneme = char.toUpperCase()
      }
      
      phonemes.push({
        phoneme,
        duration: 100 + Math.random() * 100 // Random duration between 100-200ms
      })
    }
    
    // Add pause between words
    if (wordIndex < words.length - 1) {
      phonemes.push({ phoneme: 'silence', duration: 200 })
    }
  })
  
  return phonemes
}

// Advanced emotion detection from text
export function detectEmotionFromText(text: string): 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic' {
  const lowerText = text.toLowerCase()
  
  // Happy indicators
  if (lowerText.includes('happy') || lowerText.includes('joy') || lowerText.includes('great') || 
      lowerText.includes('wonderful') || lowerText.includes('excited') || lowerText.includes('😊') ||
      lowerText.includes('good') || lowerText.includes('amazing')) {
    return 'happy'
  }
  
  // Sad indicators
  if (lowerText.includes('sad') || lowerText.includes('depressed') || lowerText.includes('cry') ||
      lowerText.includes('hurt') || lowerText.includes('pain') || lowerText.includes('😢') ||
      lowerText.includes('terrible') || lowerText.includes('awful')) {
    return 'sad'
  }
  
  // Concerned indicators
  if (lowerText.includes('worried') || lowerText.includes('anxious') || lowerText.includes('scared') ||
      lowerText.includes('afraid') || lowerText.includes('nervous') || lowerText.includes('stress') ||
      lowerText.includes('concerned') || lowerText.includes('help')) {
    return 'concerned'
  }
  
  // Empathetic indicators
  if (lowerText.includes('understand') || lowerText.includes('support') || lowerText.includes('care') ||
      lowerText.includes('here for you') || lowerText.includes('listen') || lowerText.includes('comfort')) {
    return 'empathetic'
  }
  
  return 'neutral'
}
