'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, MicrophoneIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline'
import { generateId } from '@/lib/utils'
import VoiceChat from '@/components/chatbot/VoiceChat'
import HumanAvatar from '@/components/avatar/HumanAvatar'
import FacialAnimationEngine, { FacialAnimationData, detectEmotionFromText } from '@/components/avatar/FacialAnimationEngine'
import AudioAnalyzer, { generateLipSyncFromAudio } from '@/components/avatar/AudioAnalyzer'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  isTyping?: boolean
  typedContent?: string
  safetyAlert?: {
    riskLevel: string
    keywords: string[]
  }
  language?: 'en' | 'ta'
  emotion?: 'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'
}

export default function AvatarChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [language, setLanguage] = useState<'en' | 'ta'>('en')
  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)
  
  // Avatar states
  const [avatarEmotion, setAvatarEmotion] = useState<'neutral' | 'happy' | 'sad' | 'concerned' | 'empathetic'>('neutral')
  const [avatarIsSpeaking, setAvatarIsSpeaking] = useState(false)
  const [avatarIsListening, setAvatarIsListening] = useState(false)
  const [currentSpeechText, setCurrentSpeechText] = useState('')
  const [facialAnimationData, setFacialAnimationData] = useState<FacialAnimationData | null>(null)
  const [realTimeLipSync, setRealTimeLipSync] = useState({
    mouthOpenness: 0,
    mouthWidth: 0,
    phoneme: 'silence'
  })
  const [realTimeFacialData, setRealTimeFacialData] = useState({
    eyeBlinkLeft: 1,
    eyeBlinkRight: 1,
    eyebrowLeft: 0,
    eyebrowRight: 0,
    cheekPuff: 0,
    jawOpen: 0
  })
  const [audioAnalysisActive, setAudioAnalysisActive] = useState(false)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()
  const speechTimeoutRef = useRef<NodeJS.Timeout>()

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage = {
      id: generateId(),
      role: 'assistant' as const,
      content: language === 'ta' 
        ? 'வணக்கம்! நான் உங்கள் AI மனநல ஆலோசகர். நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்?'
        : 'Hello! I\'m your AI mental health counselor. I\'m here to provide emotional support and guidance. How are you feeling today?',
      timestamp: new Date().toISOString(),
      language,
      emotion: 'empathetic' as const
    }
    
    setMessages([welcomeMessage])
    setAvatarEmotion('empathetic')
    
    // Auto-speak welcome message
    setTimeout(() => {
      if (speakResponse) {
        speakResponse(welcomeMessage.content, language)
        setAvatarIsSpeaking(true)
        setCurrentSpeechText(welcomeMessage.content)
        
        // Stop speaking after estimated duration
        const estimatedDuration = welcomeMessage.content.length * 80 // ~80ms per character
        speechTimeoutRef.current = setTimeout(() => {
          setAvatarIsSpeaking(false)
          setCurrentSpeechText('')
        }, estimatedDuration)
      }
    }, 1000)
  }, [language, speakResponse])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Handle facial animation updates
  const handleFacialAnimationUpdate = useCallback((animationData: FacialAnimationData) => {
    setFacialAnimationData(animationData)
  }, [])

  // Handle real-time audio analysis
  const handleAudioData = useCallback((audioData: {
    volume: number
    frequency: number
    formants: number[]
    pitch: number
    voiceActivity: boolean
  }) => {
    // Generate lip-sync data from audio
    const lipSyncData = generateLipSyncFromAudio(audioData)
    setRealTimeLipSync(lipSyncData)

    // Update facial expressions based on voice activity
    if (audioData.voiceActivity && audioData.volume > 0.02) {
      setRealTimeFacialData(prev => ({
        ...prev,
        jawOpen: Math.min(0.8, audioData.volume * 1.5),
        cheekPuff: audioData.frequency > 1000 ? 0.2 : 0
      }))
    } else {
      setRealTimeFacialData(prev => ({
        ...prev,
        jawOpen: 0,
        cheekPuff: 0
      }))
    }
  }, [])

  // Typing effect for assistant messages
  const typeMessage = useCallback((message: Message) => {
    const words = message.content.split(' ')
    let currentIndex = 0
    
    const typeNextWord = () => {
      if (currentIndex < words.length) {
        const typedContent = words.slice(0, currentIndex + 1).join(' ')
        
        setMessages(prev => prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, typedContent, isTyping: true }
            : msg
        ))
        
        currentIndex++
        typingTimeoutRef.current = setTimeout(typeNextWord, 150 + Math.random() * 100)
      } else {
        setMessages(prev => prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, isTyping: false, typedContent: message.content }
            : msg
        ))
        
        // Start avatar speaking
        if (message.role === 'assistant') {
          setAvatarIsSpeaking(true)
          setCurrentSpeechText(message.content)
          setAvatarEmotion(message.emotion || 'neutral')
          
          // Auto-speak the response
          if (speakResponse) {
            setTimeout(() => {
              speakResponse(message.content, message.language || language)
            }, 500)
          }
          
          // Stop speaking after estimated duration
          const estimatedDuration = message.content.length * 80
          speechTimeoutRef.current = setTimeout(() => {
            setAvatarIsSpeaking(false)
            setCurrentSpeechText('')
            setAvatarEmotion('neutral')
          }, estimatedDuration)
        }
      }
    }
    
    typeNextWord()
  }, [speakResponse, language])

  // Handle voice transcript
  const handleVoiceTranscript = useCallback((transcript: string) => {
    setInputMessage(transcript)
    setAvatarIsListening(false)
    // Auto-send voice messages
    setTimeout(() => {
      sendMessage(transcript)
    }, 500)
  }, [])

  // Handle voice response setup
  const handleSpeakResponse = useCallback((speakFn: any) => {
    setSpeakResponse(() => speakFn)
  }, [])

  // Update avatar listening state and audio analysis
  useEffect(() => {
    setAvatarIsListening(isListening)
    setAudioAnalysisActive(isListening) // Activate audio analysis when listening
  }, [isListening])

  const sendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputMessage.trim()
    if (!textToSend || isLoading) return

    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: textToSend,
      timestamp: new Date().toISOString(),
      language
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    // Show typing indicator
    const typingMessage: Message = {
      id: generateId(),
      role: 'assistant',
      content: language === 'ta' ? 'சிந்திக்கிறேன்...' : 'Thinking...',
      timestamp: new Date().toISOString(),
      isTyping: true,
      language,
      emotion: 'neutral'
    }
    
    setMessages(prev => [...prev, typingMessage])
    setAvatarEmotion('concerned') // Show thinking expression

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: textToSend,
          sessionId: generateId(),
          userId: 'anonymous',
          language
        }),
      })

      const data = await response.json()

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))

      if (data.success) {
        let responseContent = data.data.content
        
        // Detect emotion from response
        const detectedEmotion = detectEmotionFromText(responseContent)
        
        // Basic Tamil translation for common responses
        if (language === 'ta' && !responseContent.includes('தமிழ்')) {
          const translations: { [key: string]: string } = {
            'I understand': 'நான் புரிந்துகொள்கிறேன்',
            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',
            'I\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',
            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',
            'That sounds difficult': 'அது கடினமாக இருக்கும்',
            'You\'re not alone': 'நீங்கள் தனியாக இல்லை',
            'I\'m sorry to hear': 'கேட்டு வருந்துகிறேன்',
            'That\'s wonderful': 'அது அருமை',
            'How can I help': 'நான் எப்படி உதவ முடியும்'
          }
          
          Object.entries(translations).forEach(([en, ta]) => {
            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)
          })
        }

        const assistantMessage: Message = {
          id: generateId(),
          role: 'assistant',
          content: responseContent,
          timestamp: data.data.timestamp,
          safetyAlert: data.data.safetyAlert,
          language,
          emotion: detectedEmotion,
          isTyping: true,
          typedContent: ''
        }
        
        setMessages(prev => [...prev, assistantMessage])
        
        // Start typing effect
        setTimeout(() => {
          typeMessage(assistantMessage)
        }, 500)
        
      } else {
        throw new Error(data.error || 'Failed to get response')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))
      
      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: language === 'ta' 
          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும்.'
          : 'I apologize, but I\'m having trouble responding right now. Please try again.',
        timestamp: new Date().toISOString(),
        language,
        emotion: 'concerned'
      }
      setMessages(prev => [...prev, errorMessage])
      typeMessage(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-4 h-[calc(100vh-100px)]">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">

        {/* Avatar Section */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4">
            <h2 className="text-xl font-semibold">
              {language === 'ta' ? 'AI ஆலோசகர்' : 'AI Counselor'}
            </h2>
            <p className="text-purple-100 text-sm">
              {language === 'ta'
                ? 'உங்கள் மனநல ஆதரவு நண்பர்'
                : 'Your Mental Health Support Companion'
              }
            </p>
          </div>

          <HumanAvatar
            isListening={avatarIsListening}
            isSpeaking={avatarIsSpeaking}
            emotion={avatarEmotion}
            message={avatarIsSpeaking ? currentSpeechText : undefined}
            lipSyncData={avatarIsListening ? realTimeLipSync : undefined}
            facialData={avatarIsListening ? realTimeFacialData : undefined}
          />

          {/* Real-time Audio Analyzer */}
          <AudioAnalyzer
            isActive={audioAnalysisActive}
            onAudioData={handleAudioData}
          />

          {/* Facial Animation Engine */}
          <FacialAnimationEngine
            text={avatarIsSpeaking ? currentSpeechText : undefined}
            emotion={avatarEmotion}
            isActive={avatarIsSpeaking || avatarIsListening}
            onAnimationUpdate={handleFacialAnimationUpdate}
          />
        </div>

        {/* Chat Section */}
        <div className="bg-white rounded-lg shadow-lg flex flex-col">
          {/* Chat Header */}
          <div className="bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl font-semibold">
                  {language === 'ta' ? 'உரையாடல்' : 'Conversation'}
                </h1>
                <p className="text-pink-100 text-sm">
                  {language === 'ta'
                    ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு'
                    : 'Safe, confidential emotional support'
                  }
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm">
                  {language === 'ta' ? 'ஆன்லைன்' : 'Online'}
                </span>
              </div>
            </div>
          </div>

          {/* Voice Chat Component */}
          <VoiceChat
            onTranscript={handleVoiceTranscript}
            onSpeakResponse={handleSpeakResponse}
            isListening={isListening}
            setIsListening={setIsListening}
            language={language}
            setLanguage={setLanguage}
          />

          {/* Emergency Notice */}
          <div className="bg-red-50 border-l-4 border-red-400 p-3">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  <strong>{language === 'ta' ? 'அவசரநிலை:' : 'Emergency:'}</strong>{' '}
                  {language === 'ta'
                    ? 'உடனடி ஆபத்தில் இருந்தால், 911 அழைக்கவும். நெருக்கடி ஆதரவுக்கு 988 அழைக்கவும்.'
                    : 'If in immediate danger, call 911. For crisis support, call 988.'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Simple Chat Display for now */}
          <div className="flex-1 p-4 bg-gray-50">
            <div className="text-center text-gray-600">
              <p className="text-lg font-semibold mb-2">
                {language === 'ta' ? '3D அவதார் சாட்' : '3D Avatar Chat'}
              </p>
              <p className="text-sm">
                {language === 'ta'
                  ? 'இடதுபுறத்தில் உள்ள 3D அவதாருடன் பேசுங்கள்!'
                  : 'Interact with the 3D avatar on the left!'
                }
              </p>
              <div className="mt-4 space-y-2">
                <button
                  onClick={() => sendMessage(language === 'ta' ? 'வணக்கம்!' : 'Hello!')}
                  className="block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
                >
                  {language === 'ta' ? '👋 வணக்கம் சொல்லுங்கள்' : '👋 Say Hello'}
                </button>
                <button
                  onClick={() => sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious')}
                  className="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  {language === 'ta' ? '😰 கவலையை பகிர்ந்து கொள்ளுங்கள்' : '😰 Share Anxiety'}
                </button>
                <button
                  onClick={() => sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help')}
                  className="block w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                >
                  {language === 'ta' ? '🆘 உதவி கேளுங்கள்' : '🆘 Ask for Help'}
                </button>
              </div>

              {/* Advanced Features Status */}
              <div className="mt-6 p-3 bg-gray-100 rounded-lg">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">
                  {language === 'ta' ? '🚀 மேம்பட்ட அம்சங்கள்' : '🚀 Advanced Features'}
                </h4>
                <div className="space-y-1 text-xs text-gray-600">
                  <div className="flex items-center justify-between">
                    <span>{language === 'ta' ? '🎭 முக வெளிப்பாடுகள்' : '🎭 Facial Expressions'}</span>
                    <span className="text-green-600">✓ Active</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>{language === 'ta' ? '👄 உதடு ஒத்திசைவு' : '👄 Lip Sync'}</span>
                    <span className={avatarIsListening ? 'text-green-600' : 'text-gray-400'}>
                      {avatarIsListening ? '✓ Real-time' : '○ Standby'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>{language === 'ta' ? '🎤 ஆடியோ பகுப்பாய்வு' : '🎤 Audio Analysis'}</span>
                    <span className={audioAnalysisActive ? 'text-green-600' : 'text-gray-400'}>
                      {audioAnalysisActive ? '✓ Analyzing' : '○ Inactive'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>{language === 'ta' ? '🧠 உணர்ச்சி கண்டறிதல்' : '🧠 Emotion Detection'}</span>
                    <span className="text-green-600">✓ Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
