{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function formatTime(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function sanitizeInput(input: string): string {\n  return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n    .replace(/[<>]/g, '')\n    .trim()\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MAAM,OAAO,CAAC,uDAAuD,IACzE,OAAO,CAAC,SAAS,IACjB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/components/chatbot/VoiceChat.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { MicrophoneIcon, SpeakerWaveIcon, StopIcon, LanguageIcon } from '@heroicons/react/24/outline'\n\ninterface VoiceChatProps {\n  onTranscript: (text: string) => void\n  onSpeakResponse: (text: string, language: 'en' | 'ta') => void\n  isListening: boolean\n  setIsListening: (listening: boolean) => void\n  language: 'en' | 'ta'\n  setLanguage: (lang: 'en' | 'ta') => void\n}\n\nexport default function VoiceChat({\n  onTranscript,\n  onSpeakResponse,\n  isListening,\n  setIsListening,\n  language,\n  setLanguage\n}: VoiceChatProps) {\n  const [isSupported, setIsSupported] = useState(false)\n  const [isSpeaking, setIsSpeaking] = useState(false)\n  const [transcript, setTranscript] = useState('')\n  \n  const recognitionRef = useRef<any>(null)\n  const synthRef = useRef<SpeechSynthesis | null>(null)\n\n  useEffect(() => {\n    // Check for speech recognition support\n    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition\n    const speechSynthesis = window.speechSynthesis\n\n    if (SpeechRecognition && speechSynthesis) {\n      setIsSupported(true)\n      synthRef.current = speechSynthesis\n\n      // Initialize speech recognition\n      const recognition = new SpeechRecognition()\n      recognition.continuous = true\n      recognition.interimResults = true\n      recognition.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n\n      recognition.onstart = () => {\n        console.log('Voice recognition started')\n      }\n\n      recognition.onresult = (event: any) => {\n        let finalTranscript = ''\n        let interimTranscript = ''\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const transcript = event.results[i][0].transcript\n          if (event.results[i].isFinal) {\n            finalTranscript += transcript\n          } else {\n            interimTranscript += transcript\n          }\n        }\n\n        setTranscript(interimTranscript)\n\n        if (finalTranscript) {\n          onTranscript(finalTranscript)\n          setTranscript('')\n          setIsListening(false)\n        }\n      }\n\n      recognition.onerror = (event: any) => {\n        console.error('Speech recognition error:', event.error)\n        setIsListening(false)\n      }\n\n      recognition.onend = () => {\n        setIsListening(false)\n      }\n\n      recognitionRef.current = recognition\n    }\n\n    return () => {\n      if (recognitionRef.current) {\n        recognitionRef.current.stop()\n      }\n    }\n  }, [language, onTranscript, setIsListening])\n\n  const startListening = () => {\n    if (recognitionRef.current && !isListening) {\n      recognitionRef.current.lang = language === 'ta' ? 'ta-IN' : 'en-US'\n      recognitionRef.current.start()\n      setIsListening(true)\n    }\n  }\n\n  const stopListening = () => {\n    if (recognitionRef.current && isListening) {\n      recognitionRef.current.stop()\n      setIsListening(false)\n    }\n  }\n\n  const speakText = (text: string, lang: 'en' | 'ta' = language) => {\n    if (synthRef.current && text) {\n      // Stop any current speech\n      synthRef.current.cancel()\n\n      const utterance = new SpeechSynthesisUtterance(text)\n      \n      // Set language and voice\n      utterance.lang = lang === 'ta' ? 'ta-IN' : 'en-US'\n      utterance.rate = 0.9\n      utterance.pitch = 1\n      utterance.volume = 1\n\n      // Try to find a suitable voice\n      const voices = synthRef.current.getVoices()\n      const preferredVoice = voices.find(voice => \n        lang === 'ta' \n          ? voice.lang.includes('ta') || voice.lang.includes('Tamil')\n          : voice.lang.includes('en') && voice.lang.includes('US')\n      )\n      \n      if (preferredVoice) {\n        utterance.voice = preferredVoice\n      }\n\n      utterance.onstart = () => setIsSpeaking(true)\n      utterance.onend = () => setIsSpeaking(false)\n      utterance.onerror = () => setIsSpeaking(false)\n\n      synthRef.current.speak(utterance)\n    }\n  }\n\n  const stopSpeaking = () => {\n    if (synthRef.current) {\n      synthRef.current.cancel()\n      setIsSpeaking(false)\n    }\n  }\n\n  // Expose speakText function to parent\n  useEffect(() => {\n    onSpeakResponse(speakText as any, language)\n  }, [language, onSpeakResponse])\n\n  if (!isSupported) {\n    return (\n      <div className=\"text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n        <p className=\"text-yellow-800\">\n          Voice features are not supported in your browser. Please use Chrome or Edge for voice interaction.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"flex items-center space-x-4 p-4 bg-white border border-gray-200 rounded-lg\">\n      {/* Language Toggle */}\n      <div className=\"flex items-center space-x-2\">\n        <LanguageIcon className=\"h-5 w-5 text-gray-500\" />\n        <select\n          value={language}\n          onChange={(e) => setLanguage(e.target.value as 'en' | 'ta')}\n          className=\"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-pink-500\"\n        >\n          <option value=\"en\">English</option>\n          <option value=\"ta\">தமிழ் (Tamil)</option>\n        </select>\n      </div>\n\n      {/* Voice Input */}\n      <div className=\"flex items-center space-x-2\">\n        {!isListening ? (\n          <button\n            onClick={startListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors\"\n          >\n            <MicrophoneIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'பேசுங்கள்' : 'Speak'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopListening}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors animate-pulse\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'நிறுத்து' : 'Stop'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Speech Output Control */}\n      <div className=\"flex items-center space-x-2\">\n        {!isSpeaking ? (\n          <button\n            onClick={() => speakText(language === 'ta' \n              ? 'வணக்கம்! நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்.' \n              : 'Hello! I am here to help you.'\n            )}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <SpeakerWaveIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'சோதனை' : 'Test Voice'}\n            </span>\n          </button>\n        ) : (\n          <button\n            onClick={stopSpeaking}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n          >\n            <StopIcon className=\"h-5 w-5\" />\n            <span className=\"text-sm\">\n              {language === 'ta' ? 'அமைதி' : 'Stop Voice'}\n            </span>\n          </button>\n        )}\n      </div>\n\n      {/* Live Transcript */}\n      {transcript && (\n        <div className=\"flex-1 text-sm text-gray-600 italic\">\n          {language === 'ta' ? 'கேட்கிறது: ' : 'Listening: '}{transcript}\n        </div>\n      )}\n\n      {/* Status Indicators */}\n      <div className=\"flex space-x-2\">\n        {isListening && (\n          <div className=\"flex items-center space-x-1 text-red-600\">\n            <div className=\"w-2 h-2 bg-red-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'கேட்கிறது' : 'Listening'}\n            </span>\n          </div>\n        )}\n        {isSpeaking && (\n          <div className=\"flex items-center space-x-1 text-blue-600\">\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></div>\n            <span className=\"text-xs\">\n              {language === 'ta' ? 'பேசுகிறது' : 'Speaking'}\n            </span>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAce,SAAS,UAAU,EAChC,YAAY,EACZ,eAAe,EACf,WAAW,EACX,cAAc,EACd,QAAQ,EACR,WAAW,EACI;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACnC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuC;QACvC,MAAM,oBAAoB,AAAC,OAAe,iBAAiB,IAAI,AAAC,OAAe,uBAAuB;QACtG,MAAM,kBAAkB,OAAO,eAAe;QAE9C,IAAI,qBAAqB,iBAAiB;YACxC,eAAe;YACf,SAAS,OAAO,GAAG;YAEnB,gCAAgC;YAChC,MAAM,cAAc,IAAI;YACxB,YAAY,UAAU,GAAG;YACzB,YAAY,cAAc,GAAG;YAC7B,YAAY,IAAI,GAAG,aAAa,OAAO,UAAU;YAEjD,YAAY,OAAO,GAAG;gBACpB,QAAQ,GAAG,CAAC;YACd;YAEA,YAAY,QAAQ,GAAG,CAAC;gBACtB,IAAI,kBAAkB;gBACtB,IAAI,oBAAoB;gBAExB,IAAK,IAAI,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE,IAAK;oBAC7D,MAAM,aAAa,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,UAAU;oBACjD,IAAI,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;wBAC5B,mBAAmB;oBACrB,OAAO;wBACL,qBAAqB;oBACvB;gBACF;gBAEA,cAAc;gBAEd,IAAI,iBAAiB;oBACnB,aAAa;oBACb,cAAc;oBACd,eAAe;gBACjB;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,KAAK,CAAC,6BAA6B,MAAM,KAAK;gBACtD,eAAe;YACjB;YAEA,YAAY,KAAK,GAAG;gBAClB,eAAe;YACjB;YAEA,eAAe,OAAO,GAAG;QAC3B;QAEA,OAAO;YACL,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,IAAI;YAC7B;QACF;IACF,GAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,iBAAiB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,aAAa;YAC1C,eAAe,OAAO,CAAC,IAAI,GAAG,aAAa,OAAO,UAAU;YAC5D,eAAe,OAAO,CAAC,KAAK;YAC5B,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,eAAe,OAAO,IAAI,aAAa;YACzC,eAAe,OAAO,CAAC,IAAI;YAC3B,eAAe;QACjB;IACF;IAEA,MAAM,YAAY,CAAC,MAAc,OAAoB,QAAQ;QAC3D,IAAI,SAAS,OAAO,IAAI,MAAM;YAC5B,0BAA0B;YAC1B,SAAS,OAAO,CAAC,MAAM;YAEvB,MAAM,YAAY,IAAI,yBAAyB;YAE/C,yBAAyB;YACzB,UAAU,IAAI,GAAG,SAAS,OAAO,UAAU;YAC3C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,+BAA+B;YAC/B,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,MAAM,iBAAiB,OAAO,IAAI,CAAC,CAAA,QACjC,SAAS,OACL,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,WACjD,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC;YAGvD,IAAI,gBAAgB;gBAClB,UAAU,KAAK,GAAG;YACpB;YAEA,UAAU,OAAO,GAAG,IAAM,cAAc;YACxC,UAAU,KAAK,GAAG,IAAM,cAAc;YACtC,UAAU,OAAO,GAAG,IAAM,cAAc;YAExC,SAAS,OAAO,CAAC,KAAK,CAAC;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,MAAM;YACvB,cAAc;QAChB;IACF;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,WAAkB;IACpC,GAAG;QAAC;QAAU;KAAgB;IAE9B,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAkB;;;;;;;;;;;IAKrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;0CAAK;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,2NAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;sCAC1B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,cAAc;;;;;;;;;;;yCAIvC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,aAAa;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,8OAAC;oBACC,SAAS,IAAM,UAAU,aAAa,OAClC,oDACA;oBAEJ,WAAU;;sCAEV,8OAAC,6NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;yCAInC,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb,aAAa,OAAO,UAAU;;;;;;;;;;;;;;;;;YAOtC,4BACC,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,OAAO,gBAAgB;oBAAe;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;oBAIxC,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CACb,aAAa,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/mini/mental-wellness-platform/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { PaperAirplaneIcon, ExclamationTriangleIcon, UserIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline'\nimport { generateId } from '@/lib/utils'\nimport VoiceChat from '@/components/chatbot/VoiceChat'\n\ninterface Message {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n  isTyping?: boolean\n  typedContent?: string\n  safetyAlert?: {\n    riskLevel: string\n    keywords: string[]\n  }\n  language?: 'en' | 'ta'\n}\n\nexport default function ChatPage() {\n  const [messages, setMessages] = useState<Message[]>([])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [isListening, setIsListening] = useState(false)\n  const [language, setLanguage] = useState<'en' | 'ta'>('en')\n  const [speakResponse, setSpeakResponse] = useState<((text: string, lang: 'en' | 'ta') => void) | null>(null)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const typingTimeoutRef = useRef<NodeJS.Timeout>()\n\n  // Initialize with welcome message\n  useEffect(() => {\n    const welcomeMessage = {\n      id: generateId(),\n      role: 'assistant' as const,\n      content: language === 'ta'\n        ? 'வணக்கம்! நான் உங்களுக்கு உணர்ச்சி ஆதரவு மற்றும் வழிகாட்டுதல் வழங்க இங்கே இருக்கிறேன். இன்று நீங்கள் எப்படி உணர்கிறீர்கள்? இது ஒரு பாதுகாப்பான இடம், இங்கே நீங்கள் உங்கள் மனதில் உள்ளதை பகிர்ந்து கொள்ளலாம்.'\n        : 'Hello! I\\'m here to provide emotional support and guidance. How are you feeling today? Remember, this is a safe space where you can share what\\'s on your mind.',\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages([welcomeMessage])\n\n    // Auto-speak welcome message\n    setTimeout(() => {\n      if (speakResponse) {\n        speakResponse(welcomeMessage.content, language)\n      }\n    }, 1000)\n  }, [language, speakResponse])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  // Typing effect for assistant messages\n  const typeMessage = useCallback((message: Message) => {\n    const words = message.content.split(' ')\n    let currentIndex = 0\n\n    const typeNextWord = () => {\n      if (currentIndex < words.length) {\n        const typedContent = words.slice(0, currentIndex + 1).join(' ')\n\n        setMessages(prev => prev.map(msg =>\n          msg.id === message.id\n            ? { ...msg, typedContent, isTyping: true }\n            : msg\n        ))\n\n        currentIndex++\n        typingTimeoutRef.current = setTimeout(typeNextWord, 100 + Math.random() * 100)\n      } else {\n        setMessages(prev => prev.map(msg =>\n          msg.id === message.id\n            ? { ...msg, isTyping: false, typedContent: message.content }\n            : msg\n        ))\n\n        // Auto-speak the response\n        if (speakResponse && message.role === 'assistant') {\n          setTimeout(() => {\n            speakResponse(message.content, message.language || language)\n          }, 500)\n        }\n      }\n    }\n\n    typeNextWord()\n  }, [speakResponse, language])\n\n  // Handle voice transcript\n  const handleVoiceTranscript = useCallback((transcript: string) => {\n    setInputMessage(transcript)\n    // Auto-send voice messages\n    setTimeout(() => {\n      sendMessage(transcript)\n    }, 500)\n  }, [])\n\n  // Handle voice response setup\n  const handleSpeakResponse = useCallback((speakFn: any) => {\n    setSpeakResponse(() => speakFn)\n  }, [])\n\n  const sendMessage = async (messageText?: string) => {\n    const textToSend = messageText || inputMessage.trim()\n    if (!textToSend || isLoading) return\n\n    const userMessage: Message = {\n      id: generateId(),\n      role: 'user',\n      content: textToSend,\n      timestamp: new Date().toISOString(),\n      language\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsLoading(true)\n\n    // Show typing indicator\n    const typingMessage: Message = {\n      id: generateId(),\n      role: 'assistant',\n      content: language === 'ta' ? 'தட்டச்சு செய்கிறது...' : 'Typing...',\n      timestamp: new Date().toISOString(),\n      isTyping: true,\n      language\n    }\n\n    setMessages(prev => [...prev, typingMessage])\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: textToSend,\n          sessionId: generateId(),\n          userId: 'anonymous',\n          language\n        }),\n      })\n\n      const data = await response.json()\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      if (data.success) {\n        let responseContent = data.data.content\n\n        // Translate response if needed (basic translation for demo)\n        if (language === 'ta' && !responseContent.includes('தமிழ்')) {\n          // Add Tamil translation for common responses\n          const translations: { [key: string]: string } = {\n            'I understand': 'நான் புரிந்துகொள்கிறேன்',\n            'How are you feeling': 'நீங்கள் எப்படி உணர்கிறீர்கள்',\n            'I\\'m here to help': 'நான் உங்களுக்கு உதவ இங்கே இருக்கிறேன்',\n            'Thank you for sharing': 'பகிர்ந்ததற்கு நன்றி',\n            'That sounds difficult': 'அது கடினமாக இருக்கும்',\n            'You\\'re not alone': 'நீங்கள் தனியாக இல்லை'\n          }\n\n          Object.entries(translations).forEach(([en, ta]) => {\n            responseContent = responseContent.replace(new RegExp(en, 'gi'), ta)\n          })\n        }\n\n        const assistantMessage: Message = {\n          id: generateId(),\n          role: 'assistant',\n          content: responseContent,\n          timestamp: data.data.timestamp,\n          safetyAlert: data.data.safetyAlert,\n          language,\n          isTyping: true,\n          typedContent: ''\n        }\n\n        setMessages(prev => [...prev, assistantMessage])\n\n        // Start typing effect\n        setTimeout(() => {\n          typeMessage(assistantMessage)\n        }, 500)\n\n      } else {\n        throw new Error(data.error || 'Failed to get response')\n      }\n    } catch (error) {\n      console.error('Error sending message:', error)\n\n      // Remove typing indicator\n      setMessages(prev => prev.filter(msg => msg.id !== typingMessage.id))\n\n      const errorMessage: Message = {\n        id: generateId(),\n        role: 'assistant',\n        content: language === 'ta'\n          ? 'மன்னிக்கவும், இப்போது பதிலளிப்பதில் சிக்கல் உள்ளது. மீண்டும் முயற்சிக்கவும், அல்லது இது அவசரநிலை என்றால், 911 அல்லது நெருக்கடி ஹாட்லைனை தொடர்பு கொள்ளவும்.'\n          : 'I apologize, but I\\'m having trouble responding right now. Please try again, or if this is an emergency, please call 911 or contact a crisis hotline.',\n        timestamp: new Date().toISOString(),\n        language\n      }\n      setMessages(prev => [...prev, errorMessage])\n      typeMessage(errorMessage)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  return (\n    <div className=\"max-w-5xl mx-auto p-4 h-[calc(100vh-200px)]\">\n      <div className=\"bg-white rounded-lg shadow-lg h-full flex flex-col\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-pink-600 to-purple-600 text-white p-4 rounded-t-lg\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-xl font-semibold flex items-center\">\n                <ChatBubbleLeftRightIcon className=\"h-6 w-6 mr-2\" />\n                {language === 'ta' ? 'AI மனநல ஆதரவு' : 'AI Mental Health Support'}\n              </h1>\n              <p className=\"text-pink-100 text-sm\">\n                {language === 'ta'\n                  ? 'பாதுகாப்பான, ரகசிய உணர்ச்சி ஆதரவு • 24/7 கிடைக்கும்'\n                  : 'Safe, confidential emotional support • Available 24/7'\n                }\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm\">\n                {language === 'ta' ? 'ஆன்லைன்' : 'Online'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Voice Chat Component */}\n        <VoiceChat\n          onTranscript={handleVoiceTranscript}\n          onSpeakResponse={handleSpeakResponse}\n          isListening={isListening}\n          setIsListening={setIsListening}\n          language={language}\n          setLanguage={setLanguage}\n        />\n\n        {/* Emergency Notice */}\n        <div className=\"bg-red-50 border-l-4 border-red-400 p-4\">\n          <div className=\"flex\">\n            <ExclamationTriangleIcon className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-700\">\n                <strong>Emergency:</strong> If you're in immediate danger, call 911. \n                For crisis support, call 988 or text HOME to 741741.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} items-end space-x-2`}\n            >\n              {/* Avatar */}\n              {message.role === 'assistant' && (\n                <div className=\"w-8 h-8 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center text-white text-sm font-bold mb-1\">\n                  AI\n                </div>\n              )}\n\n              <div\n                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${\n                  message.role === 'user'\n                    ? 'bg-gradient-to-r from-pink-500 to-purple-500 text-white rounded-br-md'\n                    : 'bg-white text-gray-900 rounded-bl-md border border-gray-200'\n                }`}\n              >\n                {message.safetyAlert && (\n                  <div className={`mb-2 p-2 rounded-lg text-xs ${\n                    message.safetyAlert.riskLevel === 'critical'\n                      ? 'bg-red-100 text-red-800 border border-red-200'\n                      : message.safetyAlert.riskLevel === 'high'\n                      ? 'bg-orange-100 text-orange-800 border border-orange-200'\n                      : 'bg-yellow-100 text-yellow-800 border border-yellow-200'\n                  }`}>\n                    ⚠️ {language === 'ta' ? 'பாதுகாப்பு எச்சரிக்கை' : 'Safety alert detected'} - {message.safetyAlert.riskLevel} risk\n                  </div>\n                )}\n\n                <div className=\"whitespace-pre-wrap\">\n                  {message.isTyping ? (\n                    <div>\n                      <span>{message.typedContent || ''}</span>\n                      {message.typedContent !== message.content && (\n                        <span className=\"inline-block w-2 h-5 bg-gray-400 ml-1 animate-pulse\"></span>\n                      )}\n                    </div>\n                  ) : (\n                    message.content\n                  )}\n                </div>\n\n                <div className={`flex items-center justify-between mt-2 text-xs ${\n                  message.role === 'user' ? 'text-pink-200' : 'text-gray-500'\n                }`}>\n                  <span>{new Date(message.timestamp).toLocaleTimeString()}</span>\n                  {message.language && (\n                    <span className=\"ml-2 px-1 py-0.5 bg-gray-200 text-gray-600 rounded text-xs\">\n                      {message.language === 'ta' ? 'தமிழ்' : 'EN'}\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* User Avatar */}\n              {message.role === 'user' && (\n                <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mb-1\">\n                  <UserIcon className=\"h-5 w-5\" />\n                </div>\n              )}\n            </div>\n          ))}\n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg\">\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                </div>\n              </div>\n            </div>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Input */}\n        <div className=\"border-t bg-white p-4\">\n          <div className=\"flex space-x-3\">\n            <div className=\"flex-1\">\n              <textarea\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder={language === 'ta'\n                  ? 'உங்கள் மனதில் உள்ளதை பகிர்ந்து கொள்ளுங்கள்... (அனுப்ப Enter அழுத்தவும்)'\n                  : 'Share what\\'s on your mind... (Press Enter to send)'\n                }\n                className=\"w-full border border-gray-300 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none text-sm\"\n                rows={2}\n                disabled={isLoading || isListening}\n              />\n              {isListening && (\n                <div className=\"mt-2 text-sm text-red-600 flex items-center\">\n                  <div className=\"w-2 h-2 bg-red-600 rounded-full animate-pulse mr-2\"></div>\n                  {language === 'ta' ? 'குரல் கேட்கப்படுகிறது...' : 'Listening for voice input...'}\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => sendMessage()}\n              disabled={!inputMessage.trim() || isLoading || isListening}\n              className=\"bg-gradient-to-r from-pink-500 to-purple-500 text-white px-6 py-3 rounded-xl hover:from-pink-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md\"\n            >\n              {isLoading ? (\n                <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n              ) : (\n                <PaperAirplaneIcon className=\"h-5 w-5\" />\n              )}\n            </button>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"mt-3 flex flex-wrap gap-2\">\n            <button\n              onClick={() => sendMessage(language === 'ta' ? 'நான் கவலையாக உணர்கிறேன்' : 'I feel anxious')}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors\"\n              disabled={isLoading}\n            >\n              {language === 'ta' ? '😰 கவலை' : '😰 Anxious'}\n            </button>\n            <button\n              onClick={() => sendMessage(language === 'ta' ? 'நான் சோகமாக உணர்கிறேன்' : 'I feel sad')}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors\"\n              disabled={isLoading}\n            >\n              {language === 'ta' ? '😢 சோகம்' : '😢 Sad'}\n            </button>\n            <button\n              onClick={() => sendMessage(language === 'ta' ? 'நான் மன அழுத்தத்தில் இருக்கிறேன்' : 'I feel stressed')}\n              className=\"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors\"\n              disabled={isLoading}\n            >\n              {language === 'ta' ? '😤 மன அழுத்தம்' : '😤 Stressed'}\n            </button>\n            <button\n              onClick={() => sendMessage(language === 'ta' ? 'எனக்கு உதவி தேவை' : 'I need help')}\n              className=\"px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm hover:bg-red-200 transition-colors\"\n              disabled={isLoading}\n            >\n              {language === 'ta' ? '🆘 உதவி' : '🆘 Help'}\n            </button>\n          </div>\n\n          <p className=\"text-xs text-gray-500 mt-3 text-center\">\n            {language === 'ta'\n              ? 'இந்த AI ஆதரவு வழங்குகிறது ஆனால் தொழில்முறை சிகிச்சை அல்லது மருத்துவ பராமரிப்புக்கு மாற்றாக இல்லை.'\n              : 'This AI provides support but is not a substitute for professional therapy or medical care.'\n            }\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IACvG,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAE9B,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAClB,gNACA;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY;YAAC;SAAe;QAE5B,6BAA6B;QAC7B,WAAW;YACT,IAAI,eAAe;gBACjB,cAAc,eAAe,OAAO,EAAE;YACxC;QACF,GAAG;IACL,GAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,CAAC;QACpC,IAAI,eAAe;QAEnB,MAAM,eAAe;YACnB,IAAI,eAAe,MAAM,MAAM,EAAE;gBAC/B,MAAM,eAAe,MAAM,KAAK,CAAC,GAAG,eAAe,GAAG,IAAI,CAAC;gBAE3D,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE;4BAAc,UAAU;wBAAK,IACvC;gBAGN;gBACA,iBAAiB,OAAO,GAAG,WAAW,cAAc,MAAM,KAAK,MAAM,KAAK;YAC5E,OAAO;gBACL,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC3B,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;4BAAE,GAAG,GAAG;4BAAE,UAAU;4BAAO,cAAc,QAAQ,OAAO;wBAAC,IACzD;gBAGN,0BAA0B;gBAC1B,IAAI,iBAAiB,QAAQ,IAAI,KAAK,aAAa;oBACjD,WAAW;wBACT,cAAc,QAAQ,OAAO,EAAE,QAAQ,QAAQ,IAAI;oBACrD,GAAG;gBACL;YACF;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,0BAA0B;IAC1B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,gBAAgB;QAChB,2BAA2B;QAC3B,WAAW;YACT,YAAY;QACd,GAAG;IACL,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,iBAAiB,IAAM;IACzB,GAAG,EAAE;IAEL,MAAM,cAAc,OAAO;QACzB,MAAM,aAAa,eAAe,aAAa,IAAI;QACnD,IAAI,CAAC,cAAc,WAAW;QAE9B,MAAM,cAAuB;YAC3B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,wBAAwB;QACxB,MAAM,gBAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,MAAM;YACN,SAAS,aAAa,OAAO,0BAA0B;YACvD,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV;QACF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACpB,QAAQ;oBACR;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,kBAAkB,KAAK,IAAI,CAAC,OAAO;gBAEvC,4DAA4D;gBAC5D,IAAI,aAAa,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,UAAU;oBAC3D,6CAA6C;oBAC7C,MAAM,eAA0C;wBAC9C,gBAAgB;wBAChB,uBAAuB;wBACvB,qBAAqB;wBACrB,yBAAyB;wBACzB,yBAAyB;wBACzB,qBAAqB;oBACvB;oBAEA,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG;wBAC5C,kBAAkB,gBAAgB,OAAO,CAAC,IAAI,OAAO,IAAI,OAAO;oBAClE;gBACF;gBAEA,MAAM,mBAA4B;oBAChC,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;oBACb,MAAM;oBACN,SAAS;oBACT,WAAW,KAAK,IAAI,CAAC,SAAS;oBAC9B,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC;oBACA,UAAU;oBACV,cAAc;gBAChB;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;gBAE/C,sBAAsB;gBACtB,WAAW;oBACT,YAAY;gBACd,GAAG;YAEL,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;YAElE,MAAM,eAAwB;gBAC5B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;gBACb,MAAM;gBACN,SAAS,aAAa,OAClB,+JACA;gBACJ,WAAW,IAAI,OAAO,WAAW;gBACjC;YACF;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC3C,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,6OAAA,CAAA,0BAAuB;gDAAC,WAAU;;;;;;4CAClC,aAAa,OAAO,kBAAkB;;;;;;;kDAEzC,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,wDACA;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDACb,aAAa,OAAO,YAAY;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC,0IAAA,CAAA,UAAS;oBACR,cAAc;oBACd,iBAAiB;oBACjB,aAAa;oBACb,gBAAgB;oBAChB,UAAU;oBACV,aAAa;;;;;;8BAIf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6OAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;sDAAO;;;;;;wCAAmB;;;;;;;;;;;;;;;;;;;;;;;8BAQnC,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,gBAAgB,oBAAoB,CAAC;;oCAGjG,QAAQ,IAAI,KAAK,6BAChB,8OAAC;wCAAI,WAAU;kDAAuI;;;;;;kDAKxJ,8OAAC;wCACC,WAAW,CAAC,qDAAqD,EAC/D,QAAQ,IAAI,KAAK,SACb,0EACA,+DACJ;;4CAED,QAAQ,WAAW,kBAClB,8OAAC;gDAAI,WAAW,CAAC,4BAA4B,EAC3C,QAAQ,WAAW,CAAC,SAAS,KAAK,aAC9B,kDACA,QAAQ,WAAW,CAAC,SAAS,KAAK,SAClC,2DACA,0DACJ;;oDAAE;oDACE,aAAa,OAAO,0BAA0B;oDAAwB;oDAAI,QAAQ,WAAW,CAAC,SAAS;oDAAC;;;;;;;0DAIhH,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,iBACf,8OAAC;;sEACC,8OAAC;sEAAM,QAAQ,YAAY,IAAI;;;;;;wDAC9B,QAAQ,YAAY,KAAK,QAAQ,OAAO,kBACvC,8OAAC;4DAAK,WAAU;;;;;;;;;;;2DAIpB,QAAQ,OAAO;;;;;;0DAInB,8OAAC;gDAAI,WAAW,CAAC,+CAA+C,EAC9D,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;;kEACA,8OAAC;kEAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;oDACpD,QAAQ,QAAQ,kBACf,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ,KAAK,OAAO,UAAU;;;;;;;;;;;;;;;;;;oCAO9C,QAAQ,IAAI,KAAK,wBAChB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;+BAzDnB,QAAQ,EAAE;;;;;wBA8DlB,2BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;sCAKzG,8OAAC;4BAAI,KAAK;;;;;;;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,YAAY;4CACZ,aAAa,aAAa,OACtB,4EACA;4CAEJ,WAAU;4CACV,MAAM;4CACN,UAAU,aAAa;;;;;;wCAExB,6BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;gDACd,aAAa,OAAO,6BAA6B;;;;;;;;;;;;;8CAIxD,8OAAC;oCACC,SAAS,IAAM;oCACf,UAAU,CAAC,aAAa,IAAI,MAAM,aAAa;oCAC/C,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,iOAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY,aAAa,OAAO,4BAA4B;oCAC3E,WAAU;oCACV,UAAU;8CAET,aAAa,OAAO,YAAY;;;;;;8CAEnC,8OAAC;oCACC,SAAS,IAAM,YAAY,aAAa,OAAO,2BAA2B;oCAC1E,WAAU;oCACV,UAAU;8CAET,aAAa,OAAO,aAAa;;;;;;8CAEpC,8OAAC;oCACC,SAAS,IAAM,YAAY,aAAa,OAAO,qCAAqC;oCACpF,WAAU;oCACV,UAAU;8CAET,aAAa,OAAO,mBAAmB;;;;;;8CAE1C,8OAAC;oCACC,SAAS,IAAM,YAAY,aAAa,OAAO,qBAAqB;oCACpE,WAAU;oCACV,UAAU;8CAET,aAAa,OAAO,YAAY;;;;;;;;;;;;sCAIrC,8OAAC;4BAAE,WAAU;sCACV,aAAa,OACV,sGACA;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}]}