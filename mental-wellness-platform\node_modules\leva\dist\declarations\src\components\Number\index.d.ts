declare const sanitizeStep: (v: number, { step, initialValue }: Pick<import("./number-types").InternalNumberSettings, "step" | "initialValue">) => number;
export * from './Number';
export * from './StyledNumber';
export * from './StyledRange';
export { sanitizeStep };
declare const _default: import("../../types").InternalPlugin<import("./number-types").NumberInput, string | number, unknown, {
    type?: import("../..").LevaInputs | undefined;
    step: number;
    initialValue: number;
    pad: number;
    min: number;
    max: number;
    suffix: string | undefined;
}>;
export default _default;
